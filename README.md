# Stackd CLI

Requires Desktop and Extension

### Install

1. `npm install`
2. Start `node index`

### Usage

- To start `node index`, make sure you have Stackd Server running already.
- Proxies, profiles, and tasks CRUD is in the extension
- To exit any part hit escape, when tasks are running hit esc and tasks will stop.
- To exit app hit control-c once the tasks are stopped.

### Sitelist

- Bath & Body Works
- Big Cartel
- <PERSON>
- <PERSON>
- Game Nerdz
- Limited Run (unmaintained)
- Metallica
- Shopify (Works but marked as high risk of fraud -_-)
- Store Envy (incomplete)
- Squarespace

### Todo

- Worker threads
- Error handling & clean up
- /orders for Bath, GN, SS, Chuck (scrapers need to also be created in server)

#### Shopify
- ~~Shopify high fraud fix~~
- Password protection
- Coupon support
- Paypal support
- Full task retry
- Monitor picks up unavailable items