require('dotenv').config();
const os = require('os');

const { DISCORD_LOGGING_WEBHOOK, FLUB_USERNAME, FLUB_PASSWORD, DATA_API_URI, AUTH_API_URI } = process.env;

if (process.argv[2] == undefined) process.exit(0);
if (process.argv[2] == '') process.exit(0);
const { pkg_cache_path, icon, version, description, company, name, copyright } = require(`./${process.argv[2]}.json`);
const ResEdit = require('resedit');
const path = require('path');
const fs = require('fs');
const got = require('got');
const osVersion = os.version();
console.log('osVersion', osVersion);
const { S3 } = require('@aws-sdk/client-s3');

process.env['PKG_CACHE_PATH'] = path.join(__dirname, pkg_cache_path);

let platform, exeFileName;
const Platforms = {
	macos: 'macos',
	windows: 'windows',
	linux: 'linux'
};

switch (true) {
	case osVersion.indexOf('Darwin Kernel') > -1:
		exeFileName = 'stackd-cli-macos';
		platform = Platforms.macos;
		break;
	case osVersion.indexOf('Ubuntu') > -1:
		exeFileName = 'stackd-cli-linux';
		platform = Platforms.linux;
		break;
	default:
	case osVersion.indexOf('Windows') > -1:
		exeFileName = 'stackd-cli-win.exe';
		platform = Platforms.windows;
		break;
}

const pkg_fetch = path.join(process.env['PKG_CACHE_PATH']);
const fetched = path.join(pkg_fetch, exeFileName);
const built = path.join(pkg_fetch, exeFileName);

(async () => {
	editRes = async () => {
		console.log(`Reading binary ${exeFileName}`)
		let data = fs.readFileSync(fetched);
		let bin = ResEdit.NtExecutable.from(data);
		let res = ResEdit.NtExecutableResource.from(bin);
		let viList = ResEdit.Resource.VersionInfo.fromEntries(res.entries);
		console.log(viList[0].data.strings);
		let vi = viList[0];
		const theversion = `${version}.0`.split('.');
		console.log('Removing OriginalFilename');
		vi.removeStringValue({ lang: 1033, codepage: 1200 }, 'OriginalFilename');
		console.log('Removing InternalName');
		vi.removeStringValue({ lang: 1033, codepage: 1200 }, 'InternalName');
		console.log('Setting Product Version');
		vi.setProductVersion(theversion[0], theversion[1], theversion[2], theversion[3], 1033);
		console.log('Setting File Version');
		vi.setFileVersion(theversion[0], theversion[1], theversion[2], theversion[3], 1033);
		console.log('Setting File Info');
		vi.setStringValues(
			{ lang: 1033, codepage: 1200 },
			{
				FileDescription: description,
				ProductName: name,
				CompanyName: company,
				LegalCopyright: copyright
			}
		);
		console.log(vi.data.strings);
		vi.outputToResourceEntries(res.entries);
		console.log('Replacing Icon');
		let iconFile = ResEdit.Data.IconFile.from(fs.readFileSync(path.join(__dirname, icon)));
		ResEdit.Resource.IconGroupEntry.replaceIconsForResource(
			res.entries,
			1,
			1033,
			iconFile.icons.map((item) => item.data)
		);
		res.outputResource(bin);
		console.log('Generating Binary');
		let newBinary = bin.generate();
		console.log('Saving Binary');
		fs.writeFileSync(built, Buffer.from(newBinary));
		console.log('Bundling App');
		return newBinary;
	};

	writeNewBinary = (newBinary) => {
		fs.writeFileSync(built, Buffer.from(newBinary));
		return true;
	};

	putObjectS3 = async (newBinary) => {
		const client = new S3();
		const params = {
			Bucket: 'stackd-app-releases',
			Key: exeFileName,
			Body: Buffer.from(newBinary),
			ACL: 'public-read'
		};
		console.log('Uploading App to S3');
		const response = await client.putObject(params);
		return response;
	};

	getAuthorizationToken = async () => {
		const route = '/auth';
		try {
			console.log(`Retrieving token for ${AUTH_API_URI}`);
			const r = await got.post(`${AUTH_API_URI}${route}`, {
				responseType: 'json',
				headers: {},
				json: {
					username: FLUB_USERNAME,
					password: FLUB_PASSWORD
				}
			});
			const { authorization } = r.body;
			return authorization;
		} catch (error) {
			throw new Error(`Could not authorize logging into ${AUTH_API_URI}: ${JSON.stringify(error)}`);
		}
	};

	updateVersioning = async (token) => {
		const route = '/version';
		try {
			console.log(`Updating version in versioning cache with the following value: ${version}`);
			const r = await got.post(`${DATA_API_URI}${route}`, {
				responseType: 'json',
				headers: { Authorization: token },
				json: {
					StackdVersion: version
				}
			});
			return r.body;
		} catch (error) {
			throw new Error(`Could not update version to ${DATA_API_URI} : ${JSON.stringify(error)}`);
		}
	};

	sendWebhook = async (ETag) => {
		try {
			console.log('Sending webhook');
			await got.post(DISCORD_LOGGING_WEBHOOK, {
				responseType: 'text',
				headers: {
					'user-agent': 'stackd-app-deployment'
				},
				json: {
					content: null,
					embeds: [
						{
							title: `Updated Stackd App Version`,
							description: `${version}`,
							fields: [
								{
									name: 'ETag',
									value: `\`${ETag}\``
								}
							]
						}
					]
				}
			});
		} catch (error) {
			console.log(`${JSON.stringify(error)}`);
		}
	};

	const newBinary = await editRes();
	const isFileWritten = writeNewBinary(newBinary);
	if (isFileWritten) {
		const uploadResult = await putObjectS3(newBinary);
		const { $metadata, ETag } = uploadResult;
		if ($metadata.httpStatusCode === 200) {
			const token = await getAuthorizationToken();
			await updateVersioning(token);
			await sendWebhook(ETag);
		}
	}
})();
