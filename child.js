// child.js

const { Pi<PERSON><PERSON><PERSON> } = require('piscina');
const { MessageChannel } = require('worker_threads');

// feather client
const feathers = require('@feathersjs/feathers');
const socketio = require('@feathersjs/socketio-client');
const io = require('socket.io-client');

const socket = io('http://localhost:3030');
const app = feathers();
app.configure(socketio(socket));

const got = require('got');

const runChild = async function (runningTasks) {
	const piscina = new Piscina({ concurrentTasksPerWorker: 10 });
	const taskGroup = JSON.parse(runningTasks);
	let SHOULD_CHILD_EXIT = false;

	process.on('SIGINT', function () {
		if (SHOULD_CHILD_EXIT) process.exit();
	});

	await Promise.all(
		taskGroup.map(async (task) => {
			if (!task.enabled) {
				const channel = new MessageChannel();

				// patched questionsolver listener
				await app.service('questionsolver').on('patched', async (data) => {
					if (taskGroup && taskGroup.length > 0 && taskGroup)
						taskGroup.forEach((task) => {
							if (
								task.group === data.taskGroupName &&
								task.hasOwnProperty('questionSolverEventEmitter') &&
								task.questionSolverEventEmitter !== undefined
							) {
								channel.port2.postMessage({ action: 'questionAnswered', data: data });
							}
						});
					if (runningTaskGroupQuestionSolver.hasOwnProperty(`${data.taskGroupName}`))
						runningTaskGroupQuestionSolver[data.taskGroupName] = data;
				});

				// patched captcha listener
				await app.service('captcha').on('patched', async (data) => {
					let group = data.id.split('-');
					group.pop();
					const currentGroup = taskGroup;
					if (currentGroup) {
						const found = currentGroup.find((e) => e.name === data.id);
						if (found) {
							channel.port2.postMessage({ action: 'captchaSolved', data: data });
						}
					}
				});

				let body = [];
				process.on('message', async (message) => {
					const { action, data } = message;
					switch (action) {
						case 'QUESTION_REQUIRED_SET':
							let { runningTaskGroupQuestionSolver } = data;
							if (
								body &&
								body.find((f) => f.taskGroupName === taskGroup[0].group) === undefined &&
								!runningTaskGroupQuestionSolver.hasOwnProperty(taskGroup[0].group) &&
								runningTaskGroupQuestionSolver[taskGroup[0].group] === undefined
							) {
								Object.defineProperty(runningTaskGroupQuestionSolver, taskGroup[0].group, { value: true });
								await app.service('questionsolver').create(data);
							} else {
								let answer = body.find((f) => f.taskGroupName === taskGroup[0].group && f.answered);
								if (answer && task.hasOwnProperty('questionSolverEventEmitter') && task.questionSolverEventEmitter)
									channel.port2.postMessage({ action: 'questionAnswered', data: answer });
							}
							break;
						case 'CHILD_SHOULD_EXIT':
							SHOULD_CHILD_EXIT = true;
							break;
						default:
							break;
					}
				});

				let runningTaskGroupQuestionSolver = {};
				// used mainly to relay message back to parent
				channel.port2.on('message', async (message) => {
					const { action, data } = message;
					switch (action) {
						case 'CAPTCHA_REQUIRED':
							process.send('CAPTCHA_REQUIRED');
							break;
						case 'CAPTCHA_CLEARED':
							process.send('CAPTCHA_ANSWERED');
							break;
						case 'QUESTION_REQUIRED':
							const taskGroupQuestionResponse = await got.get(
								`http://localhost:3030/questionsolver?taskGroupName=${data.taskGroupName}`,
								{ responseType: 'json' }
							);
							body = taskGroupQuestionResponse.body;
							process.send({ action: 'GET_RUNNING_TASKGROUP_QUESTION_SOLVER', data: data });
							process.send('QUESTION_REQUIRED');
							break;
						case 'QUESTION_ANSWERED':
							process.send('QUESTION_ANSWERED');
							break;
						default:
							break;
					}
				});

				await piscina.run(
					{ ...task, port: channel.port1 },
					{
						filename: task.fullPath,
						name: 'flow',
						transferList: [channel.port1]
					}
				);
			}
		})
	);
};

runChild(process.argv[2], process.argv[3]);
