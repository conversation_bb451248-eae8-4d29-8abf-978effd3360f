// Checkout and cart api

const got = require('got')

module.exports = class Checkout  {
    constructor(group, name, link, inputType, pInput, oInput, randomOos, quantity, profile, proxyList, platform, mode) {
        this.group = group;
		this.name = name;
		this.link = link;
		this.inputType = inputType;
		this.pInput = pInput;
		this.oInput = oInput;
		this.randomOos = randomOos;
		this.quantity = quantity;
		this.profile = profile;
		this.proxyList = proxyList;
		this.platform = platform;
		this.mode = mode;
    }

    async cGen() {
        if (this.enabled) {
            try {
                const options = {
                    ... await this.mGetOptions('api:post'),
                    url: `https://api.bigcartel.com/store/${this.data.store_id}/carts`,
                    json: {}
                }
                const response = await got(options)
                await this.log(`${response.statusCode} - Generated checkout ${response.body.customer_checkout_url}`)
                return {
                    checkout: response.body.customer_checkout_url,
                    api: response.body.url,
                }
            } catch (error) {
                if (error.response) {
                    if (error.response.statusCode === 403 || error.response.statusCode === 429) { //Password is up
                        await this.log(`${error.response.statusCode} - Password up or rate limit. Retrying in 1000 ms`)
                        await this.delay(1000)
                        return await this.cGen()
                    } else {
                        await this.log(`${error.response.statusCode} - Error generating checkout: ${error.stack}`, 'error')
                        this.enabled = false
                    }
                } else {
                    await this.log(`000 - Error generating checkout ${error.message}`, 'error')
                    this.enabled = false
                }
            }
        }
    };

    async cStripe () {
        if (this.enabled) {
            try {
                const options = {
                    ... await this.mGetOptions('api:patch'),
                    url: this.data.api_url,
                    json: { stripe_payment_method_id: this.data.payment_token }
                }
                const response = await got(options)
                await this.log(`${response.statusCode} - Sent payment token`)
                return response.statusCode === 200;
            } catch (error) {
                await this.log(`${error.response ? error.response.statusCode : '000'} - Error sending payment token: ${error.stack}`, 'error')
                this.enabled = false
            }
        }
    };

    // Add product(s) to checkout session
    async cAtc (option, quantity) {
        if (this.enabled) {
            try {
                const body = {
                    "buyer_email": this.profile.shippingAddress.email,
                    "buyer_first_name": this.profile.shippingAddress.name.split(' ')[0],
                    "buyer_last_name": this.profile.shippingAddress.name.split(' ')[1],
                    "buyer_opted_in_to_marketing": false,
                    "buyer_phone_number": this.profile.shippingAddress.phone,
                    "shipping_address_1": this.profile.shippingAddress.line1,
                    "shipping_address_2": this.profile.shippingAddress.line2,
                    "shipping_city": this.profile.shippingAddress.city,
                    "shipping_country_autofill_name": "",
                    "shipping_country_id": "43",
                    "shipping_state": this.profile.shippingAddress.state,
                    "shipping_zip": this.profile.shippingAddress.postCode,
                    "items": []
                }
                // Multiple options 
                if (typeof option === 'number') {
                    body.items.push({
                        "product_option_id": option,
                        "quantity": quantity,
                    })
                } else if (typeof option === 'string') {
                    if (option.includes(',')) {
                        const opts = option.split(',')
                        opts.forEach(opt => {
                            body.items.push({
                                "product_option_id": opt.trim(),
                                "quantity": quantity,
                            })
                        })
                    } else {
                        body.items.push({
                            "product_option_id": option,
                            "quantity": quantity,
                        })
                    }
                }

                // Request
                const response = await got({
                    ... await this.mGetOptions('api:patch'),
                    url: this.data.api_url,
                    json: body
                })

                await this.log(`${response.statusCode} - Added ${response.body.items[0].full_name} ($${response.body.total})`)
                return {
                    item: response.body.items[0].full_name,
                    price: response.body.total,
                    image: response.body.items[0].primary_image
                }
            } catch (error) {
                if (error.response) {
                    if (error.response.statusCode === 422) {
                        await this.log(`${error.response.statusCode} - OID is OOS, retrying in 1000 ms.`, 'error')
                        await this.delay(1000)
                        return await this.cAtc(option, quantity)
                    } else {
                        await this.log(`${error.response.statusCode} - Store is not setup for payments, retrying in 1000ms.`, 'error')
                        await this.delay(1000)
                        return await this.cAtc(option, quantity)
                    }
                } else {
                    await this.log(`000 - Error adding to cart ${error.message}`, 'error')
                    this.enabled = false
                }
            }
        }
    };

    async cComplete(paypal = false) {
        if (this.enabled) {
            try {
                await this.log('000 - Submitting checkout')
                let body = paypal ? {
                    "cart_token": this.data.order_id,
                    "paypal_order_id": this.data.paypalToken
                } : {
                    cart_token: this.data.order_id,
                    stripe_payment_intent_id: null,
                    stripe_payment_method_id: this.data.payment_token,
                }

                const response = await got({
                    ... await this.mGetOptions('api:post'),
                    url: `https://api.bigcartel.com/store/${this.data.store_id}/checkouts`,
                    json: body
                })
                await this.log(`${response.statusCode} - Submitted checkout`)
                if (response.statusCode >= 200) {
                    return response.body.location
                }
            } catch (error) {
                const data = error.response ? JSON.stringify(error.response.body) : error.message
                await this.log(`${error.response ? error.response.statusCode : '000'} - Error submitting checkout, retrying in 2500 ms. ${data}`, 'error')
                await this.delay(2500)
                return await this.cComplete(paypal)
            }
        }
    };

    async cCheckOrder () {
        if (this.enabled) {
            try {
                const response = await got({
                    ...await this.mGetOptions('api:get'),
                    url: this.data.submit_url,
                })

                if (response.body.status === 'failure') { // fail
                    await this.log(`000 - If you do not know why your card declined, please show open a ticket and send this: ${JSON.stringify(response.body)}`, 'prompt')
                    if (Object.values(response.body.errors)[0][0].includes('Your card was declined') || Object.values(response.body.errors)[0][0].includes('your payment method and try again')) {
                        return 'decline'
                    } else if (Object.values(response.body.errors)[0][0].includes('no longer available') || Object.values(response.body.errors)[0][0].includes('has sold out')) {
                        return 'oos'
                    } else {
                        await this.log(`${response.statusCode} - Unknown error, restarting task.`, 'error')
                        return 'oos'
                    }
                } else if (response.body.status === 'processing' || response.body.status === 'pending') { // processing
                    await this.delay(1000)
                    return await this.cCheckOrder()
                } else if (response.body.status === 'success') { // Success
                    return 'success'
                } else {
                    throw new Error(`Unknown status: ${response.body.status}`)
                }
            } catch (error) {
                const data = error.response ? JSON.stringify(error.response.body) : error.message
                await this.log(`${error.response ? error.response.statusCode : '000'} - Error checking order, check monitor for checkout to check ${data}`, 'error')
                this.enabled = false
            }
        }
    };

    // Generate Paypal token
    async cCreatePaypal () {
        if (this.enabled) {
            try {
                const options = {
                    ... await this.mGetOptions('api:post'),
                    url: `${this.data.api_url}/paypal/orders`,
                    json: {}
                }
                const response = await got(options);
                await this.log(`${response.statusCode} - ${response.statusCode === 201 ? 'Created Paypal token' : 'Paypal token creation unknown'}`)
                return response.body.paypal_order_id
            } catch(error) {
                await this.log(`${error.response ? error.response.statusCode : '000'} - Error creating paypal token: ${error.stack}`, 'error')
                this.enabled = false
            }
        }
    };

    // Get stripe key (Sadly having to go to the checkout url seems to be the only way)
    async cGetStripeKey () {
        if (this.enabled) {
            try {
                const response = await got({
                    ... await this.mGetOptions('checkout'),
                    url: this.data.checkout_url,
                    // responseType: 'text',
                    // headers: {
                    //     'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8', 
                    //     'Accept-Language': 'en-US,en;q=0.5', 
                    //     'DNT': '1', 
                    //     'Connection': 'keep-alive', 
                    //     'Upgrade-Insecure-Requests': '1', 
                    //     'Sec-Fetch-Dest': 'document', 
                    //     'Sec-Fetch-Mode': 'navigate', 
                    //     'Sec-Fetch-Site': 'none', 
                    //     'Sec-Fetch-User': '?1', 
                    //     'Cache-Control': 'max-age=0', 
                    //     'TE': 'trailers', 
                    //     // 'If-None-Match': 'W/"e00cba0b76eeeb55fc386b06837a3153"'
                    //     "User-Agent": this.ua,
                    // },
                    // cookieJar: this.cookieJar,
                    // agent: this.proxy
                })
                return response.body.includes("stripePublishableKey") ? response.body.split('stripePublishableKey')[1].split('"')[1] : false
            } catch (error) {
                await this.log(`${error.response ? error.response.statusCode : '000'} - Error getting Stripe key`, 'error')
                if (error.response) {
                    if (error.response.statusCode === 403 || error.response.statusCode === 429 || error.response.statusCode === 429) {
                        this.config.agent = await this.proxyGot()
                    }
                    await this.delay(1000)
                    return await this.cGetStripeKey()
                } else {
                    //console.log(err)
                    this.enabled = false
                }
            }
        }
    };

    // Get store id
    async cGetStoreId() {
        if (this.enabled) {
            try {
                await this.log('000 - Getting store id')
                const response = await got({
                    responseType: 'text',
                    url: this.link,
                    method: 'GET',
                    headers: {
                        "User-Agent": this.config.userAgent,
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8', 
                        'Accept-Language': 'en-US,en;q=0.5', 
                        'DNT': '1', 
                        'Connection': 'keep-alive', 
                        'Upgrade-Insecure-Requests': '1', 
                        'Sec-Fetch-Dest': 'document', 
                        'Sec-Fetch-Mode': 'navigate', 
                        'Sec-Fetch-Site': 'none', 
                        'Sec-Fetch-User': '?1', 
                        'Cache-Control': 'max-age=0', 
                        'TE': 'trailers', 
                    },
                    cookieJar: this.config.cookieJar,
                    agent: this.config.agent
                })
                return response.body.split('_trackVisit')[1].split("'")[2]
            } catch (error) {
                await this.log(`${error.response ? error.response.statusCpde : '000'} - Error getting store ID ${error.response ? '' : error.message}`, 'error')
                if (error.response) {
                    if (error.response.statusCode === 429 || error.response.statusCode === 408 || error.response.statusCode === 429 || error.response.statusCode === 503) {
                        this.config.agent = await this.proxyGot()
                    }
                    return await this.cGetStoreId()
                } else {
                    this.enabled = false
                }
            }
        }
    };
}