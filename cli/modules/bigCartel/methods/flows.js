// Shared flows
module.exports = class Flows {
    constructor(group, name, link, inputType, pInput, oInput, randomOos, quantity, profile, proxyList, platform, mode) {
        this.group = group;
		this.name = name;
		this.link = link;
		this.inputType = inputType;
		this.pInput = pInput;
		this.oInput = oInput;
		this.randomOos = randomOos;
		this.quantity = quantity;
		this.profile = profile;
		this.proxyList = proxyList;
		this.platform = platform;
		this.mode = mode;
    }

    // Restock (re atc flow)
    async flowRestock() {
        if (this.enabled) {
            try {
                // For reloading stripe to make sure it's as fast as possible
                if (this.data.stripe && !this.data.stripe_preloaded) {
                    this.data.payment_token = await this.sGenToken()
                    this.data.stripe_preloaded = await this.cStripe();
                }

                await this.log(`000 - Adding ${this.data.item.OID} to cart`)
                this.data.cart = await this.cAtc(this.data.item.OID, this.quantity)

                if (this.enabled) {
                    await this.feed({
                        title: this.data.cart.item,
                        link: `${this.data.checkout_url}/summary`,
                        price: parseFloat(this.data.cart.price).toFixed(2),
                        image: this.data.cart.image !== null ? this.data.cart.image.url : 'https://cdn.discordapp.com/attachments/714346599735754762/843321954688565278/blue.png',
                        variants: [`${this.data.checkout_url}/summary`]
                    })
                    this.data.orderCheckIndex = 0
                    if (this.data.stripe) {
                        await this.flowCharge()
                    } else {
                        await this.log('000 - Paypal checkout starting')
                        await this.flowPaypal()
                    }
                }
            } catch (error) {
                await this.log(`000 - Error in restock flow ${error.message}`, 'error')
                this.enabled = false
            }
        }
    };

    // Submits stripe order then checks the order, and if its a payment decline redoes it, if its oos goes back to restock
    async flowCharge () {
        if (this.enabled && this.data.orderCheckIndex < 3) {
            try {
                // If stripe is not preloaded we need to reload before we go full on autobots roll out
                if (!this.data.stripe_preloaded && this.enabled) {
                    // Get payment token
                    this.data.payment_token= await this.sGenToken()
                    // Send payment token to bigcartel
                    this.data.stripe_preloaded = await this.cStripe();
                }

                // Submit and check status of checkout
                this.data.submit_url = await this.cComplete();
                // await console.log(this.data.submit_url)
                this.data.status = await this.cCheckOrder()

                // Decides what happens based off of what is returned from checkOrder()
                switch (this.data.status) {
                    case 'decline':
                        if (this.data.orderCheckIndex === 0) {
                            await this.log(`000 - Card decline, check monitor`, 'prompt')
                        } else {
                            await this.log('000 - Card declined', 'error')
                        }

                        await this.data.orderCheckIndex++
                        this.data.stripe_preloaded = false
                        await this.delay(1000)
                        return await this.flowCharge()
                        break;
                    case 'oos':
                        await this.log('Item went OOS while in checkout, re-adding item now.')
                        this.data.orderCheckIndex = 0
                        this.data.stripe_preloaded = false
                        this.data.cart = false
                        return await this.flowRestock()
                        break;
                    case 'success': 
                        await this.log('Check email', 'success')
                        await this.staffLog(this.link, this.data.cart.item, parseFloat(this.data.cart.price).toFixed(2), this.data.cart.image !== null ? this.data.cart.image.url : 'https://cdn.discordapp.com/attachments/714346599735754762/843321954688565278/blue.png')
                        await this.userWebhook(this.data.checkout_url, this.data.cart.item, parseFloat(this.data.cart.price).toFixed(2), this.data.cart.image !== null ? this.data.cart.image.url : 'https://cdn.discordapp.com/attachments/714346599735754762/843321954688565278/blue.png')
                        
                        this.data.cart = false
                        this.data.success = true
                        await this.successAnalytics (this.data.api_url, 'Big Cartel')
                        break;
                }
            } catch (error) {
                await this.log(`000 - Error in charge flow ${error.message}`, 'error')
                this.enabled = false
            }
        } else {
            await this.log('000 - Declined 3 times, stopping task.', 'error')
        }
    };

    // Paypal checkout flow
    async flowPaypal () {
		if (this.enabled) {
			try {
				this.data.paypal = {}
				this.data.paypalToken = await this.cCreatePaypal()
				const ppToken = await this.pSubmit()
				await this.pCapture(ppToken)
				this.data.submit_url = await this.cComplete(true)
				this.data.status = await this.cCheckOrder();
				switch (this.data.status) {
					case 'decline':
						await this.log(`000 - Card decline, check monitor`, 'prompt')
						break;
					case 'oos':
						await this.log('Item went OOS while in checkout, re-adding item now.')
						this.data.orderCheckIndex = 0
						this.data.stripe_preloaded = false
						this.data.cart = false
						return await this.flowRestock()
						break;
					case 'success': 
						await this.log('Check email', 'success')
						await this.staffLog(this.link, this.data.cart.item, parseFloat(this.data.cart.price).toFixed(2), this.data.cart.image !== null ? this.data.cart.image.url : 'https://cdn.discordapp.com/attachments/714346599735754762/843321954688565278/blue.png')
						await this.userWebhook(this.data.checkout_url, this.data.cart.item, parseFloat(this.data.cart.price).toFixed(2), this.data.cart.image !== null ? this.data.cart.image.url : 'https://cdn.discordapp.com/attachments/714346599735754762/843321954688565278/blue.png')
						this.data.cart = false
						this.data.success = true
						await this.successAnalytics (this.data.api_url, 'Big Cartel')
						break;
				}
			} catch (error) {
				await this.log(`000 - Error in Paypal flow: ${error.message}`, 'error')
				this.enabled = false
			}
		}
    };
};