// Basically this all config methods

const got = require('got')
const qs = require('qs');
const fs = require('fs');
const {parse} = require('node-html-parser')
// const Front = require('./front.js')

// Misc methods (Monitors, config endpoints, account login, initial setup)
module.exports = class Misc {
    constructor(group, name, link, inputType, pInput, oInput, randomOos, quantity, profile, proxyList, platform, mode) {
        // super(group, name, link, inputType, pInput, oInput, randomOos, quantity, profile, proxyList, platform, mode)

        this.group = group;
		this.name = name;
		this.link = link;
		this.inputType = inputType;
		this.pInput = pInput;
		this.oInput = oInput;
		this.randomOos = randomOos;
		this.quantity = quantity;
		this.profile = profile;
		this.proxyList = proxyList;
		this.platform = platform;
		this.mode = mode;
    }

    async mGetOptions(type = 'api:patch') {
        try {
            let options = {}
            switch (type) {
                case 'api:post':
                case 'api:patch':
                    options = {
                        responseType: 'json',
                        method: 'POST',
                        headers: {
                            'User-Agent': this.config.userAgent,
                            'Accept': 'application/vnd.bigcartel.v1',
                            'Accept-Language': 'en-US,en;q=0.5',
                            'Accept-Encoding': 'gzip, deflate, br',
                            'Referer': 'https://checkout.bigcartel.com/',
                            'Content-Type': 'application/json',
                            // 'X-HTTP-Method-Override': 'PATCH', // For generate checkout this cannot be enabled, but it does not matter if this is used when updating info
                            'Origin': 'https://checkout.bigcartel.com',
                            'Connection': 'keep-alive',
                            'Sec-Fetch-Dest': 'empty',
                            'Sec-Fetch-Mode': 'cors',
                            'Sec-Fetch-Site': 'same-site'
                        },
                        agent: this.config.agent,
                        cookieJar: this.config.cookieJar
                    }
                    if (type === 'api:patch') {
                        options.headers['X-HTTP-Method-Override'] = 'PATCH'
                    }
                    break;
                case 'api:get':
                    options = {
                        responseType: 'json',
                        method: 'GET',
                        headers: {
                            'User-Agent': this.config.userAgent,
                            'Accept': 'application/vnd.bigcartel.v1',
                            'Accept-Language': 'en-US,en;q=0.5',
                            'Accept-Encoding': 'gzip, deflate, br',
                            'Referer': 'https://checkout.bigcartel.com/',
                            'Origin': 'https://checkout.bigcartel.com',
                            'Connection': 'keep-alive',
                            'Sec-Fetch-Dest': 'empty',
                            'Sec-Fetch-Mode': 'cors',
                            'Sec-Fetch-Site': 'same-site'
                        },
                        agent: this.config.agent,
                        cookieJar: this.config.cookieJar
                    }
                    break;
                case 'stripe':
                    options = {
                        responseType: 'json',
                        headers: { 
                            'User-Agent': this.config.userAgent, 
                            'Accept': 'application/json', 
                            'Accept-Language': 'en-US,en;q=0.5', 
                            'Referer': 'https://js.stripe.com/', 
                            'Content-Type': 'application/x-www-form-urlencoded', 
                            'Origin': 'https://js.stripe.com', 
                            'DNT': '1', 
                            'Connection': 'keep-alive', 
                            'Sec-Fetch-Dest': 'empty', 
                            'Sec-Fetch-Mode': 'cors', 
                            'Sec-Fetch-Site': 'same-site'
                        },
                        agent: this.config.agent
                    }
                    break;
                case 'paypal':
                    options = {
                        responseType: 'json',
                        agent: this.config.agent,
                        cookieJar: this.config.cookieJar,
                        method: 'POST'
                    }
                    break;
                case 'checkout':
                    options = {
                        responseType: 'text',
                        headers: {
                            'User-Agent': this.config.userAgent,
                            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8', 
                            'Accept-Language': 'en-US,en;q=0.5', 
                            'DNT': '1', 
                            'Connection': 'keep-alive', 
                            'Upgrade-Insecure-Requests': '1', 
                            'Sec-Fetch-Dest': 'document', 
                            'Sec-Fetch-Mode': 'navigate', 
                            'Sec-Fetch-Site': 'none', 
                            'Sec-Fetch-User': '?1', 
                            'Cache-Control': 'max-age=0', 
                            'TE': 'trailers', 
                        },
                        agent: this.config.agent,
                        cookieJar: this.config.cookieJar,
                        method: 'GET'
                    }
                    break;
                default:
                    throw new Error(`Type unknown: ${type}`)
            }
            return options
        } catch (error) {
            await this.log(`000 - Error configuring options: ${error.message}`, 'error')
            this.enabled = false
            return false
        }
    };

    async mErrorFlow() {
        // Not sure whether to add this because Big Cartel itself has a very simple retry, maybe Paypal will need this
    }
};