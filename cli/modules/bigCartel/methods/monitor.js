const got = require('got')

module.exports = class Monitor  {
    constructor(group, name, link, inputType, pInput, oInput, randomOos, quantity, profile, proxyList, platform, mode) {
        this.group = group;
		this.name = name;
		this.link = link;
		this.inputType = inputType;
		this.pInput = pInput;
		this.oInput = oInput;
		this.randomOos = randomOos;
		this.quantity = quantity;
		this.profile = profile;
		this.proxyList = proxyList;
		this.platform = platform;
		this.mode = mode;
    }

        // MONITOR
    
    // Figures out what monitor to use
    async monitor () {
        await this.log(`000 - Monitoring ${this.link.split('/')[2]}`)
        this.oInput = this.oInput.toLowerCase()
        let monitored
        switch (this.inputType) {
            case 'DL':
                monitored = await this.dlMonitor()
                break;
            case 'PID':
                monitored = {link: this.link, OID: this.pInput}
                break;
            case 'KW':
                this.pInput = this.pInput.map(p => p.toLowerCase())
                this.positives = this.pInput.filter(keyword => keyword[0] === '+')
                this.negatives = this.pInput.filter(keyword => keyword[0] === '-')
                monitored = await this.kwMonitor()
                break;
        }
        if (monitored) {
            return (monitored)
        }
    };

    // NEEDS TO BE CONVERTED
    async dlMonitor () {
        if (this.enabled) {
            try {
                const response = await got.get(`${this.pInput}.js`, {
                    responseType: 'json',
                    headers: {
                        "User-Agent": this.userAgent(),
                        'Cookie': '_storefront_session=;'
                    },
                    agent: await this.proxyGot('monitor')
                })
                await this.log(`${response.statusCode} - Monitor health: ${response.headers['x-cache'] === 'MISS' ? 'good' : 'bad'}`)
                const product = response.body
                switch (this.oInput) {
                    case '':
                        const availOptions = product.options.filter(opt => !opt.sold_out)
                        if (availOptions.length > 0) {
                            const botOpt = availOptions[Math.floor(Math.random() * availOptions.length)]
                            return {
                                title: `${product.name} - ${botOpt.name}`,
                                OID: botOpt.id,
                            }
                        }
                        break;
                    default:
                        const filtOptions = product.options.filter(opt => opt.name.toLowerCase().includes(this.oInput))
                        const filtOptionsAvail = filtOptions.filter(opt => !opt.sold_out)
                        if (filtOptionsAvail.length > 0) {
                            return {
                                title: `${product.name} - ${filtOptionsAvail[0].name}`,
                                OID: filtOptionsAvail[0].id,
                            }
                        } else if (filtOptionsAvail.length === 0 && this.randomOos) { //options oos, will try any of the options
                            const optionsAvail = product.options.filter(opt => !opt.sold_out)
                            if (optionsAvail.length > 0) {
                                return {
                                    title: `${product.name} - ${optionsAvail[0].name}`,
                                    OID: optionsAvail[0].id,
                                }
                            }
                        }
                }
            } catch (err) {
                if (err.response) {
                    if (err.response.status === 503) {
                        await this.log(`${err.response.status} - Password up`, 'error')
                    } else {
                        await this.log(`${err.response.status} - Unknown error with DL monitor request: ${err.message}`, 'error')
                    }
                } else {
                    await this.log('000 - DL monitor error or product link is invalid', 'error')
                }
            }
            await new Promise(r => setTimeout(r, this.proxy ? 200 : 500))
            const remonitor = await this.dlMonitor()
            return remonitor
        }
    };

    async kwMonitor () {
        if (this.enabled) {
            try {
                // Getting products json
                const response = await got.get(`${this.link}/products.js`, {
                    responseType: 'json',
                    headers: {
                        "User-Agent": this.userAgent(),
                        'Cookie': '_storefront_session=;'
                    },
                    agent: await this.proxyGot('monitor')
                })
                await this.log(`${response.statusCode} - Monitor health: ${response.headers['x-cache'] === 'MISS' ? 'good' : 'bad'}`)
                const products = response.body
                loop1:
                for (let i = 0; i < products.length; i++) {
                    const product = products[i]
                    // Seeing if product.name matches keywords
                    product.name = product.name.replace(/[\-]/gi, ' ').toLowerCase().replace(/[^\w\s]/gi, '').split(' ')
                    const pMatches = this.positives.filter(keyword => product.name.includes(keyword.slice(1, keyword.length)))
                    const nMatches = this.negatives.filter(keyword => product.name.includes(keyword.slice(1, keyword.length)))
                    if (pMatches.length === this.positives.length && nMatches.length === 0) {
                        product.name = product.name.join(' ')
                        switch (this.oInput) {
                            case '':
                                const availOptions = product.options.filter(opt => !opt.sold_out)
                                if (availOptions.length > 0) {
                                    const botOpt = availOptions[Math.floor(Math.random() * availOptions.length)]
                                    return {
                                        title: `${product.name} - ${botOpt.name}`,
                                        OID: botOpt.id,         
                                    }
                                    break loop1
                                }
                                break;
                            default:
                                const filtOptions = product.options.filter(opt => opt.name.toLowerCase().includes(this.oInput))
                                const filtOptionsAvail = filtOptions.filter(opt => !opt.sold_out)
    
                                if (filtOptionsAvail.length > 0) {
                                    return {
                                        title: `${product.name} - ${filtOptionsAvail[0].name}`,
                                        OID: filtOptionsAvail[0].id,
                                    }
                                    break loop1
                                } else if (filtOptionsAvail.length === 0 && this.randomOos) { //options oos, will try any of the options
                                    const optionsAvail = product.options.filter(opt => !opt.sold_out)
                                    if (optionsAvail.length > 0) {
                                        return {
                                            title: `${product.name} - ${optionsAvail[0].name}`,
                                            OID: optionsAvail[0].id,
                                        }
                                        break loop1
                                    }
                                }
                        } // End of switch
                    } // End of if
                }
            } catch (err) {
                await this.log(`${err.response ? err.response.statusCode : '000'} - Error keyword monitor: ${err.message}`, 'error')
            }
            await new Promise(r => setTimeout(r, this.proxy ? 200 : 500))
            const remonitor = await this.kwMonitor()
            return remonitor
        };
    };

    // Find item to preload with
    async preloadItem () {
        if (this.enabled) {
            try {
                const response = await got.get(`${this.link}/products.js?`, {
                    responseType: 'json',
                    headers: {
                        "User-Agent": this.userAgent(),
                        'Cookie': '_storefront_session=;'
                    },
                    agent: this.proxy
                })

                const products = response.body
                let foundInStock = false
                for (let a = 0; a < products.length; a++) {
                    const product = products[a]
                    if(product.status === 'active') {
                        const options = product.options
                        for (let b = 0; b < options.length; b++) {
                            const option = options[b]
                            if (option.sold_out === false) {
                                return option.id
                            } 
                        }
                    }
                }
                
                if (!foundInStock) {
                    await this.log('000 - No products available', 'error')
                    return 'none found'
                }
            } catch (error) {
                await this.log(`${error.response ? error.response.statusCode : '000'} - Error finding item to preload ${error.response ? '' : error.message}`, 'error')
                // console.log(err)
                return 'none found'
            }
        }
    };
}