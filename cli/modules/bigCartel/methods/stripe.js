// Stripe API

const got = require('got')
const qs = require('qs');
const fs = require('fs');

// Misc methods (Monitors, config endpoints, account login, initial setup)
module.exports = class Stripe {
    constructor(group, name, link, inputType, pInput, oInput, randomOos, quantity, profile, proxyList, platform, mode) {
        this.group = group;
		this.name = name;
		this.link = link;
		this.inputType = inputType;
		this.pInput = pInput;
		this.oInput = oInput;
		this.randomOos = randomOos;
		this.quantity = quantity;
		this.profile = profile;
		this.proxyList = proxyList;
		this.platform = platform;
		this.mode = mode;
    }

    // Generate a unique stripe token for stripe payment
    async sGenToken () {
        if (this.enabled) {
            try {
                const stripeIds = await this.sGenIds()
                const body = {
                    'type': 'card',
                    'billing_details[name]': this.profile.billingAddress.name,
                    'billing_details[address][line1]': this.profile.billingAddress.line1,
                    'billing_details[address][line2]': this.profile.billingAddress.line2,
                    'billing_details[address][city]': this.profile.billingAddress.city,
                    'billing_details[address][state]': this.profile.billingAddress.state,
                    'billing_details[address][postal_code]': this.profile.billingAddress.postCode,
                    'billing_details[address][country]': 'US',
                    'card[number]': this.profile.paymentDetails.cardNumber,
                    'card[cvc]': this.profile.paymentDetails.cardCvv,
                    'card[exp_month]': this.profile.paymentDetails.cardExpMonth,
                    'card[exp_year]': this.profile.paymentDetails.cardExpYear,
                    'guid': stripeIds.guid,
                    'muid': stripeIds.muid,
                    'sid': stripeIds.sid,
                    'payment_user_agent':  this.data.stripePaymentAgent,
                    'time_on_page': Math.floor(Math.random() * (20000 - 5000 + 1) + 5000), //TODO Math.floor(Math.random() * (10000 - 5000 + 1) + 5000)
                    'key': this.data.stripe
                }
                const options = {
                    ... await this.mGetOptions('stripe'),
                    method: 'POST',
                    url: 'https://api.stripe.com/v1/payment_methods',
                    body: qs.stringify(body),
                }
                const response = await got(options)
                if (response.statusCode === 200) {
                    await this.log(`${response.statusCode} - Generated payment token`)
                    return response.body["id"]
                } else {
                    await this.log(`${response.statusCode} - Unexpected response generating Stripe token. Retrying in 2500 ms`, 'error')
                    await this.delay(2500)
                    return await this.sGenToken()
                }
            } catch (error) {
                const data = JSON.stringify(error.response ? error.response.body : error.message)
                await this.log(`${error.response ? error.response.statusCode : '000'} - Error generating Stripe token, retrying in 2500 ms. ${data}`, 'error')
                await this.delay(2500)
                return await this.sGenToken()
            }
        }
    };

    // Generate unique Stripe IDs required to generate a Stripe token
    async sGenIds () {
        if (this.enabled) {
            try {
                const response = await got({
                    ... await this.mGetOptions('stripe'),
                    url: 'https://m.stripe.com/6',
                    method: 'POST',

                })
                return response.body
            } catch (error) {
                const data = JSON.stringify(error.response ? error.response.body : error.message)
                await this.log(`${error.response ? error.response.statusCode : '000'} - Error generating Stripe IDs, retrying in 2500 ms. ${data}`, 'error')
                await this.delay(2500)
                return await this.sGenIds()
            }
        }
    };
}