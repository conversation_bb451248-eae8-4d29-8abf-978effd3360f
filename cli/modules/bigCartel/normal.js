const Checkout = require('./methods/checkout');
const Monitor = require('./methods/monitor');
const Paypal = require('./methods/paypal');
const Misc = require('./methods/misc');
const Stripe = require('./methods/stripe');
const Flows = require('./methods/flows');

// Rewrite Big Cartel Normal to now use /api and have easy plug and play methods

// METHODS KEY:
// cXXX = checkout.js, Any Big Cartel cart/checkout endpoints
// mXXX = misc.js, Error handler and options generator
// pXXX = paypal.js, Paypal methods and flow
// sXXX = stripe.js, Stripe methods
// flowXXX = flows.js Shared flows
// monitor & preloadItem = monitor.js

const Utils = require('../utils/utils');
const { Mixin } = require('ts-mixer');
const { CookieJar } = require('tough-cookie');
const { resolve } = require('path');

const BCNormal = class BCNormal extends Mixin(Utils, Checkout, Misc, Monitor, Stripe, Paypal, Flows) {

    constructor(group, name, link, inputType, pInput, oInput, randomOos, quantity, profile, proxyList, port) {
        super(group, name, link, inputType, pInput, oInput, randomOos, quantity, profile, proxyList, 'Big Cartel', 'Normal')
        this.fullPath = resolve(`${__filename}`)
        this.port = port,
        this.enabled = false
        this.data = {}
        this.config = {}
    }

    async flow () {
        try {
            // Config
            this.enabled = true
            this.config.userAgent = this.userAgent()
            this.config.cookieJar = new CookieJar();
            this.config.agent = await this.proxyGot()
            this.settings = await this.getSettings()
            this.data.stripePaymentAgent = await this.getStripePaymentAgent();

            // Starting flow
            this.data.store_id = await this.cGetStoreId()
            const getUrls = await this.cGen()
            if (this.enabled) {
                this.data.checkout_url = getUrls.checkout
                this.data.api_url = getUrls.api
                this.data.order_id = getUrls.api.split('/')[6]
                this.data.item = await this.monitor()
                await this.log(`000 - Adding ${this.data.item.OID} to cart`)
                this.data.cart = await this.cAtc(this.data.item.OID, this.quantity)
                if (this.enabled && this.data.cart) {
                    await this.feed({
                        title: this.data.cart.item,
                        link: `${this.data.checkout_url}/summary`,
                        price: parseFloat(this.data.cart.price).toFixed(2),
                        image: this.data.cart.image !== null ? this.data.cart.image.url : 'https://cdn.discordapp.com/attachments/714346599735754762/843321954688565278/blue.png',
                        variants: [`${this.data.checkout_url}/summary`]
                    })
                    this.data.stripe = await this.cGetStripeKey()
        
                    if (this.enabled && this.data.item) {
                        this.data.orderCheckIndex = 0
                        if (this.data.stripe) {
                            await this.flowCharge()
                        } else {
                            await this.log('000 - Paypal checkout starting')
                            await this.flowPaypal()
                        }
                    }
                }
            }
        } catch (error) {
            await this.log(`000 - Error in flow: ${error.message}`, 'error')
        }
    }   
}


module.exports.flow = async function flow(obj) {
    const instance = new BCNormal(obj.group, obj.name, obj.link, obj.inputType, obj.pInput, obj.oInput, obj.randomOos, obj.quantity, obj.profile, obj.proxyList, obj.port)
    await instance.flow()
}

module.exports.BCNormal = BCNormal