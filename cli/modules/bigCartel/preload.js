const Checkout = require('./methods/checkout');
const Monitor = require('./methods/monitor');
const Paypal = require('./methods/paypal');
const Misc = require('./methods/misc');
const Stripe = require('./methods/stripe');
const Flows = require('./methods/flows');

const BCNormal = require('./normal')

// METHODS KEY:
// cXXX = checkout.js, Any Big Cartel cart/checkout endpoints
// mXXX = misc.js, Error handler and options generator
// pXXX = paypal.js, Paypal methods and flow
// sXXX = stripe.js, Stripe methods
// flowXXX = flows.js Shared flows
// monitor & preloadItem = monitor.js

const Utils = require('../utils/utils');
const { Mixin } = require('ts-mixer');
const { CookieJar } = require('tough-cookie');
const { resolve } = require('path');

const BCPreload = class BCPreload extends Mixin(Utils, Checkout, Misc, Monitor, Stripe, Paypal, Flows) {

    constructor(group, name, link, inputType, pInput, oInput, randomOos, quantity, profile, proxyList, port) {
        super(group, name, link, inputType, pInput, oInput, randomOos, quantity, profile, proxyList, 'Big Cartel', 'Preload')
        this.fullPath = resolve(`${__filename}`)
        this.port = port,
        this.enabled = false
        this.data = {}
        this.config = {}
    }

    // Init and figure out what flow path is needed
    async flow () {
        try {
            this.enabled = true
            this.config.userAgent = this.userAgent()
            this.config.cookieJar = new CookieJar();
            this.config.agent = await this.proxyGot()
            this.settings = await this.getSettings()
    
            this.data.stripePaymentAgent = await this.getStripePaymentAgent();
            this.data.storeInfo = await this.getStoreInfo()

            if (this.data.storeInfo && this.data.storeInfo.hasOwnProperty('stripeLiveKey')) {
                await this.log('000 - Preload information found')
                this.data.preload = 1
                this.data.stripe = this.data.storeInfo.stripeLiveKey
                if (this.data.stripe === "") {
                    this.data.stripe = null
                }
                this.data.store_id = this.data.storeInfo.storeId
                await this.noLoadFlow()
            } else {
                await this.yesLoadFlow()
            }
        } catch (error) {
            await this.log(`000 - Error in flow: ${error.message}`, 'error')
            this.enabled = false
        }
    };

    // Has the store info to preload without preloading with an item
    async noLoadFlow() {
        if (this.enabled) {
            try {
                // if stripe is available gen it
                if (this.data.stripe !== null && this.data.stripe !== false) {
                    this.data.payment_token = await this.sGenToken()
                } 
                
                const getUrls = await this.cGen();
                if (this.enabled) {
                    this.data.checkout_url = getUrls.checkout
                    this.data.api_url = getUrls.api
                    this.data.order_id = getUrls.api.split('/')[6]
                    if (this.data.stripe !== null && this.data.stripe !== false) {
                        this.data.stripe_preloaded = await this.cStripe();
                    }
                    // Monitor
                    if (this.enabled) {
                        this.data.item = await this.monitor()
                        if (this.data.item) {
                            await this.flowRestock()
                        }
                    }
                }
            } catch (error) {
                await this.log(`000 - Error in no load flow: ${error.message}`, 'error')
                this.enabled = false
            }
        }
    };

    async yesLoadFlow() { // info was not in server
        try {
            await this.log('000 - Preloading...')
            this.data.preload = await this.preloadItem()
            this.data.store_id = await this.cGetStoreId()
            if (this.data.preload === 'none found') { // Switch to normal mode
                await this.log('000 - Switching to Normal mode', 'prompt')
                const norm =  new BCNormal(this.name,  this.link, this.inputType, this.pInput, this.oInput, this.randomOos, this.quantity, this.profile, this.proxyList)
                await norm.flow()
                await this.log('000 - Please switch to normal mode', 'prompt')
                this.enabled = false
            } else if (typeof this.data.preload === 'number') { // Continue with preload
                // Generate a checkout session
                await this.log('000 - Generating checkout')
                // const getUrls = await this.generateCheckout()
                const getUrls = await this.cGen()
                if (this.enabled) {
                    this.data.checkout_url = getUrls.checkout
                    this.data.api_url = getUrls.api
                    this.data.order_id = getUrls.api.split('/')[6]
                    this.data.cart = await this.cAtc(this.data.preload, 1)

                }

                if (this.data.cart) {
                    this.data.stripe = await this.cGetStripeKey()
                    if (this.data.stripe) {
                        if (this.data.stripe) { // Preloading stripe
                            // Get payment token
                            this.data.payment_token = await this.sGenToken();
                            // Send payment token to bigcartel
                            this.data.stripe_preloaded = await this.cStripe();
                            // do not await creating store-info on purpose
                            this.createStoreInfo({storeName: this.parsedLink.host, storeType: 'Big Cartel', storeId: this.data.store_id, stripeLiveKey: this.data.stripe})
                        }

                        // Monitor
                        if (this.enabled) {
                            this.data.item = await this.monitor()
                        }
                        if (this.data.item) {
                            await this.flowRestock()
                        }
                    } else {
                        // await this.log('here')
                        await this.createStoreInfo({storeName: this.parsedLink.host, storeType: 'Big Cartel', storeId: this.data.store_id, stripeLiveKey: ''})

                        // await this.log(`000 - Please switch to Normal mode`, 'prompt')
                        // monitor then atc
                        if (this.enabled) {
                            this.data.item = await this.monitor()
                            await this.flowRestock()
                        }
                    }
                }
            } else {
                await this.log('000 - Unknown location, stopping task.', 'error')
                this.enabled = false
            }
        } catch (error) {
            await this.log(`000 - Error in YL Flow ${error.message}`, 'error')
            this.enabled = false
        }
    };
}


module.exports.flow = async function flow(obj) {
    const instance = new BCPreload(obj.group, obj.name, obj.link, obj.inputType, obj.pInput, obj.oInput, obj.randomOos, obj.quantity, obj.profile, obj.proxyList, obj.port)
    await instance.flow()
}

module.exports.BCPreload = BCPreload