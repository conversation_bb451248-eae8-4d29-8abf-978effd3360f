const moment = require("moment");
const { parse } = require('node-html-parser')
const Utils = require('../utils/utils')
const got = require('got')
const tunnel = require('tunnel')
const {CookieJar} = require('tough-cookie')
const {promisify} = require('util')
const qs = require('qs')

module.exports = class Methods extends Utils {
    constructor(group, name, link, inputType, pInput, oInput, randomOos, quantity, profile, proxyList, platform, mode) {
        super(group, name, link, inputType, pInput, oInput, randomOos, quantity, profile, proxyList, platform, mode)
        
    }

    async generateCart () {
        if (this.enabled) {
            this.log('000 - Generating cart')
            try {
                // const response = await this._instance.get(`${this.link}/cart.json`, config)
                const response = await got.get(`${this.link}/cart.json`, {
                    responseType: 'json',
                    headers: {
                        accept: "*/*",
                        "accept-encoding": "gzip, br",
                        "accept-language": "en-US,en;q=0.9",
                        connection: "keep-alive",
                        "content-type": "application/json",
                        "user-agent": this.obj.ua
                    },
                    cookieJar: this.cookieJar,
                    agent: this.proxy
                })
                this.log(`${response.statusCode} - Generated cart`)
                return response.body
            } catch (err) {
                this.log(err.response ? `${err.response.statusCode} - Error generating cart` : 'Error generating cart', 'error')
                if (err.response) {
                    if (err.response.statusCode >= 400) {
                        await new Promise(r => setTimeout(r, 1000))
                        return await this.generateCart()
                    } else {
                        this.enabled = false
                    }
                } else {
                    this.enabled = false
                    this.flow()
                }
            }
        }
    };

    // atc
    async atc () {
        if (this.enabled) {
            try {
                const response = await got.put(`${this.link}/cart.json`, {
                    responseType: 'json',
                    json: {
                        id: this.obj.cart.id,
                        modify: "item",
                        "item": {
                            "id": `${this.obj.item.OID}`,
                            "quantity": this.quantity,
                            "discount": null
                        },
                    },
                    headers: {
                        accept: "*/*",
                        "accept-encoding": "gzip, br",
                        "accept-language": "en-US,en;q=0.9",
                        connection: "keep-alive",
                        "content-type": "application/json",
                        "user-agent": this.obj.UA
                    },
                    cookieJar: this.cookieJar,
                    agent: this.proxy
                })

                // Technically you are allowed to go on after this, but because there is nothing in the cart, you get errors at the gen checkout step
                if (response.body.item_count > 0) {
                    return response.body
                } else {
                    await new Promise(r => setTimeout(r, 1500))
                    const reAtc = await this.atc()
                    return reAtc
                }
            } catch (err) {
                this.log(`${err.response ? err.response.statusCode : '000'} - Error adding to cart`, 'error')
                if (err.response) {
                    if (err.response.statusCode >= 400) {
                        await new Promise(r => setTimeout(r, 1000))
                        return await this.atc()
                    } else {
                        this.enabled = false
                        this.flow()
                    }
                } else {
                    this.enabled = false
                    this.flow()
                }
            }
        }
    };

    // Gives phone, country, and shipping
    async shipping () {
        if (this.enabled) {
            const config = {
                withCredentials: true,
                headers: {
                    accept: "*/*",
                    "accept-encoding": "gzip, br",
                    "accept-language": "en-US,en;q=0.9",
                    connection: "keep-alive",
                    "content-type": "application/json",
                    "user-agent": this.obj.UA
                }
            }
            if (this.proxyList !== 'local') {
                config.httpsAgent = this.proxy
            }
            this.log('000 - Shipping')
            try {
                const response = await got.put(`${this.link}/cart.json`, {
                    responseType: 'json',
                    json: {
                        "id": this.obj.cart.id,
                        "modify": "shipping",
                        "shipping": {
                            "country": "39",
                            method: null
                        }
                    },
                    headers: {
                        accept: "*/*",
                        "accept-encoding": "gzip, br",
                        "accept-language": "en-US,en;q=0.9",
                        connection: "keep-alive",
                        "content-type": "application/json",
                        "user-agent": this.obj.UA
                    },
                    cookieJar: this.cookieJar,
                    agent: this.proxy

                })
                // await console.log(response.data.shipping)
                let shippingMethod = ''
                for (let i = 0; i < response.body.shipping.config.methods.length; i++) {
                    const method = response.body.shipping.config.methods[i]
                    if (!method.conflict) {
                        shippingMethod = method.id
                        break;
                    }
                }
                // await console.log(shippingMethod)
    
                const response1 = await got.put(`${this.link}/cart.json`, {
                    responseType: 'json',
                    json: {
                        "id": this.obj.cart.id,
                        "modify": "shipping",
                        "shipping": {
                            "country": "39",
                            method: `${shippingMethod}`
                        }
                    },
                    headers: {
                        accept: "*/*",
                        "accept-encoding": "gzip, br",
                        "accept-language": "en-US,en;q=0.9",
                        connection: "keep-alive",
                        "content-type": "application/json",
                        "user-agent": this.obj.UA
                    },
                    cookieJar: this.cookieJar,
                    agent: this.proxy
                })
                return response.body // I think this should be response1.body but not fosho so ill just leave it here
            } catch (err) {
                this.log(`${err.response ? err.response.statusCode : '000'} - Error submitting shipping`, 'error')
                if (err.response) {
                    if (err.response.status >= 400) {
                        await new Promise(r => setTimeout(r, 1000))
                        return await this.shipping()
                    } else {
                        this.enabled = false
                        this.flow()
                    }
                } else {
                    this.enabled = false
                    this.flow()
                }
            }
        }
    };

    async genCheckout () {
        if (this.enabled) {
            try {
                const response = await got.get(`${this.link}/cart/checkout`, {
                    responseType: 'text',
                    headers: {
                        accept: "*/*",
                        "accept-encoding": "gzip, br",
                        "accept-language": "en-US,en;q=0.9",
                        connection: "keep-alive",
                        "content-type": "application/json",
                        "user-agent": this.obj.UA
                    },
                    cookieJar: this.cookieJar,
                    agent: this.proxy
                })
                
                // comment this whole "if" out once stable
                if (!`${response.url.split('?')[0]}.json}`.includes('returned')) {
                    this.log(`${response.statusCode} - Checkout available: ${response.url.split('?')[0]}.json`)
                    if (!this.obj.stripe) {
                        this.obj.stripe = response.body.split('stripe_key: "')[1].split('"')[0]
                    }
                    return `${response.url.split('?')[0]}`
                } else if (`${response.url.split('?')[0]}.json}`.includes('returned')) {
                    this.log('Item OOS, retrying', 'error')
                    await new Promise(r => setTimeout(r, 1000))
                    const reGenCheckout = await this.genCheckout()
                    return reGenCheckout
                }

                return `${response.url.split('?')[0]}`
            } catch (err) {
                this.log(err.response ? `${err.response.statusCode} - Error getting payment token` : 'Error getting payment token', 'error')
                if (err.response) {
                    if (err.response.status >= 400) {
                        await new Promise(r => setTimeout(r, 1000))
                        const reGenCheckout = await this.submit()
                        return reGenCheckout
                    } else {
                        this.enabled = false
                        this.flow()
                    }
                } else {
                    this.enabled = false
                    this.flow()
                }
            }
        }
    };

    // Post profile info
    async postProfile () {
        if (this.enabled) {
            this.log('000 - Posting profile')
            // States have their own unique id that does not have to be sent, but the browser does it so here we are
            const states = {Alaska:1,Alabama:2,Arkansas:3,Arizona:4,California:5,Colorado:6,Connecticut:7,'District of Columbia':8,Delaware:9,Florida:10,Georgia:11,Hawaii:12,Iowa:13,Idaho:14,Illinois:15,Indiana:16,Kansas:17,Kentucky:18,Louisiana:19,Massachusetts:20,Maryland:21,Maine:22,Michigan:23,Minnesota:24,Missouri:25,Mississippi:26,Montana:27,'North Carolina':28,'North Dakota':29,Nebraska:30,'New Hampshire':31,'New Jersey':32,'New Mexico':33,Nevada:34,'New York':35,Ohio:36,Oklahoma:37,Oregon:38,Pennsylvania:39,'Rhode Island':40,'South Carolina':41,'South Dakota':42,Tennessee:43,Texas:44,Utah:45,Virginia:46,Vermont:47,Washington:48,Wisconsin:49,'West Virginia':50,Wyoming:51,'Armed Forces Americas':115,'Armed Forces Europe':116,'Armed Forces Pacific':117}
            try {
                const response = await got.post(`${this.obj.checkout_url}/prepare.json`, {
                    responseType: 'json', // TODO CHECK THIS
                    body: qs.stringify({
                        "_method": "PUT",
                        "order[name]": `${this.profile.shippingAddress.name}`,
                        "order[email]": `${this.profile.shippingAddress.email}`,
                        "order[phone]": `+1${this.profile.shippingAddress.phone}`,
                        "order[street_address_1]": `${this.profile.shippingAddress.line1}`,
                        "order[street_address_2]": `${this.profile.shippingAddress.line2}`,
                        "order[city]": `${this.profile.shippingAddress.city}`,
                        "order[state_id]": `${states[this.profile.shippingAddress.state]}`,
                        "order[state_name]": `${this.profile.shippingAddress.state}`,
                        "order[postal_code]": `${this.profile.shippingAddress.postCode}`,
                        "order[country_id]": "39",
                        "order[country_name]": "United States",
                    }),
                    headers: {
                        accept: "*/*",
                        "accept-encoding": "gzip, br",
                        "accept-language": "en-US,en;q=0.9",
                        connection: "keep-alive",
                        "content-type": 'application/x-www-form-urlencoded; charset=UTF-8',
                        "user-agent": this.obj.UA
                    },
                    cookieJar: this.cookieJar,
                    agent: this.proxy
                })
                return true
            } catch (err) {
                this.log(err.response ? `${err.response.statusCode} - Error posting profile` : '000 - Error posting profile', 'error')
                if (err.response) {
                    if (err.response.statusCode >= 400) {
                        await new Promise(r => setTimeout(r, 1000))
                        return await this.postProfile()
                    } else {
                        this.enabled = false
                        this.flow()
                    }
                } else {
                    this.enabled = false
                    this.flow()
                }
            }
        }
    };

    // Submit checkout
    async submit () {
        if (this.enabled) {
            this.log('000 - Getting payment token')
            try {
                const response = await got.post(`${this.obj.checkout_url}/intent.json`, {
                    responseType: 'json',
                    body: qs.stringify({
                        "_method": "PUT",
                        "gateway": "stripe"
                    }),
                    headers: {
                        accept: "*/*",
                        "accept-encoding": "gzip, br",
                        "accept-language": "en-US,en;q=0.9",
                        connection: "keep-alive",
                        "content-type": 'application/x-www-form-urlencoded; charset=UTF-8',
                        "user-agent": this.obj.UA
                    },
                    cookieJar: this.cookieJar,
                    agent: this.proxy
                })
                return response.body
            } catch (err) {
                this.log(err.response ? `${err.response.statusCode} - Error getting payment token` : '000 - Error getting payment token', 'error')
                if (err.response) {
                    if (err.response.statusCode >= 400) {
                        await new Promise(r => setTimeout(r, 1000))
                        return await this.submit()
                    } else {
                        this.enabled = false
                        this.flow()
                    }
                } else {
                    this.enabled = false
                    this.flow()
                }
            }
        }
    };

    // After submitting the checkout you submit the stripe
    async submitStripe () {
        if (this.enabled) {
            this.log('000 - Submitting order')
            try {
                // Get stripe ids
                const stripeIds = await got.post('https://m.stripe.com/6')
                const stripeLink = `https://api.stripe.com/v1/payment_intents/${this.obj.submit.order.intent.split('_secret_')[0]}/confirm`
                const response = await got.post(stripeLink, {
                    responseType: 'json',
                    body: qs.stringify({
                        "payment_method_data[type]": "card",
                        "payment_method_data[billing_details][name]": `${this.profile.shippingAddress.name.split(' ')[1]}`,
                        "payment_method_data[billing_details][email]": `${this.profile.shippingAddress.email}`,
                        "payment_method_data[billing_details][address][postal_code]": `${this.profile.shippingAddress.postCode}`,
                        "payment_method_data[billing_details][phone]": `+1${this.profile.shippingAddress.phone}`,
                        "payment_method_data[card][number]": `${this.profile.paymentDetails.cardNumber}`,
                        "payment_method_data[card][cvc]": `${this.profile.paymentDetails.cardCvv}`,
                        "payment_method_data[card][exp_month]": `${this.profile.paymentDetails.cardExpMonth}`,
                        "payment_method_data[card][exp_year]": `${this.profile.paymentDetails.cardExpYear.replace('20', '')}`,
                        "payment_method_data[guid]": `${stripeIds.body.guid}`,
                        "payment_method_data[muid]": `${stripeIds.body.muid}`,
                        "payment_method_data[sid]": `${stripeIds.body.sid}`,
                        "payment_method_data[payment_user_agent]": this.obj.stripePaymentAgent,
                        "payment_method_data[time_on_page]": "24819",
                        "payment_method_data[referrer]": "https://checkout.limitedrun.com/",
                        "expected_payment_method_type": "card",
                        "use_stripe_sdk": "true",
                        "webauthn_uvpa_available": "false",
                        "spc_eligible": "false",
                        "key": this.obj.stripe,
                        "_stripe_version": "2020-08-27",
                        "client_secret": `${this.obj.submit.order.intent}`
                    }),
                    headers: {
                        accept: "application/json",
                        "accept-encoding": "gzip, br",
                        "accept-language": "en-US,en;q=0.9",
                        "content-type": "application/x-www-form-urlencoded",
                        origin: "https://js.stripe.com",
                        referer: "https://js.stripe.com/",
                        "user-agent": this.obj.UA
                    },
                    cookieJar: this.cookieJar,
                    agent: this.proxy
                })
                // await console.log(response.data)
                // this.log(response.data.status === 'succeeded' ? response.data.description : response.status + ' Check email?', 'green')
                this.log(`${response.statusCode} - ${response.body.status === 'succeeded' ? response.body.description : 'Check email?'}`, 'green')
                if (response.body.status === 'succeeded') {
                    return 'success'
                } else {
                    return response.body
                }
            } catch (err) {
                if (err.response) {
                    // this.log(err.response ? `${err.response.status} - error submitting order` : 'error submitting order')
                    switch (err.response.body.error.code) {
                        case 'card_declined':
                            // this.log('card decline', 'error')
                            return 'decline'
                            break
                        default:
                            this.log(`${err.response.statusCode} - ${err.response.data.error.code}`, 'error')
                            return 'decline'
                    }
                } else {
                    this.log('000 - Unknown error submitting stripe, please check monitor for manual checkout', 'error')
                    this.enabled = false
                    this.flow()
                }
            }
        }
    };

    async monitor () {
        this.log(`000 - Monitoring ${this.link.split('/')[2]}`)


        // Lowering oInput
        this.oInput = this.oInput.toLowerCase()

        let monitored
        // Is the product input (pInput) a direct link, option ID, or Keyword
        switch (this.inputType) {
            case 'DL':
                monitored = await this.dlMonitor(this.pInput)
                break;
            case 'PID':
                monitored = {link: this.link, OID: this.pInput}
                break;
            case 'KW':
                this.pInput = this.pInput.map(p => p.toLowerCase())
                this.positives = this.pInput.filter(keyword => keyword[0] === '+')
                this.negatives = this.pInput.filter(keyword => keyword[0] === '-')
                monitored = await this.kwMonitor()
                break;
        }
        if (monitored) {
            return (monitored)
        } else {
            this.enabled = false
        }
    };

    async kwMonitor () {
        if (this.enabled) {
            try {
                const search = this.positives.map(pos => pos.slice(1, pos.length))
                const link = `${this.link}/products/search?q=${search.join('+')}&cgid=${this.randomStr()}`
                const response = await got.get(link, {
                    responseType: 'text',
                    headers: {
                        'User-Agent': this.userAgent()
                    },
                    agent: await this.proxyGot('monitor')
                })

                const root = parse(response.body)
                const products = this.link.includes('drewmillward.awesomedistro.com') ? root.querySelectorAll('.product a') : root.querySelectorAll('.grid-item-caption a')
                for (let i = 0; i < products.length; i++) {
                    const product = products[i]
                    const pMatches = await this.positives.filter(keyword => product.innerText.toLowerCase().includes(keyword.slice(1, keyword.length)))
                    const nMatches = await this.negatives.filter(keyword => product.innerText.toLowerCase().includes(keyword.slice(1, keyword.length)))
                    if (pMatches.length === this.positives.length && nMatches.length === 0) {
                        this.log(`${response.statusCode} - Found: ${product.innerText.trim()} - switching to DL monitor`)
                        // this.log(`${this.link}${product.outerHTML.split('"')[1]}`)
                        const dlSwitch = await this.dlMonitor(`${this.link}${product.outerHTML.split('"')[1]}`)
                        return dlSwitch
                    }
                }
            } catch (err) {
                this.log(err.response ? `${err.response.statusCode} - Error with keyword monitor` : '000 - Error with keyword monitor', 'error')
            }
            await new Promise(r => setTimeout(r, 1000))
            const remonitor = await this.kwMonitor()
            return remonitor
        }
    };

    async dlMonitor (dl) {
        if (this.enabled) {
            try {
                const response = await got.get(`${dl}?${Math.floor(Math.random() * 999999999)}`, {
                    responseType: 'text',
                    headers: {
                        'User-Agent': this.userAgent()
                    },
                    agent: await this.proxyGot('monitor')
                })
                const root = parse(response.body)
                if (root.querySelector('#cart_variation_id')) {
                    const variantsFind = root.querySelectorAll('#cart_variation_id option')
                    const variants = variantsFind.map(variant => {
                        return {
                            title: variant.innerHTML.split(' — ')[1],
                            id: variant.outerHTML.split('"')[1]
                        }
                    })
                    switch(this.oInput) {
                        case '':
    
    
                            break;
                        default:
                            for (let i = 0; i < variants.length; i++) {
                                const variant = variants[i]
                                // Filters out the first option which is just a title option
                                if (variant.title) {
                                    if (variant.title.toLowerCase().includes(this.oInput) && variant.id) {
                                        return {title: `${root.querySelector('[property="og:title"]')._attrs.content} - ${variant.title}`, OID: variant.id}
                                    }
                                }
                            }
                            // if it did not find an OID on last run ^ and randomOOS = true
                            if (this.randomOos) {
                                for (let i = 0; i < variants.length; i++) {
                                    const variant = variants[i]
                                    // Filters out the first option which is just a title option
                                    if (variant.title && variant.id) {
                                        return {title: `${root.querySelector('[property="og:title"]')._attrs.content} - ${variant.title}`, OID: variant.id}
                                    }
                                }
                            }
                    }
                } else if (root.querySelector('.variations button')) {
                    const variantFind = root.querySelector('.variations button')
                    return { title: root.querySelector('[property="og:title"]')._attrs.content, OID: variantFind.outerHTML.split('.add(')[1].split(')')[0] }
                    
    
                }
            } catch (err) {
                this.log(err.response ? `${err.response.statusCode} - Error with direct link monitor` : '000 - Error with direct link monitor', 'error')
                // console.log(err)
            }
            // this.counter === this.proxies.length ? this.counter = 0 : this.counter++
            this.monCounter++
            if (this.monCounter % 10 === 0) {
                this.log('Still monitoring')
            }
            await new Promise(r => setTimeout(r, 1000))
            const reDLMonitor = await this.dlMonitor(dl)
            return reDLMonitor
        }
    };

};