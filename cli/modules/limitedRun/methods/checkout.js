// Checkout and cart api

const got = require('got')
const qs = require('qs')
const { parse } = require('node-html-parser')
module.exports = class Checkout  {
    constructor(group, name, link, inputType, pInput, oInput, randomOos, quantity, profile, proxyList, platform, mode) {
        this.group = group;
		this.name = name;
		this.link = link;
		this.inputType = inputType;
		this.pInput = pInput;
		this.oInput = oInput;
		this.randomOos = randomOos;
		this.quantity = quantity;
		this.profile = profile;
		this.proxyList = proxyList;
		this.platform = platform;
		this.mode = mode;
    }

    async cOptions (type = 'checkout', responseType = 'json') {
        try {
            let options = {}
            switch (type) {
                default:
                    options = {
                        responseType: responseType,
                        headers: {
                            "accept": "*/*",
                            "accept-encoding": "gzip, br",
                            "accept-language": "en-US,en;q=0.9",
                            "connection": "keep-alive",
                            "content-type": "application/json",
                            "user-agent": this.config.userAgent
                        },
                        cookieJar: this.config.cookieJar,
                        agent: this.config.agent
                    }
            }
            return options
        } catch (error) {
            await this.log(`000 - Error with request options: ${error.message}`, 'error')
            this.enabled = false
        }
    }
    // Generate cart
    async cGenCart () {
        if (this.enabled) {
            try {
                await this.log('000 - Generating cart')
                const response = await got({
                    ... await this.cOptions('checkout', 'json'),
                    method: 'GET',
                    url: `${this.link}/cart.json`
                })
                await this.log(`${response.statusCode} - Generated cart`)
                return response.body
            } catch (error) {
                this.log(`${error.response ? error.response.statusCode : '000'} - Error generating cart`, 'error')
                if (error.response) {
                    if (error.response.statusCode >= 400) {
                        await this.delay(1000)
                        return await this.cGenCart()
                    } else {
                        this.enabled = false
                    }
                } else {
                    this.enabled = false
                    // this.flow()
                }
            }
        }
    };

    // Add item to cart 
    async cAdd () {
        if (this.enabled) {
            try {
                const response = await got({
                    ... await this.cOptions('checkout', 'json'),
                    url: `${this.link}/cart.json`,
                    method: 'PUT',
                    json: {
                        id: this.data.cart.id,
                        modify: "item",
                        "item": {
                            "id": `${this.data.item.OID}`,
                            "quantity": this.quantity,
                            "discount": null
                        },
                        // "modify": "shipping",
                        // "shipping": {
                        //     "country": "39",
                        //     method: null
                        // }
                    },
                })


                // Technically you are allowed to go on after this, but because there is nothing in the cart, you get errors at the gen checkout step
                if (response.body.item_count > 0) {
                    await this.log(`${response.statusCode} - Added: ${Object.values(response.body.items).map(item => item.name).join(', ')} ($${response.body.total_price})`)
                    return response.body
                } else {
                    await this.delay(1500)
                    return await this.cAdd()
                }
            } catch (error) {
                await this.log(`${error.response ? error.response.statusCode : '000'} - Error adding to cart: ${error.message}`, 'error')
                if (error.response) {
                    if (error.response.statusCode >= 400) {
                        await this.delay(1500)
                        return await this.atc()
                    } else {
                        this.enabled = false
                        // this.flow()
                    }
                } else {
                    this.enabled = false
                    // this.flow()
                }
            }
        }
    };

    // Can this be done in same req as add?
    async cGetShipping () {
        if (this.enabled) {
            try {
                const response = await got({
                    ... await this.cOptions('checkout', 'json'),
                    url: `${this.link}/cart.json`,
                    method: 'PUT',
                    json: {
                        "id": this.data.cart.id,
                        "modify": "shipping",
                        "shipping": {
                            "country": "39",
                            method: null
                        }
                    }
                })
                const method = (response.body.shipping.config.methods.filter(method => !method.conflict).reduce((prev, curr) => prev.calculated_amout < curr.calculated_amout ? prev : curr)).id
                // if (method) {
                    await this.log(`${response.statusCode} - Shipping method: ${method}`)
                    return method
                // } else {
                //     await this.log(`${response.statusCode} - Polling shipping methods...`)
                //     await this.delay(1000)
                //     return await this.cGetShipping()
                // }
            } catch (error) {
                await this.log(`${error.response ? error.response.statusCode : '000'} - Error polling shipping method: ${error.message}`, 'error')
                if (error.response) {
                    if (error.response.status >= 400) {
                        await this.delay(1000)
                        return await this.cGetShipping()
                    } else {
                        this.enabled = false
                        // this.flow()
                    }
                } else {
                    this.enabled = false
                    // this.flow()
                }
            }
        }
    };

    // Can this be done in same req as add?
    async cSubmitShipping (method) {
        if (this.enabled) {
            try {
                const response = await got({
                    ... await this.cOptions('checkout', 'json'),
                    url: `${this.link}/cart.json`,
                    method: 'PUT',
                    json: {
                        "id": this.data.cart.id,
                        "modify": "shipping",
                        "shipping": {
                            "country": "39",
                            method: method
                        }
                    }
                })
                await this.log(`${response.statusCode} - Submitted shipping method`)
                return response.body
            } catch (error) {
                await this.log(`${error.response ? error.response.statusCode : '000'} - Error submitting shipping method: ${error.message}`, 'error')
                if (error.response) {
                    if (error.response.status >= 400) {
                        await this.delay(1000)
                        return await this.cGetShipping()
                    } else {
                        this.enabled = false
                        // this.flow()
                    }
                } else {
                    this.enabled = false
                    // this.flow()
                }
            }
        }
    };

    // Create checkout session
    async cCreateCheckout () {
        if (this.enabled) {
            try {
                const response = await got({
                    ... await this.cOptions('checkout', 'text'),
                    url: `${this.link}/cart/checkout`,
                    method: 'GET',
                    // responseType: 'text',
                    // headers: {
                    //     accept: "*/*",
                    //     "accept-encoding": "gzip, br",
                    //     "accept-language": "en-US,en;q=0.9",
                    //     connection: "keep-alive",
                    //     "content-type": "application/json",
                    //     "user-agent": this.config.userAgent
                    // },
                    // cookieJar: this.cookieJar,
                    // agent: this.proxy
                })
                
                // comment this whole "if" out once stable
                if (!`${response.url.split('?')[0]}.json}`.includes('returned')) {
                    this.log(`${response.statusCode} - Checkout available: ${response.url.split('?')[0]}.json`)
                    // if (!this.obj.stripe) {
                        this.data.stripe = response.body.split('stripe_key: "')[1].split('"')[0]
                    // }
                    const root = await parse(response.body)
                    this.data.csrf = root.querySelector('[name="csrf-token"]').attrs.content
                    return `${response.url.split('?')[0]}`
                } else if (`${response.url.split('?')[0]}.json}`.includes('returned')) {
                    this.log(`${response.statusCode} - Item OOS, retrying...`, 'error')
                    await this.delay(1000)
                    return await this.cCreateCheckout()
                }

                return `${response.url.split('?')[0]}`
            } catch (error) {
                await this.log(`${error.response ? error.response.statusCode : '000'} - Error submitting shipping method: ${error.message}`, 'error')
                if (error.response) {
                    if (error.response.status >= 400) {
                        await this.delay(1000)
                        return await this.cCreateCheckout()
                    } else {
                        this.enabled = false
                        // this.flow()
                    }
                } else {
                    this.enabled = false
                    // this.flow()
                }
            }
        }
    };

    // Submit profile
    async cProfile () {
        if (this.enabled) {
            await this.log('000 - Submitting profile')
            // States have their own unique id that does not have to be sent, but the browser does it so here we are
            const states = {Alaska:1,Alabama:2,Arkansas:3,Arizona:4,California:5,Colorado:6,Connecticut:7,'District of Columbia':8,Delaware:9,Florida:10,Georgia:11,Hawaii:12,Iowa:13,Idaho:14,Illinois:15,Indiana:16,Kansas:17,Kentucky:18,Louisiana:19,Massachusetts:20,Maryland:21,Maine:22,Michigan:23,Minnesota:24,Missouri:25,Mississippi:26,Montana:27,'North Carolina':28,'North Dakota':29,Nebraska:30,'New Hampshire':31,'New Jersey':32,'New Mexico':33,Nevada:34,'New York':35,Ohio:36,Oklahoma:37,Oregon:38,Pennsylvania:39,'Rhode Island':40,'South Carolina':41,'South Dakota':42,Tennessee:43,Texas:44,Utah:45,Virginia:46,Vermont:47,Washington:48,Wisconsin:49,'West Virginia':50,Wyoming:51,'Armed Forces Americas':115,'Armed Forces Europe':116,'Armed Forces Pacific':117}
            try {
                const options = {
                    ... await this.cOptions('checkout', 'json'),
                    url: `${this.data.checkout_url}/prepare.json`,
                    method: 'POST',
                    body: qs.stringify({
                        "_method": "PUT",
                        "order[name]": `${this.profile.shippingAddress.name}`,
                        "order[email]": `${this.profile.shippingAddress.email}`,
                        "order[phone]": `+1${this.profile.shippingAddress.phone}`,
                        "order[street_address_1]": `${this.profile.shippingAddress.line1}`,
                        "order[street_address_2]": `${this.profile.shippingAddress.line2}`,
                        "order[city]": `${this.profile.shippingAddress.city}`,
                        "order[state_id]": `${states[this.profile.shippingAddress.state]}`,
                        "order[state_name]": `${this.profile.shippingAddress.state}`,
                        "order[postal_code]": `${this.profile.shippingAddress.postCode}`,
                        "order[country_id]": "39",
                        "order[country_name]": "United States",
                    })
                }
                options.headers['content-type'] = 'application/x-www-form-urlencoded; charset=UTF-8'
                options.headers['X-CSRF-Token'] = this.data.csrf
                const response = await got(options)
                if (response.body.success) {
                    await this.log(`${response.statusCode} - Submitted profile`)
                    return true
                } else {
                    await this.log(`${response.statusCode} - Error submitting profile: ${response.body.errors}`, 'error')
                    this.enabled = false
                    return false

                }
            } catch (error) {
                await this.log(`${error.response ? error.response.statusCode : '000'} - Error submitting profile: ${error.message}`, 'error')
                if (error.response) {
                    if (error.response.statusCode >= 400) {
                        await this.delay(1000)
                        return await this.cProfile()
                    } else {
                        this.enabled = false
                        // this.flow()
                    }
                } else {
                    this.enabled = false
                    // this.flow()
                }
            }
        }
    };

    // Find payment method (paypal or stripe)
    async cGetPaymentMethod () {
        if (this.enabled) {
            try {
                const response = await got({
                    ... await this.cOptions('checkout', 'text'),
                    method: 'GET',
                    url: this.data.checkout_url
                })

                // Stripe priority
                const stripe = response.body.split('"stripe":')[1].split('}')[0].trim()
                const method =  stripe === 'true' ? 'stripe' : 'paypal'

                // // paypal priority
                // const stripe = response.body.split('"paypal":')[1].split(',')[0].trim()
                // const method =  stripe === 'true' ? 'paypal' : 'stripe'
                if (method === 'stripe') {
                    this.data.stripeLiveKey = response.body.split('stripe_key: "')[1].split('"')[0]
                    // await console.log('stripeKey', this.data.stripe)
                    // await this.log(`000 - Stripe live key: ${this.data.stripeLiveKey}`, 'prompt')
                }
                await this.log(`${response.statusCode} - Payment method: ${method}`)
                return method
            } catch (error) {
                await this.log(`${error.response ? error.response.statusCode : '000'} - Error getting payment method: ${error.message}`, 'error')

                if (error.response) {
                    if (error.response.statusCode >= 400) {
                        await this.delay(1000)
                        return await this.cGetPaymentMethod()
                    } else {
                        this.enabled = false
                        // this.flow()
                    }
                } else {
                    this.enabled = false
                    // this.flow()
                }
            }
        }
    }

    // Generate Paypal or Stripe info
    async cCreatePayment () {
        if (this.enabled) {
            try {
                const options = {
                    ... await this.cOptions('checkout', 'json'),
                    method: 'POST',
                    url: `${this.data.checkout_url}/intent.json`,
                    body: qs.stringify({
                        "_method": "PUT",
                        "gateway": this.data.paymentMethod
                    }),
                }
                options.headers['content-type'] = 'application/x-www-form-urlencoded; charset=UTF-8'
                const response = await got(options)
                await this.log(`${response.statusCode} - Found payment info`)
                return this.data.paymentMethod === 'paypal' ? response.body.order.intent.split('token=')[1].split('&')[0] : response.body.order.intent
            } catch (error) {
                await this.log(`${error.response ? error.response.statusCode : '000'} - Error getting payment info: ${error.message}`, 'error')

                if (error.response) {
                    if (error.response.statusCode >= 400) {
                        await this.delay(1000)
                        return await this.cCreatePayment()
                    } else {
                        this.enabled = false
                        // this.flow()
                    }
                } else {
                    this.enabled = false
                    // this.flow()
                }
            }
        }
    };

    // This may work, getting 303 rn tho
    async cReturnPaypalPost(url) {
        if (this.enabled && url) {
            try {
                // https://checkout.limitedrun.com/order/d89db080d50b196071e315deb0f406c9/returned?token=0WJ73989R7129984P&PayerID=YPMKGFRLJTG6S
                const body = {
                    '_method': 'PUT'
                }

                await url.split('?')[1].split('&').forEach(param => {
                    const p = param.split('=')
                    body[p[0]] = p[1]
                })
                await console.log('pp post', body)
                const options = {
                    ... await this.cOptions('checkout', 'json'),
                    method: 'POST',
                    url: url.split('?')[0],
                    body: qs.stringify(body),
                }
                await console.log(options)
                options.headers['content-type'] = 'application/x-www-form-urlencoded; charset=UTF-8'
                const response = await got(options)
                await console.log(response.body)
            } catch (error) {
                await this.log(`${error.response ? error.response.statusCode : '000'} - Error posting paypal return url: ${error.message}`, 'error')
                this.enabled = false
            }
        }
    }

    // Get return URL after paypal save
    async cReturnPaypal (url) {
        if (this.enabled && url) {
            try {
                const options = {
                    ... await this.cOptions('checkout', 'text'),
                    url: url,
                    method: 'GET'
                }
                options.headers = {
                    "User-Agent": this.config.userAgent,
                    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8",
                    "Accept-Language": "en-US,en;q=0.5",
                    "Accept-Encoding": "gzip, deflate, br",
                    "DNT": "1",
                    "Connection": "keep-alive",
                    "Referer": "https://www.paypal.com/",
                    "Upgrade-Insecure-Requests": "1",
                    "Sec-Fetch-Dest": "document",
                    "Sec-Fetch-Mode": "navigate",
                    "Sec-Fetch-Site": "cross-site"
                }
                const response = await got(options)
                await this.log(`${response.statusCode} - Retrieved checkout`)
            } catch (error) {
                await this.log(`${error.response ? error.response.statusCode : '000'} - Error Paypal return URL: ${error.message}`, 'error')

                if (error.response) {
                    if (error.response.statusCode >= 400) {
                        await this.delay(1000)
                        return await this.cReturnPaypal()
                    } else {
                        this.enabled = false
                        // this.flow()
                    }
                } else {
                    this.enabled = false
                    // this.flow()
                }
            }
        }
    }

    // Finalizes checkout
    async cFinalize () {
        if (this.enabled) {
            try {
                const options = {
                    ... await this.cOptions('checkout', 'json'),
                    url: `${this.data.checkout_url}/finalize.json`,
                    method: 'POST',
                    body: qs.stringify({
                        '_method': 'PUT'
                    })
                }
                // options.headers['content-type'] = 'application/x-www-form-urlencoded; charset=UTF-8'
                // options.headers['X-CSRF-Token'] = this.data.csrf
                options.headers = {
                    "User-Agent": this.config.userAgent,
                    "Accept": "*/*",
                    "Accept-Language": "en-US,en;q=0.5",
                    "Accept-Encoding": "gzip, deflate, br",
                    "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
                    "X-CSRF-Token": this.data.csrf, //"E4AU8tt18ed2Na4zFLer/digKcpe1Fu2co1Fr0gKxxZWfYDoPwy9eVCdnb0tMn4HK4SZFMn9AdlcJ+hcal67Ww==",
                    "X-Requested-With": "XMLHttpRequest",
                    "Origin": "https://checkout.limitedrun.com",
                    "DNT": "1",
                    "Connection": "keep-alive",
                    "Sec-Fetch-Dest": "empty",
                    "Sec-Fetch-Mode": "cors",
                    "Sec-Fetch-Site": "same-origin"
                }
                const response = await got(options)
                await this.log(`${response.statusCode} - Finalized checkout: ${response.body.success === true ? response.body.success : JSON.stringify(response.body.errors)}`)
            } catch (error) {
                await this.log(`${error.response ? error.response.statusCode : '000'} - Error finalizing checkout: ${error.message}`, 'error')

                if (error.response) {
                    if (error.response.statusCode >= 400) {
                        await this.delay(1000)
                        return await this.cFinalize()
                    } else {
                        this.enabled = false
                        // this.flow()
                    }
                } else {
                    this.enabled = false
                    // this.flow()
                }
            }
        }
    };

    // Submits checkout (Paypal only)
    async cSubmitCheckout () {
        if (this.enabled) {
            try {
                const options = {
                    ... await this.cOptions('checkout', 'text'),
                    url: `${this.link}/cart/returned?next=status`,
                    method: 'GET',
              
                }
                const response = await got(options)
                await this.log(`${response.statusCode} - Submitted checkout`)
            } catch (error) {
                await this.log(`${error.response ? error.response.statusCode : '000'} - Error submitting checkout: ${error.message}`, 'error')

                if (error.response) {
                    if (error.response.statusCode >= 400) {
                        await this.delay(1000)
                        return await this.cSubmitCheckout()
                    } else {
                        this.enabled = false
                        // this.flow()
                    }
                } else {
                    this.enabled = false
                    // this.flow()
                }
            }
        }
    };

    // Checks status of order
    async cCheckOrder () {
        if (this.enabled) {
            try {
                const options = {
                    ... await this.cOptions('checkout', 'json'),
                    url: `${this.link}/cart/status`,
                    method: 'POST',
                }
                const response = await got(options)
                await this.log(`${response.statusCode} - Checking order`)
                return response.body.order
                // if (response.body.order.attempted) {
                //     return response.body.order

                // } else {
                //     await this.delay(5000)
                //     return await this.cCheckOrder()
                // }
            } catch (error) {
                await this.log(`${error.response ? error.response.statusCode : '000'} - Error checking order: ${error.message}`, 'error')

                if (error.response) {
                    if (error.response.statusCode >= 400) {
                        await this.delay(1000)
                        return await this.cCheckOrder()
                    } else {
                        this.enabled = false
                        // this.flow()
                    }
                } else {
                    this.enabled = false
                    // this.flow()
                }
            }
        }
    }

    // After submitting the checkout you submit the stripe
    async submitStripe () {
        if (this.enabled) {
            try {
                // Get stripe ids
                const stripeIds = await got.post('https://m.stripe.com/6', {
                    responseType: 'json'
                })
                // await console.log(stripeIds.body, this.data.stripeLiveKey, this.data.stripePaymentAgent, this.data.stripe)
                const stripeLink = `https://api.stripe.com/v1/payment_intents/${this.data.stripe.split('_secret_')[0]}/confirm`
                const options = {
                    url: stripeLink,
                    method: 'POST',
                    responseType: 'json',
                    body: qs.stringify({
                        "payment_method_data[type]": "card",
                        "payment_method_data[billing_details][name]": `${this.profile.shippingAddress.name.split(' ')[1]}`,
                        "payment_method_data[billing_details][email]": `${this.profile.shippingAddress.email}`,
                        "payment_method_data[billing_details][address][postal_code]": `${this.profile.shippingAddress.postCode}`,
                        "payment_method_data[billing_details][phone]": `+1${this.profile.shippingAddress.phone}`,
                        "payment_method_data[card][number]": `${this.profile.paymentDetails.cardNumber}`,
                        "payment_method_data[card][cvc]": `${this.profile.paymentDetails.cardCvv}`,
                        "payment_method_data[card][exp_month]": `${this.profile.paymentDetails.cardExpMonth}`,
                        "payment_method_data[card][exp_year]": `${this.profile.paymentDetails.cardExpYear.replace('20', '')}`,
                        "payment_method_data[guid]": `${stripeIds.body.guid}`,
                        "payment_method_data[muid]": `${stripeIds.body.muid}`,
                        "payment_method_data[sid]": `${stripeIds.body.sid}`,
                        "payment_method_data[payment_user_agent]": this.data.stripePaymentAgent,
                        "payment_method_data[time_on_page]": "24819",
                        "payment_method_data[referrer]": "https://checkout.limitedrun.com/",
                        "expected_payment_method_type": "card",
                        "use_stripe_sdk": "true",
                        "webauthn_uvpa_available": "false",
                        "spc_eligible": "false",
                        "key": this.data.stripeLiveKey,
                        "_stripe_version": "2020-08-27",
                        "client_secret": `${this.data.stripe}`
                    }),
                    headers: {
                        accept: "application/json",
                        "accept-encoding": "gzip, br",
                        "accept-language": "en-US,en;q=0.9",
                        "content-type": "application/x-www-form-urlencoded",
                        origin: "https://js.stripe.com",
                        referer: "https://js.stripe.com/",
                        "user-agent": this.config.userAgent
                    },
                    cookieJar: this.config.cookieJar,
                    agent: this.config.agent
                }
                // await console.dir(options)
                const response = await got(options)
                // await console.log(response.data)
                // this.log(response.data.status === 'succeeded' ? response.data.description : response.status + ' Check email?', 'green')
                await this.log(`${response.statusCode} - ${response.body.status === 'succeeded' ? response.body.description : 'Submitted payment'}`)
                // await console.dir(response.body)
                return response.body
                // if (response.body.status === 'succeeded') {
                //     return 'success'
                // } else {
                //     return response.body
                // }
            } catch (error) {
                if (error.response) {
                    // await console.dir(error.response.body)
                    await this.log(`${error.response.statusCode} - Error submitting payment: ${error.response.body.error.message}`, 'error')
                    return error.response.body
                    // switch (error.response.body.error.code) {
                    //     case 'card_declined':
                    //         // this.log('card decline', 'error')
                    //         return 'decline'
                    //         break
                    //     default:
                    //         this.log(`${error.response.statusCode} - ${error.response.data.error.code}`, 'error')
                    //         return 'decline'
                    // }
                } else {
                    this.log('000 - Unknown error submitting stripe, please check monitor for manual checkout', 'error')
                    this.enabled = false
                    // this.flow()
                }
            }
        }
    };
};