// Checkout and cart api

const got = require('got')
const { parse } = require('node-html-parser')

module.exports = class Monitor {
    constructor(group, name, link, inputType, pInput, oInput, randomOos, quantity, profile, proxyList, platform, mode) {
        this.group = group;
		this.name = name;
		this.link = link;
		this.inputType = inputType;
		this.pInput = pInput;
		this.oInput = oInput;
		this.randomOos = randomOos;
		this.quantity = quantity;
		this.profile = profile;
		this.proxyList = proxyList;
		this.platform = platform;
		this.mode = mode;
    }

    async monitor () {
        this.log(`000 - Monitoring ${this.link.split('/')[2]}`)


        // Lowering oInput
        this.oInput = this.oInput.toLowerCase()

        let monitored
        // Is the product input (pInput) a direct link, option ID, or Keyword
        switch (this.inputType) {
            case 'DL':
                monitored = await this.dlMonitor(this.pInput)
                break;
            case 'PID':
                monitored = {link: this.link, OID: this.pInput}
                break;
            case 'KW':
                this.pInput = this.pInput.map(p => p.toLowerCase())
                this.positives = this.pInput.filter(keyword => keyword[0] === '+')
                this.negatives = this.pInput.filter(keyword => keyword[0] === '-')
                monitored = await this.kwMonitor()
                break;
        }
        if (monitored) {
            return (monitored)
        } else {
            this.enabled = false
        }
    };

    async kwMonitor () {
        if (this.enabled) {
            try {
                const search = this.positives.map(pos => pos.slice(1, pos.length))
                const link = `${this.link}/products/search?q=${search.join('+')}&cgid=${this.randomStr()}`
                const response = await got.get(link, {
                    responseType: 'text',
                    headers: {
                        'User-Agent': this.userAgent()
                    },
                    agent: await this.proxyGot('monitor')
                })

                const root = parse(response.body)
                const products = this.link.includes('drewmillward.awesomedistro.com') ? root.querySelectorAll('.product a') : root.querySelectorAll('.grid-item-caption a')
                for (let i = 0; i < products.length; i++) {
                    const product = products[i]
                    const pMatches = await this.positives.filter(keyword => product.innerText.toLowerCase().includes(keyword.slice(1, keyword.length)))
                    const nMatches = await this.negatives.filter(keyword => product.innerText.toLowerCase().includes(keyword.slice(1, keyword.length)))
                    if (pMatches.length === this.positives.length && nMatches.length === 0) {
                        this.log(`${response.statusCode} - Found: ${product.innerText.trim()} - switching to DL monitor`)
                        // this.log(`${this.link}${product.outerHTML.split('"')[1]}`)
                        const dlSwitch = await this.dlMonitor(`${this.link}${product.outerHTML.split('"')[1]}`)
                        return dlSwitch
                    }
                }
            } catch (error) {
                this.log(`${error.response ? error.response.statusCode : '000'} - Error in keyword monitor: ${error.message}`, 'error')
            }
            await new Promise(r => setTimeout(r, 1000))
            const remonitor = await this.kwMonitor()
            return remonitor
        }
    };

    async dlMonitor (dl) {
        if (this.enabled) {
            try {
                const response = await got.get(`${dl}?${Math.floor(Math.random() * 999999999)}`, {
                    responseType: 'text',
                    headers: {
                        'User-Agent': this.userAgent()
                    },
                    agent: await this.proxyGot('monitor')
                })
                const root = parse(response.body)
                if (root.querySelector('#cart_variation_id')) {
                    const variantsFind = root.querySelectorAll('#cart_variation_id option')
                    const variants = variantsFind.map(variant => {
                        return {
                            title: variant.innerHTML.split(' — ')[1],
                            id: variant.outerHTML.split('"')[1]
                        }
                    })
                    switch(this.oInput) {
                        case '':
    
    
                            break;
                        default:
                            for (let i = 0; i < variants.length; i++) {
                                const variant = variants[i]
                                // Filters out the first option which is just a title option
                                if (variant.title) {
                                    if (variant.title.toLowerCase().includes(this.oInput) && variant.id) {
                                        return {title: `${root.querySelector('[property="og:title"]')._attrs.content} - ${variant.title}`, OID: variant.id}
                                    }
                                }
                            }
                            // if it did not find an OID on last run ^ and randomOOS = true
                            if (this.randomOos) {
                                for (let i = 0; i < variants.length; i++) {
                                    const variant = variants[i]
                                    // Filters out the first option which is just a title option
                                    if (variant.title && variant.id) {
                                        return {title: `${root.querySelector('[property="og:title"]')._attrs.content} - ${variant.title}`, OID: variant.id}
                                    }
                                }
                            }
                    }
                } else if (root.querySelector('.variations button')) {
                    const variantFind = root.querySelector('.variations button')
                    return { title: root.querySelector('[property="og:title"]')._attrs.content, OID: variantFind.outerHTML.split('.add(')[1].split(')')[0] }
                    
    
                }
            } catch (err) {
                this.log(err.response ? `${err.response.statusCode} - Error with direct link monitor` : '000 - Error with direct link monitor', 'error')
                // console.log(err)
            }
            // this.counter === this.proxies.length ? this.counter = 0 : this.counter++
            this.monCounter++
            if (this.monCounter % 10 === 0) {
                this.log('Still monitoring')
            }
            await new Promise(r => setTimeout(r, 1000))
            const reDLMonitor = await this.dlMonitor(dl)
            return reDLMonitor
        }
    };
}