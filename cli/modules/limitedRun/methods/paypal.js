// Paypal methods

const got = require('got')
const qs = require('qs');
const fs = require('fs');
const {parse} = require('node-html-parser')

module.exports = class Paypal {
    constructor(group, name, link, inputType, pInput, oInput, randomOos, quantity, profile, proxyList, platform, mode) {
        this.group = group;
		this.name = name;
		this.link = link;
		this.inputType = inputType;
		this.pInput = pInput;
		this.oInput = oInput;
		this.randomOos = randomOos;
		this.quantity = quantity;
		this.profile = profile;
		this.prooxyList = proxyList;
		this.platform = platform;
		this.mode = mode;
    }
	
	async pOptions () {
		return {
			responseType: 'json',
			agent: this.config.agent,
			cookieJar: this.config.cookieJar,
			method: 'POST'
		}
	}
	// Submits the paypal order (does not submit the big cartel order)
	async pSubmit() {
		if (this.enabled) {
			let cardType
			switch(this.profile.paymentDetails.cardType) {
				case 'Visa':
					cardType = 'VISA'
					break;
				case 'MasterCard':
					cardType = 'MASTER_CARD'
					break;
				case 'AmericanExpress':
					cardType = 'AMEX'
					break;
				default:
					cardType = this.profile.paymentDetails.cardType.toUpperCase()
			}
			try {
				const states = {Alabama:'AL',Alaska:'AK',Arizona:'AZ',Arkansas:'AR',California:'CA',Colorado:'CO',Connecticut:'CT',Delaware:'DE',Florida:'FL',Georgia:'GA',Hawaii:'HI',Idaho:'ID',Illinois:'IL',Indiana:'IN',Iowa:'IA',Kansas:'KS',Kentucky:'KY',Louisiana:'LA',Maine:'ME',Maryland:'MD',Massachusetts:'MA',Michigan:'MI',Minnesota:'MN',Mississippi:'MS',Missouri:'MO',Montana:'MT',Nebraska:'NE',Nevada:'NV','New Hampshire':'NH','New Jersey':'NJ','New Mexico':'NM','New York':'NY','North Carolina':'NC','North Dakota':'ND',Ohio:'OH',Oklahoma:'OK',Oregon:'OR',Pennsylvania:'PA','Rhode Island':'RI','South Carolina':'SC','South Dakota':'SD',Tennessee:'TN',Texas:'TX',Utah:'UT',Vermont:'VT',Virginia:'VA',Washington:'WA','West Virginia':'WV',Wisconsin:'WI',Wyoming:'WY'}
				const data = {
					"operationName": "OnboardGuestMutation",
					"variables": {
						"card": {
							"cardNumber": this.profile.paymentDetails.cardNumber,
							"expirationDate": `${this.profile.paymentDetails.cardExpMonth}/${this.profile.paymentDetails.cardExpYear}`,
							"securityCode": this.profile.paymentDetails.cardCvv,
							"type": cardType
						},
						"country": "US",
						"email": this.profile.billingAddress.email,
						"firstName": this.profile.billingAddress.name.split(' ')[0],
						"lastName": this.profile.billingAddress.name.split(' ')[1],
						"phone": {
							"countryCode": "1",
							"number": this.profile.billingAddress.phone,
							"type": "MOBILE"
						},
						"supportedThreeDsExperiences": [
							"IFRAME"
						],
						"token": this.data.paypalToken,
						"billingAddress": {
							"line1": this.profile.billingAddress.line1,
							"line2": this.profile.billingAddress.line2,
							"city": this.profile.billingAddress.city,
							"state": states[this.profile.shippingAddress.state],
							"postalCode": this.profile.billingAddress.postCode,
							"accountQuality": {
								"autoCompleteType": "MERCHANT_PREFILLED",
								"isUserModified": false,
								"twoFactorPhoneVerificationId": ""
							},
							"country": "US",
							"familyName": this.profile.billingAddress.name.split(' ')[1],
							"givenName": this.profile.billingAddress.name.split(' ')[0]
						},
						"shippingAddress": {
							"line1": this.profile.shippingAddress.line1,
							"line2": this.profile.shippingAddress.line2,
							"city": this.profile.shippingAddress.city,
							"state": states[this.profile.shippingAddress.state],
							"postalCode": this.profile.shippingAddress.postCode,
							"accountQuality": {
								"autoCompleteType": "MERCHANT_PREFILLED",
								"isUserModified": false
							},
							"country": "US",
							"familyName": this.profile.shippingAddress.name.split(' ')[1],
							"givenName": this.profile.shippingAddress.name.split(' ')[0]
						},
						"crsData": null
					},
					"query": "mutation OnboardGuestMutation($bank: BankAccountInput, $billingAddress: AddressInput, $card: CardInput, $country: CountryCodes, $currencyConversionType: CheckoutCurrencyConversionType, $dateOfBirth: DateOfBirth, $email: String, $firstName: String!, $lastName: String!, $phone: PhoneInput, $shareAddressWithDonatee: Boolean, $shippingAddress: AddressInput, $supportedThreeDsExperiences: [ThreeDSPaymentExperience], $token: String!) {\n  onboardAccount: onboardGuest(\n    bank: $bank\n    billingAddress: $billingAddress\n    card: $card\n    country: $country\n    currencyConversionType: $currencyConversionType\n    dateOfBirth: $dateOfBirth\n    email: $email\n    firstName: $firstName\n    lastName: $lastName\n    phone: $phone\n    shareAddressWithDonatee: $shareAddressWithDonatee\n    shippingAddress: $shippingAddress\n    token: $token\n  ) {\n    buyer {\n      auth {\n        accessToken\n        __typename\n      }\n      userId\n      __typename\n    }\n    flags {\n      is3DSecureRequired\n      __typename\n    }\n    ...fundingOptions\n    paymentContingencies {\n      threeDomainSecure(experiences: $supportedThreeDsExperiences) {\n        status\n        redirectUrl {\n          href\n          __typename\n        }\n        method\n        parameter\n        experience\n        requestParams {\n          key\n          value\n          __typename\n        }\n        __typename\n      }\n      ...threeDSContingencyData\n      __typename\n    }\n    __typename\n  }\n}\n\nfragment fundingOptions on CheckoutSession {\n  fundingOptions {\n    allPlans {\n      fundingSources {\n        fundingInstrument {\n          id\n          __typename\n        }\n        amount {\n          currencyCode\n          currencyValue\n          __typename\n        }\n        __typename\n      }\n      __typename\n    }\n    __typename\n  }\n  __typename\n}\n\nfragment threeDSContingencyData on PaymentContingencies {\n  threeDSContingencyData {\n    name\n    causeName\n    resolution {\n      type\n      resolutionName\n      paymentCard {\n        billingAddress {\n          line1\n          line2\n          city\n          state\n          country\n          postalCode\n          __typename\n        }\n        expireYear\n        expireMonth\n        currencyCode\n        cardProductClass\n        id\n        encryptedNumber\n        type\n        number\n        bankIdentificationNumber\n        __typename\n      }\n      contingencyContext {\n        deviceDataCollectionUrl {\n          href\n          __typename\n        }\n        jwtSpecification {\n          jwtDuration\n          jwtIssuer\n          jwtOrgUnitId\n          type\n          __typename\n        }\n        reason\n        referenceId\n        source\n        __typename\n      }\n      __typename\n    }\n    __typename\n  }\n  __typename\n}\n"
				}

				const options = {
					... await this.pOptions(),
					url: 'https://www.paypal.com/graphql?OnboardGuestMutation',
					headers: {
						'User-Agent': this.config.userAgent, 
						'Accept': '*/*', 
						'Accept-Language': 'en-US,en;q=0.5', 
						'content-type': 'application/json', 
						'paypal-client-context': this.data.paypalToken, 
						'paypal-client-metadata-id': this.data.paypalToken, 
						'x-app-name': 'checkoutuinodeweb_weasley', 
						'x-country': 'US', 
						'x-locale': 'en_US', 
						'Origin': 'https://www.paypal.com', 
						'DNT': '1', 
						'Connection': 'keep-alive', 
						'Sec-Fetch-Dest': 'empty', 
						'Sec-Fetch-Mode': 'cors', 
						'Sec-Fetch-Site': 'same-origin', 
						'Pragma': 'no-cache', 
						'Cache-Control': 'no-cache', 
						'TE': 'trailers'
					},
					body: JSON.stringify(data),
				}
				const response = await got(options)
				if (response.body.errors) {
					await this.log(`${response.statusCode} - Paypal authorization errors: ${response.body.errors.map(err => err.message).join(', ')}`, 'error')
				}

				if (response.body.data.flags && response.body.data.flags.is3DSecureRequired === true) {
					await this.log(`${response.statusCode} - 3DS required, task stopping`, 'error')
					this.enabled = false
				} else {
					this.data.paypal.authHeader = response.body.data.onboardAccount.buyer.auth.accessToken
					await this.log(`${response.statusCode} - Paypal submitted info`)
					return response.body.data.onboardAccount.fundingOptions[0].allPlans[0].fundingSources[0].fundingInstrument.id
				}
			} catch (error) {
				await this.log(`${error.response ? error.response.statusCode : '000'} - Paypal error submit: ${error.response ? '' : error.message === "Cannot read property 'buyer' of null" || error.message === "Cannot read properties of null (reading 'buyer')" ? 'Card blocked' : error.message}`, 'error')
				this.enabled = false
			}
		}
	};

	async pCapture(token) {
		if (this.enabled) {
			try {
				// const data = {
				// 	"operationName":"ApproveOnboardPaymentMutation",
				// 	"variables": {
				// 		"instrumentId":token,
				// 		"isBillingAgreement":false,
				// 		// "isWpsGuestUpgradeEligible":false,
				// 		"token":this.data.paypalToken
				// 	},
				// 	"query": "mutation ApproveOnboardPaymentMutation($token: String!, $instrumentId: String, $isBillingAgreement: Boolean!, $isWpsGuestUpgradeEligible: Boolean!) {\n  attemptSetStickyFi(token: $token, instrumentId: $instrumentId) @include(if: $isBillingAgreement) {\n    buyer {\n      userId\n      __typename\n    }\n    __typename\n  }\n  approveGuestSignUpPayment(token: $token) {\n    buyer {\n      userId\n      email {\n        stringValue\n        __typename\n      }\n      name {\n        fullName\n        __typename\n      }\n      __typename\n    }\n    ...CartDonePage\n    ...CompletedPaymentInfo\n    flags @include(if: $isWpsGuestUpgradeEligible) {\n      isGuestEligibleForUpgrade\n      __typename\n    }\n    fundingOptions {\n      fundingInstrument {\n        id\n        lastDigits\n        name\n        __typename\n      }\n      __typename\n    }\n    merchant {\n      name\n      preferences {\n        autoReturnToMerchant\n        enablePaymentDataTransfer\n        returnUrl\n        __typename\n      }\n      __typename\n    }\n    shippingAddresses {\n      fullAddress\n      __typename\n    }\n    __typename\n  }\n}\n\nfragment CartDonePage on CheckoutSession {\n  cart {\n    paymentId\n    returnUrl {\n      href\n      pathname\n      __typename\n    }\n    cancelUrl {\n      href\n      pathname\n      __typename\n    }\n    intent\n    items {\n      quantity\n      name\n      sku\n      unitPrice {\n        currencyValue\n        currencyFormat\n        __typename\n      }\n      itemOptionSelections {\n        name\n        description\n        __typename\n      }\n      __typename\n    }\n    amounts {\n      subtotal {\n        currencyCode\n        currencyFormat\n        __typename\n      }\n      tax {\n        currencyCode\n        currencyFormat\n        __typename\n      }\n      shipping {\n        currencyCode\n        currencyFormat\n        __typename\n      }\n      insurance {\n        currencyCode\n        currencyFormat\n        __typename\n      }\n      handlingFee {\n        currencyCode\n        currencyFormat\n        __typename\n      }\n      total {\n        currencyCode\n        currencyFormat\n        __typename\n      }\n      discount {\n        currencyCode\n        currencyFormat\n        __typename\n      }\n      __typename\n    }\n    __typename\n  }\n  __typename\n}\n\nfragment CompletedPaymentInfo on CheckoutSession {\n  completedPaymentInfo {\n    transactionId\n    transactionState\n    receiptId\n    softDescriptor\n    postbackData\n    __typename\n  }\n  __typename\n}\n"
				// }
				const data = {
					"operationName": "ApproveOnboardPaymentMutation",
					"query": "mutation ApproveOnboardPaymentMutation($token: String!, $instrumentId: String, $isBillingAgreement: Boolean!) {\n  attemptSetStickyFi(token: $token, instrumentId: $instrumentId) @include(if: $isBillingAgreement) {\n    buyer {\n      userId\n      __typename\n    }\n    __typename\n  }\n  approveGuestSignUpPayment(token: $token) {\n    buyer {\n      userId\n      email {\n        stringValue\n        __typename\n      }\n      name {\n        fullName\n        __typename\n      }\n      __typename\n    }\n    ...CartDonePage\n    ...CompletedPaymentInfo\n    fundingOptions {\n      fundingInstrument {\n        id\n        lastDigits\n        name\n        __typename\n      }\n      __typename\n    }\n    merchant {\n      name\n      preferences {\n        autoReturnToMerchant\n        enablePaymentDataTransfer\n        returnUrl\n        __typename\n      }\n      __typename\n    }\n    shippingAddresses {\n      fullAddress\n      __typename\n    }\n    __typename\n  }\n}\n\nfragment CartDonePage on CheckoutSession {\n  cart {\n    paymentId\n    returnUrl {\n      href\n      pathname\n      __typename\n    }\n    cancelUrl {\n      href\n      pathname\n      __typename\n    }\n    intent\n    items {\n      quantity\n      name\n      sku\n      unitPrice {\n        currencyValue\n        currencyFormat\n        __typename\n      }\n      itemOptionSelections {\n        name\n        description\n        __typename\n      }\n      __typename\n    }\n    amounts {\n      subtotal {\n        currencyCode\n        currencyFormat\n        __typename\n      }\n      tax {\n        currencyCode\n        currencyFormat\n        __typename\n      }\n      shipping {\n        currencyCode\n        currencyFormat\n        __typename\n      }\n      insurance {\n        currencyCode\n        currencyFormat\n        __typename\n      }\n      handlingFee {\n        currencyCode\n        currencyFormat\n        __typename\n      }\n      total {\n        currencyCode\n        currencyFormat\n        __typename\n      }\n      discount {\n        currencyCode\n        currencyFormat\n        __typename\n      }\n      __typename\n    }\n    __typename\n  }\n  __typename\n}\n\nfragment CompletedPaymentInfo on CheckoutSession {\n  completedPaymentInfo {\n    transactionId\n    transactionState\n    receiptId\n    softDescriptor\n    postbackData\n    __typename\n  }\n  __typename\n}\n",
					"variables": {
						"instrumentId": token,
						"isBillingAgreement": false,
						"token": this.data.paypalToken
					}
				}
				const response = await got({
					// responseType: 'json',
					... await this.pOptions(),
					url: 'https://www.paypal.com/graphql?ApproveOnboardPaymentMutation',
					headers: {
						'User-Agent': this.config.userAgent, 
						'Accept': '*/*', 
						'Accept-Language': 'en-US,en;q=0.5', 
						'content-type': 'application/json', 
						'paypal-client-context': this.data.paypalToken, 
						'paypal-client-metadata-id': this.data.paypalToken, 
						'x-app-name': 'checkoutuinodeweb_weasley', 
						'x-paypal-internal-euat': this.data.paypal.authHeader, 
						'x-country': 'US', 
						'x-locale': 'en_US', 
						'Origin': 'https://www.paypal.com', 
						'DNT': '1', 
						'Connection': 'keep-alive', 
						'Sec-Fetch-Dest': 'empty', 
						'Sec-Fetch-Mode': 'cors', 
						'Sec-Fetch-Site': 'same-origin', 
						'Pragma': 'no-cache', 
						'Cache-Control': 'no-cache', 
						'TE': 'trailers'
					},
					body: JSON.stringify(data),
					// cookieJar: this.cookieJar,
					// agent: this.proxy
				})
				if (response.body.errors) {
					await this.log(`${response.statusCode} - Paypal authorization errors: ${response.body.errors.map(err => err.message).join(', ')}`, 'error')
				}
				const returnUrl = response.body.data.approveGuestSignUpPayment.cart.returnUrl.href
				await this.log(`${response.statusCode} - Paypal authorized card`)
				return returnUrl
			} catch (error) {
				await this.log(`${error.response ? error.response.statusCode : '000'} - Error posting pp order ${error.response ? '' : error.message}`, 'error')
				this.enabled = false

			}
		}
	};
}