const got = require('got');
const tunnel = require('tunnel');
const { CookieJar } = require('tough-cookie');
const { promisify } = require('util');
const { resolve } = require('path');

const Checkout = require('./methods/checkout');
const Monitor = require('./methods/monitor');
const Paypal = require('./methods/paypal');

// Limited Run now with Paypal support along with new methods dir

// METHODS KEY:
// cXXX = checkout.js, Any Big Cartel cart/checkout endpoints
// pXXX = paypal.js, Paypal methods and flow
// monitor & preloadItem = monitor.js

const Utils = require('../utils/utils');
const { Mixin } = require('ts-mixer');

const	LRNormal = class LRNormal extends Mixin(Utils, Checkout, Monitor, Paypal) {
    constructor(group, name, link, inputType, pInput, oInput, randomOos, quantity, profile, proxyList, port) {
        super(group, name, link, inputType, pInput, oInput, randomOos, quantity, profile, proxyList, 'Limited Run', 'Normal')
        this.fullPath = resolve(`${__filename}`)
        this.port = port,
        this.enabled = false
        this.data = {
            retry: 0
        }
        this.config = {}
    }

	async flow () {
		try {
            // Config
            this.enabled = true
            this.config.userAgent = this.userAgent()
            this.config.cookieJar = new CookieJar();
            this.config.agent = await this.proxyGot()
            this.settings = await this.getSettings()
            this.data.stripePaymentAgent = await this.getStripePaymentAgent();

            // Starting flow

            // Generate a cart
            this.data.cart = await this.cGenCart()

            // Monitor
            this.data.item = await this.monitor()

            if (this.enabled) {
                // ATC
                this.data.cart = await this.cAdd()
                
                // Shipping
                const shippingMethod = await this.cGetShipping()
               
                this.data.cart = await this.cSubmitShipping(shippingMethod)

                

                // Create checkout
                this.data.checkout_url = await this.cCreateCheckout()

                if (this.enabled) {

                    // Send to monitor feed
                    this.feed({
                        platform: `${this.name}: Limited Run`,
                        site: this.link.split('/')[2],
                        type: 'Checkout',
                        title: Object.values(this.data.cart.items)[0].name,
                        link: this.data.checkout_url,
                        price: parseFloat(this.data.cart.total_price).toFixed(2),
                        image: Object.values(this.data.cart.items)[0].image_url
                            ? Object.values(this.data.cart.items)[0].image_url
                            : 'https://media.discordapp.net/attachments/903009397637283920/923089289296871504/Frame_4stackd-icon-bg-1.png',
                        variants: [`${this.data.checkout_url}`]
                    });

                    // Submit profile
                    const profile = await this.cProfile()

                    // Get payment method: paypal or stripe
                    this.data.paymentMethod = await this.cGetPaymentMethod()
                    if (this.data.paymentMethod === 'paypal') {
                        this.data.paypalToken = await this.cCreatePayment()
                        await this.flowPaypal()
                    } else {
                        await this.flowCharge()
                    }

                }
            }
		} catch (error) {
			await this.log(`000 - Error in flow: ${error.message}`, 'error')
			this.enabled = false
		}
	}

    // Stripe checkout flow
    async flowCharge () {
        if (this.enabled && this.data.retry < 3) {
            try {
                // Get payment token
                this.data.stripe = await this.cCreatePayment()

                // Submit payment
                const paymentInfo = await this.submitStripe()

                if (this.enabled && !paymentInfo.error) {
                    // Finalize order
                    await this.cFinalize()

                    // Submit order
                    await this.cSubmitCheckout()

                    // Check order
                    const order = await this.cCheckOrder()

                    if (this.enabled && order.status === 'COMPLETED') {
                        await this.log(`000 - Success: $${order.total_price} order #${order.id} ($${order.total_price})`, 'success')
                        const cartedItem = Object.values(order.items)[0];
                        await this.staffLog(
                            this.link,
                            cartedItem.name,
                            order.total_price.toFixed(2),
                            Object.values(this.data.cart.items)[0].image_url
                                ? Object.values(this.data.cart.items)[0].image_url
                                : 'https://media.discordapp.net/attachments/903009397637283920/923089289296871504/Frame_4stackd-icon-bg-1.png'
                        );
                        await this.userWebhook(
                            this.link,
                            cartedItem.name,
                            order.total_price.toFixed(2),
                            Object.values(this.data.cart.items)[0].image_url
                                ? Object.values(this.data.cart.items)[0].image_url
                                : 'https://media.discordapp.net/attachments/903009397637283920/923089289296871504/Frame_4stackd-icon-bg-1.png'
                        );
                    } else if (this.enabled) {
                        await this.log('000 - Checkout failed, retrying...', 'error')
                        await this.delay(1000)
                        this.data.retry++
                        return await this.flowCharge()
                    }
                } else if (this.enabled) {
                    await this.delay(1000)
                    this.data.retry++
                    return await this.flowCharge()
                }
            } catch (error) {
                await this.log(`000 - Error in charge flow: ${error.message}`, 'error')
                this.enabled = false
            }
        } else if (this.enabled) {
            await this.log(`000 - Card declined 3x, stopping task`, 'error')
            this.enabled = false
        }
    }

    // Paypal checkout flow
    async flowPaypal () {
        if (this.enabled) {
            try {
                this.data.paypal = {}

                // Submit info to paypal
                const ppToken = await this.pSubmit()

                // Authorize paypal
                const returnUrl = await this.pCapture(ppToken)
                
                // Connect Paypal payment token to checkout
                await this.cReturnPaypal(returnUrl)
                
                // Finalize order
                await this.cFinalize()

                // Submit order
                await this.cSubmitCheckout()

                // Check order
                const order = await this.cCheckOrder()

                if (this.enabled && order.status === 'COMPLETED') {
                    await this.log(`000 - Success: $${order.total_price} order #${order.id} ($${order.total_price})`, 'success')
                    const cartedItem = Object.values(order.items)[0];
					await this.staffLog(
						this.link,
						cartedItem.name,
						order.total_price.toFixed(2),
						Object.values(this.data.cart.items)[0].image_url
							? Object.values(this.data.cart.items)[0].image_url
							: 'https://media.discordapp.net/attachments/903009397637283920/923089289296871504/Frame_4stackd-icon-bg-1.png'
					);
					await this.userWebhook(
						this.link,
						cartedItem.name,
						order.total_price.toFixed(2),
						Object.values(this.data.cart.items)[0].image_url
							? Object.values(this.data.cart.items)[0].image_url
							: 'https://media.discordapp.net/attachments/903009397637283920/923089289296871504/Frame_4stackd-icon-bg-1.png'
					);
                } else if (this.enabled) {
                    await this.log('000 - Checkout failed', 'error')
                    this.enabled = false
                }
            } catch (error) {
                await this.log(`000 - Error in Paypal flow: ${error.message}`, 'error')
                this.enabled = false
            }
        }
    };

    async flowSuccess() {
        if (this.enabled) {
            try {
                
            } catch (error) {
                await this.log(`000 - Error in success flow: ${error.message}`, 'error')
                this.enabled = false
            }
        }
    }

}

module.exports.flow = async function flow(obj) {
	const instance = new LRNormal(
		obj.group,
		obj.name,
		obj.link,
		obj.inputType,
		obj.pInput,
		obj.oInput,
		obj.randomOos,
		obj.quantity,
		obj.profile,
		obj.proxyList,
		obj.port
	);
	await instance.flow();
};

module.exports.LRNormal = LRNormal;

// !TODO
// [ ] Why does proxy messaging keep happening?
// [x] Stripe checkout
// [x] Stripe priority over PP
