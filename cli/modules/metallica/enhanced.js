// Metallica Enhanced - Improved bot detection avoidance while maintaining speed
const Utils = require('../utils/utils');
const Checkout = require('./methods/enhanced-checkout');
const Misc = require('./methods/enhanced-misc');
const Login = require('./methods/enhanced-login');
const Cart = require('./methods/enhanced-cart');
const Monitor = require('./methods/enhanced-monitor');

const { Mixin } = require('ts-mixer');
const { CookieJar } = require('tough-cookie');
const { resolve } = require('path');
const got = require('got');
const qs = require('qs');
const { parse } = require('node-html-parser');

const MEEnhanced = class MEEnhanced extends Mixin(Utils, Checkout, Misc, Login, Cart, Monitor) {

    constructor(group, name, inputType, pInput, quantity, profile, proxyList, port) {
        super(group, name, 'https://www.metallica.com', inputType, pInput, null, null, quantity, profile, proxyList, 'Metallica', 'Enhanced')
        this.fullPath = resolve(`${__filename}`)
        this.port = port,
        this.enabled = false
        this.data = {
            csrf: {},
            keys: {},
            cartEmpty: false
        }
        this.retry = 0
        this.config = {}
    }

    // Initialize session with consistent browser fingerprint
    async initSession() {
        // Select a single user agent for the entire session
        this.config.userAgent = this.userAgent();

        // Generate a realistic browser fingerprint
        this.config.browserProps = {
            screenWidth: [1366, 1440, 1920][Math.floor(Math.random() * 3)],
            screenHeight: [768, 900, 1080][Math.floor(Math.random() * 3)],
            colorDepth: 24,
            platform: Math.random() > 0.5 ? 'Win32' : 'MacIntel',
            language: 'en-US'
        };

        // Set consistent headers for the session
        this.config.acceptHeader = 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8';
        this.config.acceptLanguage = 'en-US,en;q=0.9';

        // Initialize cookie jar with consistent settings
        this.config.cookieJar = new CookieJar();

        // Pre-populate with some common cookies that browsers typically have
        await this.config.cookieJar.setCookie(
            `dwsid=${this.randomStr(20)}; path=/; domain=.metallica.com`,
            'https://www.metallica.com'
        );

        await this.config.cookieJar.setCookie(
            `dwanonymous_${this.randomStr(8)}=${this.randomStr(20)}; path=/; domain=.metallica.com`,
            'https://www.metallica.com'
        );

        // Get a proxy at the start of the session and stick with it
        this.config.agent = await this.proxyGot();
        this.log(`Session initialized with consistent browser fingerprint`);
    }

    // Enhanced headers for requests
    async getEnhancedHeaders(baseHeaders = {}) {
        return {
            'User-Agent': this.config.userAgent,
            'Accept': this.config.acceptHeader || 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': this.config.acceptLanguage || 'en-US,en;q=0.9',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0',
            'sec-ch-ua': '"Google Chrome";v="113", "Chromium";v="113"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': `"${this.config.browserProps?.platform || 'Windows'}"`,
            ...baseHeaders
        };
    }

    // Main flow method
    async flow() {
        try {
            // Config and initialization
            this.enabled = true
            await this.initSession();
            this.settings = await this.getSettings()

            // Login
            const cookieId = await this.randomStr()
            const loginPage = await this.loginGet()
            const loggedIn = await this.loginPost(loginPage)

            if (this.enabled) {
                await got.post('http://localhost:3030/cookies', {
                    responseType: 'json',
                    json: {
                        id: cookieId,
                        cookies: JSON.stringify(await this.config.cookieJar.getCookies('https://www.metallica.com'))
                    }
                })

                // Add to cart
                this.data.cart = await this.cartAdd()
                if (this.enabled) {
                    // Keep the same timing as original
                    await new Promise(r => setTimeout(r, 1))
                    await got.post('http://localhost:3030/monitor', {
                        responseType: 'json',
                        json: {
                            platform: `Metallica - ${this.name} - ${this.profile.shippingAddress.email}`,
                            site: 'Metallica',
                            type: 'Checkout',
                            title: this.data.cart.cart[0].name,
                            link: this.data.cart.cart[0].url,
                            price: (this.data.cart.cart[0].price).toFixed(2),
                            image: 'https://cdn.discordapp.com/attachments/903009397637283920/923088597723279360/Frame_4stackd-icon-bg.png',
                            variants: [`https://www.metallica.com/shipping`],
                            cookies: cookieId
                        }
                    })

                    // Checkout steps - maintaining original timing
                    await new Promise(r => setTimeout(r, 1))
                    this.data.csrf.cart = await this.cartGet()
                    await new Promise(r => setTimeout(r, 1))
                    this.data.csrf.checkout = await this.checkoutCreate()
                    await new Promise(r => setTimeout(r, 1))
                    const lowestShippingMethod = await this.getAvailableShippingCodes()
                    await new Promise(r => setTimeout(r, 1))
                    await this.checkoutGetShipping(lowestShippingMethod)
                    await new Promise(r => setTimeout(r, 1))
                    this.data.csrf.shipping = await this.checkoutSubmitShipping(lowestShippingMethod)

                    // Submit checkout flow
                    await this.flowComplete()
                } else {
                    await this.log(`000 - Task stopped`, 'error')
                }
            } else {
                await this.log(`000 - Task stopped`, 'error')
            }
        } catch (error) {
            await this.log(`000 - Error in flow: ${error.message}`, 'error')
            this.enabled = false
        }
    }

    // Complete checkout flow
    async flowComplete() {
        if (this.enabled && this.retry < 3) {
            try {
                // Submit billing
                this.data.csrf.billing = await this.checkoutSubmitBilling()
                await new Promise(r => setTimeout(r, 1))

                // Submit order
                const submitOrder = await this.checkoutSubmitOrder()

                // submit order result logic
                // Success
                if (this.enabled && submitOrder && submitOrder.status === 'success') {
                    await this.staffLog('https://www.metallica.com',
                        this.data.cart.cart[0].name,
                        parseFloat(this.data.cart.cart[0].price).toFixed(2),
                        'https://cdn.discordapp.com/attachments/714346599735754762/843321954688565278/blue.png'
                    )
                    await this.successAnalytics('https://www.metallica.com/on/demandware.store/Sites-Metallica-Site/default/Login-LoginForm?scope=', 'Metallica', submitOrder.order_number)
                    await this.userWebhook(`https://www.metallica.com`, this.data.cart.cart[0].name, parseFloat(this.data.cart.cart[0].price).toFixed(2), `https://cdn.discordapp.com/attachments/903009397637283920/923088597723279360/Frame_4stackd-icon-bg.png`)

                // Fail
                } else if (this.enabled && submitOrder && submitOrder.status === 'fail') {
                    // Figure out what kind of error it was
                    if (submitOrder.message.includes('Please review your payment settings and try again')) {
                        await this.log('000 - Card decline', 'error')
                        this.retry++
                        await new Promise(r => setTimeout(r, 1000))
                        return await this.flowComplete()
                    } else if (submitOrder.message) {
                        await this.log('000 - Submitting order error unknown. retrying...', 'error')
                        await new Promise(r => setTimeout(r, 1000))
                        return await this.flowComplete()
                    } else {
                        await this.log('000 - Submitting order error unknown. retrying...', 'error')
                        this.retry++
                        await new Promise(r => setTimeout(r, 1000))
                        return await this.flowComplete()
                    }
                }
            } catch (error) {
                await this.log(`000 - Error in complete checkout flow: ${error.message}`, 'error')
                this.enabled = false
            }
        } else {
            await this.log(`000 - Task stopped`, 'error')
        }
    }
}

module.exports.flow = async function flow(obj) {
    const instance = new MEEnhanced(obj.group, obj.name, obj.inputType, obj.pInput, obj.quantity, obj.profile, obj.proxyList, obj.port);
    await instance.flow();
}
