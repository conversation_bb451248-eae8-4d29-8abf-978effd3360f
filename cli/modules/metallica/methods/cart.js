// Cart methods
const got = require('got')
const { parse } = require('node-html-parser')
const qs = require('qs')

module.exports = class Cart {
    constructor (group, name, link, inputType, pInput, oInput, randomOos, quantity, profile, proxyList, platform, mode) {
        this.group = group;
		this.name = name;
		this.link = link;
		this.inputType = inputType;
		this.pInput = pInput;
		this.oInput = oInput;
		this.randomOos = randomOos;
		this.quantity = quantity;
		this.profile = profile;
		this.proxyList = proxyList;
		this.platform = platform;
		this.mode = mode;
    }

    async cartGet(clear = false) {
        if (this.enabled) {
            try {
                const options = {
                    ...await this.miscOptions({
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8', 
                        'Accept-Language': 'en-US,en;q=0.5', 
                        'Referer': `https://www.metallica.com/store/${this.pInput}.html?cgid=store`, 
                        'DNT': '1', 
                        'Connection': 'keep-alive', 
                        'Upgrade-Insecure-Requests': '1', 
                        'Sec-Fetch-Dest': 'document', 
                        'Sec-Fetch-Mode': 'navigate', 
                        'Sec-Fetch-Site': 'same-origin', 
                        'Sec-Fetch-User': '?1', 
                        'TE': 'trailers'
                    }),
                    url: 'https://www.metallica.com/cart/',
                    method: 'GET',
                    responseType: 'text'
                }

                const response = await got(options)
                const root = await parse(response.body)

                if (root.querySelector('[name="csrf_token"]')) {
                    const csrf = await root.querySelector('[name="csrf_token"]').attrs.value
                    if (root.querySelector('.notavailable')) {
                        if (root.querySelector('.notavailable').innerText.includes('Out of Stock')) {
                            throw new Error('Item in cart is OOS.')
                        }
                        // Is there an else here?
                    } else {
                        this.data.cartEmpty = false
                        await this.log(`${response.statusCode} - Received cart`)
                    }
                    return csrf
                } else {
                    this.data.cartEmpty = true
                }
            } catch (error) {
                await this.log(`${error.response ? error.response.statusCode : '000'} - Error retreiving cart: ${error.message}`, 'error')
                if (error.response) {
                    if (error.response.statusCode === 403 || error.response.statusCode === 429 || error.response.statusCode === 430) {
                        await this.proxyGot()
                    }
                    await new Promise(r => setTimeout(r, 1000))
                    return await this.cartGet()

                } else if (error.message === 'Item in cart is OOS.') {
                    if (clear) {
                        this.config.agent = await this.cartClear()
                    } else {
                        await new Promise(r => setTimeout(r, 1000))
                        return await this.cartGet()

                    }
                } else {
                    if (error.message.includes('tunneling socket could not be established')) {
                        this.config.agent = await this.proxyGot()
                        await this.delay()
                        return await this.cartGet(clear)
                    } else {
                        this.enabled = false
                    }
                }
            }
        }
    };

    async cartClear() {
        if (this.enabled && !this.data.cartEmpty) {
            try {
                const options = {
                    ... await this.miscOptions({
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8', 
                        'Accept-Language': 'en-US,en;q=0.5', 
                        'Referer': 'https://www.metallica.com/cart/', 
                        'Content-Type': 'application/x-www-form-urlencoded', 
                        'Origin': 'https://www.metallica.com', 
                        'DNT': '1', 
                        'Connection': 'keep-alive', 
                        'Upgrade-Insecure-Requests': '1', 
                        'Sec-Fetch-Dest': 'document', 
                        'Sec-Fetch-Mode': 'navigate', 
                        'Sec-Fetch-Site': 'same-origin', 
                        'Sec-Fetch-User': '?1', 
                        'TE': 'trailers'
                    }),
                    url: 'https://www.metallica.com/on/demandware.store/Sites-Metallica-Site/default/Cart-SubmitForm',
                    method: 'POST',
                    responseType: 'text',
                    body: qs.stringify({
                        'dwfrm_cart_shipments_i0_items_i0_quantity': '1',
                        'dwfrm_cart_shipments_i0_items_i0_quantity': '1',
                        // 'dwfrm_cart_shipments_i0_items_i0_quantity': '1',
                        'dwfrm_cart_shipments_i0_items_i0_deleteProduct': 'Remove',
                        'dwfrm_cart_couponCode': '',
                        'dwfrm_cart_updateCart': 'dwfrm_cart_updateCart',
                        'csrf_token': this.data.csrf.cart
                    }),
                }
                const response = await got(options)
                const root = parse(response.body)
                // const csrf = root.querySelector('[name="csrf_token"]').attrs.value
                if (response.body.includes('<h1>Your Shopping Cart is Empty</h1>')) {
                    this.data.cartEmpty = true
                }
                await this.log(`${response.statusCode} - ${this.data.cartEmpty ? 'Cart cleared' : 'Cart may not be cleared'}`)
                
            } catch(error) {
                await this.log(`${error.response ? error.response.statusCode : '000'} - Error clearing cart: ${error.message}`, 'error')
                if (error.response) {
                    if (error.response.statusCode === 403 || error.response.statusCode === 429 || error.response.statusCode === 430) {
                        this.config.agent = await this.proxyGot()
                    }
                    await new Promise(r => setTimeout(r, 1000))
                    return await this.cartClear()

                } else {
                    if (error.message.includes('tunneling socket could not be established')) {
                        this.config.agent = await this.proxyGot()
                        await this.delay()
                        return await this.cartClear()
                    } else {
                        this.enabled = false
                    }
                }
            }
        }
    };

    async cartAdd() {
        if (this.enabled) {
            try {
                const options = {
                    ... await this.miscOptions({
                        'Accept': '*/*', 
                        'Accept-Language': 'en-US,en;q=0.5', 
                        'Referer': 'https://www.metallica.com/store/?srule=newest&viewAll=true', 
                        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8', 
                        'X-Requested-With': 'XMLHttpRequest', 
                        'Origin': 'https://www.metallica.com', 
                        'DNT': '1', 
                        'Connection': 'keep-alive', 
                        'Sec-Fetch-Dest': 'empty', 
                        'Sec-Fetch-Mode': 'cors', 
                        'Sec-Fetch-Site': 'same-origin', 
                        'TE': 'trailers'
                    }),
                    url: 'https://www.metallica.com/on/demandware.store/Sites-Metallica-Site/default/Cart-AddProduct?format=ajax',
                    method: 'POST',
                    responseType: 'text',
                    body: qs.stringify({
                        // "maxQtyAvailable": "25",
                        // "purchaseLimit": "25.0",
                        // "totalQty": "36132",
                        "Quantity": `${this.quantity}`, // May need to remove this as I dont think it's the proper way of quantity
                        "uuid": "",
                        "cartAction": "update",
                        "variantSelected": true,
                        "pid": `${this.pInput}`,
                        "cgid": "store",
                        // "pageInstanceId": "product"
                    })
                }

                const response = await got(options)

                const root = await parse(response.body)

                if (root.querySelector('.minicart-quantity')) {
                    if (root.querySelector('.minicart-quantity').innerText === '0') {
                        throw new Error(`${this.pInput} is OOS`)

                    }
                } else {
                    throw new Error(response.body)
                }

                // Find something better than this
                const cart = await JSON.parse(response.body.split("_etmc.push(['trackCart', ")[1].split(']);')[0])
                await this.log(`${response.statusCode} - Added: ${cart.cart[0].name}`)
                return cart
            } catch (error) {
                await this.log(`${error.response ? error.response.statusCode : '000'} - Error adding to cart: ${error.message}`, 'error')
                if (error.response) {
                    if (error.response.statusCode === 403 || error.response.statusCode === 429 || error.response.statusCode === 430) {
                        this.config.agent = await this.proxyGot()
                    }
                    await new Promise(r => setTimeout(r, 1000))
                    return await this.cartAdd()

                } else {
                    if (error.message.includes('tunneling socket could not be established')) {
                        this.config.agent = await this.proxyGot()
                        await this.delay()
                        return await this.cartAdd()
                    } else {
                        this.enabled = false
                    }
                }
            }
        }
    };

}