// Checkout and cart api

const got = require('got')
const qs = require('qs')
const { parse } = require('node-html-parser')

module.exports = class Checkout {
    constructor(group, name, link, inputType, pInput, oInput, randomOos, quantity, profile, proxyList, platform, mode) {
        this.group = group;
        this.name = name;
        this.link = link;
        this.inputType = inputType;
        this.pInput = pInput;
        this.oInput = oInput;
        this.randomOos = randomOos;
        this.quantity = quantity;
        this.profile = profile;
        this.proxyList = proxyList;
        this.platform = platform;
        this.mode = mode;
    }

    // Create a checkout
    async checkoutCreate() {
        if (this.enabled) {
            try {
                const options = {
                    ...await this.miscOptions({
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                        'Accept-Language': 'en-US,en;q=0.5',
                        'Referer': 'https://www.metallica.com/cart/',
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'Origin': 'https://www.metallica.com',
                        'DNT': '1',
                        'Connection': 'keep-alive',
                        'Upgrade-Insecure-Requests': '1',
                        'Sec-Fetch-Dest': 'document',
                        'Sec-Fetch-Mode': 'navigate',
                        'Sec-Fetch-Site': 'same-origin',
                        'Sec-Fetch-User': '?1',
                        'TE': 'trailers'
                    }),
                    url: 'https://www.metallica.com/on/demandware.store/Sites-Metallica-Site/default/Cart-SubmitForm',
                    method: 'POST',
                    responseType: 'text',
                    body: qs.stringify({
                        dwfrm_cart_checkoutCart: "Checkout"
                    })
                }

                const response = await got(options)

                const root = await parse(response.body)
                const csrf = await root.querySelector('[name="csrf_token"]').attrs.value
                await this.log(`${response.statusCode} - Generated checkout`)
                return csrf
            } catch (error) {
                await this.log(`${error.response ? error.response.statusCode : '000'} - Error generating checkout: ${error.message}`, 'error')
                if (error.response) {
                    if (error.response.statusCode === 403 || error.response.statusCode === 429 || error.response.statusCode === 430) {
                        this.config.agent = await this.proxyGot()
                    }
                    await new Promise(r => setTimeout(r, 1000))
                    return await this.checkoutCreate()
                } else {
                    if (error.message.includes('tunneling socket could not be established')) {
                        this.config.agent = await this.proxyGot()
                        await this.delay()
                        return await this.checkoutCreate()
                    } else {
                        this.enabled = false
                    }
                }
            }
        }
    };

    async getAvailableShippingCodes() { 
        if(this.enabled) {
            try {
                const options = {
                    ...await this.miscOptions({
                        'Accept': 'application/json, text/javascript, */*; q=0.01',
                        'Accept-Language': 'en-US,en;q=0.5',
                        'Referer': 'https://www.metallica.com/shipping/',
                        'X-Requested-With': 'XMLHttpRequest',
                        'DNT': '1',
                        'Connection': 'keep-alive',
                        'Sec-Fetch-Dest': 'empty',
                        'Sec-Fetch-Mode': 'cors',
                        'Sec-Fetch-Site': 'same-origin',
                        'TE': 'trailers'
                    }),
                    responseType: 'json',
                    method: 'GET',
                    url: `https://www.metallica.com/on/demandware.store/Sites-Metallica-Site/default/COShipping-GetApplicableShippingMethodsJSON?address1=${this.profile.shippingAddress.line1}&address2=${this.profile.shippingAddress.line2}&countryCode=us&postalCode=${this.profile.shippingAddress.postCode}&city=${this.profile.shippingAddress.city}&stateCodeRequired=true&stateCode=${this.profile.shippingAddress.state}`
                }
                const response = await got(options)
                const availableShippingRates = response.body 
                // returning first index should be the lowest available rate
                return availableShippingRates[0]
            } catch (error) {
                await this.log(`${error.response ? error.response.statusCode : '000'} - Error retreiving available shipping codes: ${error.message}`, 'error')
                if (error.response) {
                    if (error.response.statusCode === 403 || error.response.statusCode === 429 || error.response.statusCode === 430) {
                        this.config.agent = await this.proxyGot()
                    }
                    await new Promise(r => setTimeout(r, 1000))
                    return await this.getAvailableShippingCodes()
                } else {
                    if (error.message.includes('tunneling socket could not be established')) {
                        this.config.agent = await this.proxyGot()
                        await this.delay()
                        return await this.getAvailableShippingCodes()
                    } else {
                        this.enabled = false
                    }
                } 
            }
        }
    }

    // Retreive shipping method
    async checkoutGetShipping(shippingMethod) {
        if (this.enabled) {
            try {
                //&shippingMethodID=shqcustom-ups_mail_innovations&customFlag=
                const options = {
                    ...await this.miscOptions({
                        'Accept': 'application/json, text/javascript, */*; q=0.01',
                        'Accept-Language': 'en-US,en;q=0.5',
                        'Referer': 'https://www.metallica.com/shipping/',
                        'X-Requested-With': 'XMLHttpRequest',
                        'DNT': '1',
                        'Connection': 'keep-alive',
                        'Sec-Fetch-Dest': 'empty',
                        'Sec-Fetch-Mode': 'cors',
                        'Sec-Fetch-Site': 'same-origin',
                        'TE': 'trailers'
                    }),
                    responseType: 'json',
                    method: 'GET',
                    url: `https://www.metallica.com/on/demandware.store/Sites-Metallica-Site/default/COShipping-SelectShippingMethod?address1=${this.profile.shippingAddress.line1}&address2=${this.profile.shippingAddress.line2}&countryCode=us&postalCode=${this.profile.shippingAddress.postCode}&city=${this.profile.shippingAddress.city}&stateCodeRequired=true&stateCode=${this.profile.shippingAddress.state}&shippingMethodID=${shippingMethod}&customFlag=`
                }

                const response = await got(options)

                await this.log(`${response.statusCode} - Shipping method: ${response.body.shippingMethodID}`)
                return response.body.shippingMethodID
            } catch (error) {
                await this.log(`${error.response ? error.response.statusCode : '000'} - Error retreiving shipping method: ${error.message}`, 'error')
                if (error.response) {
                    if (error.response.statusCode === 403 || error.response.statusCode === 429 || error.response.statusCode === 430) {
                        this.config.agent = await this.proxyGot()
                    }
                    await new Promise(r => setTimeout(r, 1000))
                    return await this.checkoutGetShipping()
                } else {
                    if (error.message.includes('tunneling socket could not be established')) {
                        this.config.agent = await this.proxyGot()
                        await this.delay()
                        return await this.checkoutGetShipping()
                    } else {
                        this.enabled = false
                    }
                }
            }
        }
    };

    // Submit shipping
    async checkoutSubmitShipping(shipping) {
        if (this.enabled) {
            try {
                const options = {
                    ... await this.miscOptions({
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                        'Accept-Language': 'en-US,en;q=0.5',
                        'Referer': 'https://www.metallica.com/shipping/',
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'Origin': 'https://www.metallica.com',
                        'DNT': '1',
                        'Connection': 'keep-alive',
                        'Upgrade-Insecure-Requests': '1',
                        'Sec-Fetch-Dest': 'document',
                        'Sec-Fetch-Mode': 'navigate',
                        'Sec-Fetch-Site': 'same-origin',
                        'Sec-Fetch-User': '?1',
                        'TE': 'trailers'
                    }),
                    method: 'POST',
                    url: 'https://www.metallica.com/billing-checkout/',
                    responseType: 'text',
                    body: qs.stringify({
                        'ups_isAddressUpdated': 'true',
                        'dwfrm_singleshipping_shippingAddress_addressFields_firstName': `${this.profile.shippingAddress.name.split(' ')[0]}`,
                        'dwfrm_singleshipping_shippingAddress_addressFields_lastName': `${this.profile.shippingAddress.name.split(' ')[1]}`,
                        'dwfrm_singleshipping_shippingAddress_addressFields_address1': `${this.profile.shippingAddress.line1}`,
                        'dwfrm_singleshipping_shippingAddress_addressFields_address2': `${this.profile.shippingAddress.line2}`,
                        'dwfrm_singleshipping_shippingAddress_addressFields_city': `${this.profile.shippingAddress.city}`,
                        'dwfrm_singleshipping_shippingAddress_addressFields_states_state': `${this.profile.shippingAddress.state}`,
                        'dwfrm_singleshipping_shippingAddress_addressFields_castates_castate': '',
                        'dwfrm_singleshipping_shippingAddress_addressFields_otherstates': '',
                        'dwfrm_singleshipping_shippingAddress_addressFields_country': 'us',
                        'dwfrm_singleshipping_shippingAddress_addressFields_postal': `${this.profile.shippingAddress.postCode}`,
                        'dwfrm_singleshipping_shippingAddress_addressFields_phone': `${this.profile.shippingAddress.phone}`,
                        'dwfrm_singleshipping_shippingAddress_useAsBillingAddress': 'true',
                        'dwfrm_singleshipping_shippingAddress_giftMessage': '',
                        'dwfrm_singleshipping_shippingAddress_shippingMethodID': shipping,
                        'dwfrm_singleshipping_shippingAddress_save': 'Continue to Billing',
                        'csrf_token': this.data.csrf.checkout
                    }),
                }

                const response = await got(options)
                const root = await parse(response.body)
                const csrf = await root.querySelector('[name="csrf_token"]').attrs.value
                await this.log(`${response.statusCode} - Submitted shipping`)
                return csrf
            } catch (error) {
                await this.log(`${error.response ? error.response.statusCode : '000'} - Error submitting shipping: ${error.message}`, 'error')
                if (error.response) {
                    if (error.response.statusCode === 403 || error.response.statusCode === 429 || error.response.statusCode === 430) {
                        this.config.agent = await this.proxyGot()
                    }
                    await new Promise(r => setTimeout(r, 1000))
                    return await this.checkoutSubmitShipping(shipping)
                } else {
                    if (error.message.includes('tunneling socket could not be established')) {
                        this.config.agent = await this.proxyGot()
                        await this.delay()
                        return await this.checkoutSubmitBilling(shipping)
                    } else {
                        this.enabled = false
                    }
                }
            }
        }
    };

    // Posts email and card info
    async checkoutSubmitBilling() {
        if (this.enabled) {
            try {
                let cardType
                switch (this.profile.paymentDetails.cardType) {
                    case 'AmericanExpress':
                        cardType = 'Amex'
                        break;
                    default:
                        cardType = this.profile.paymentDetails.cardType
                }

                const options = {
                    ...await this.miscOptions({
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                        'Accept-Language': 'en-US,en;q=0.5',
                        'Referer': 'https://www.metallica.com/billing/',
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'Origin': 'https://www.metallica.com',
                        'DNT': '1',
                        'Connection': 'keep-alive',
                        'Upgrade-Insecure-Requests': '1',
                        'Sec-Fetch-Dest': 'document',
                        'Sec-Fetch-Mode': 'navigate',
                        'Sec-Fetch-Site': 'same-origin',
                        'Sec-Fetch-User': '?1',
                        'TE': 'trailers'
                    }),
                    responseType: 'text',
                    body: qs.stringify({
                        'dwfrm_billing_paymentMethods_selectedPaymentMethodID': 'CREDIT_CARD',
                        'dwfrm_billing_paymentMethods_bml_year': '',
                        'dwfrm_billing_paymentMethods_bml_month': '',
                        'dwfrm_billing_paymentMethods_bml_day': '',
                        'dwfrm_billing_paymentMethods_bml_ssn': '',
                        'dwfrm_billing_paymentMethods_creditCard_owner': `${this.profile.billingAddress.name}`,
                        'dwfrm_billing_paymentMethods_creditCard_type': `${cardType}`,
                        'dwfrm_billing_paymentMethods_creditCard_selectedCardID': '',
                        'dwfrm_billing_paymentMethods_creditCard_number': `${this.profile.paymentDetails.cardNumber}`,
                        'dwfrm_billing_paymentMethods_creditCard_expiration_month': `${this.profile.paymentDetails.cardExpMonth}`,
                        'dwfrm_billing_paymentMethods_creditCard_expiration_year': `${this.profile.paymentDetails.cardExpYear}`,
                        'dwfrm_billing_paymentMethods_creditCard_cvn': `${this.profile.paymentDetails.cardCvv}`,
                        'dwfrm_billing_paymentMethods_creditCard_selectedCardID': '',
                        'dwfrm_billing_paymentMethods_creditCard_selectedCardID': '',
                        'csrf_token': this.data.csrf.shipping,
                        'dwfrm_billing_giftCertCode': '',
                        'dwfrm_billing_couponCode': '',
                        'dwfrm_billing_save': 'true',
                        'dwfrm_billing_billingAddress_useAsBillingAddress': 'true',
                        'dwfrm_billing_billingAddress_addressFields_firstName': `${this.profile.billingAddress.name.split(' ')[0]}`,
                        'dwfrm_billing_billingAddress_addressFields_lastName': `${this.profile.billingAddress.name.split(' ')[1]}`,
                        'dwfrm_billing_billingAddress_addressFields_address1': `${this.profile.billingAddress.line1}`,
                        'dwfrm_billing_billingAddress_addressFields_address2': `${this.profile.billingAddress.line2}`,
                        'dwfrm_billing_billingAddress_addressFields_city': `${this.profile.billingAddress.city}`,
                        'dwfrm_billing_billingAddress_addressFields_postal': `${this.profile.billingAddress.postCode}`,
                        'dwfrm_billing_billingAddress_addressFields_states_state': `${this.profile.billingAddress.state}`,
                        'dwfrm_billing_billingAddress_addressFields_country': 'us',
                        'dwfrm_billing_billingAddress_addressFields_castates_castate': '',
                        'dwfrm_billing_billingAddress_addressFields_otherstates': '',
                        'dwfrm_billing_billingAddress_addressFields_phone': `${this.profile.billingAddress.phone}`,
                        'dwfrm_billing_billingAddress_email_emailAddress': `${this.profile.billingAddress.email}`,
                        'pp_ready': '0',
                        'dwfrm_billing_save': 'Continue to Place Order'
                    }),
                    method: 'POST',
                    url: 'https://www.metallica.com/placeorder/'
                }

                const response = await got(options)
                const root = await parse(response.body)
                const csrf = await root.querySelector('[name="csrf_token"]').attrs.value
                await this.log(`${response.statusCode} - Submitted billing`)
                return csrf
            } catch (error) {
                await this.log(`${error.response ? error.response.statusCode : '000'} - Error submitting billing: ${error.message}`, 'error')
                if (error.response) {
                    if (error.response.statusCode === 403 || error.response.statusCode === 429 || error.response.statusCode === 430) {
                        this.config.agent = await this.proxyGot()
                    }
                    await new Promise(r => setTimeout(r, 1000))
                    return await this.checkoutSubmitBilling()
                } else {
                    if (error.message.includes('tunneling socket could not be established')) {
                        this.config.agent = await this.proxyGot()
                        await this.delay()
                        return await this.checkoutSubmitBilling()
                    } else {
                        this.enabled = false
                    }
                }
            }
        }
    };

    // Submit order
    async checkoutSubmitOrder() {
        if (this.enabled) {
            try {
                await this.log('000 - Submitting order')
                const options = {
                    ... await this.miscOptions({
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                        'Accept-Language': 'en-US,en;q=0.5',
                        'Referer': 'https://www.metallica.com/placeorder/',
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'Origin': 'https://www.metallica.com',
                        'DNT': '1',
                        'Connection': 'keep-alive',
                        'Upgrade-Insecure-Requests': '1',
                        'Sec-Fetch-Dest': 'document',
                        'Sec-Fetch-Mode': 'navigate',
                        'Sec-Fetch-Site': 'same-origin',
                        'Sec-Fetch-User': '?1',
                        'TE': 'trailers'
                    }),
                    responseType: 'text',
                    body: qs.stringify({
                        'submit': 'Place Order',
                        'csrf_token': this.data.csrf.billing
                    }),
                    url: 'https://www.metallica.com/orderconfirmation/',
                    method: 'POST'

                }

                const response = await got(options)

                // Parse
                const root = await parse(response.body)
                const csrf = await root.querySelector('[name="csrf_token"]').attrs.value

                // Error, success, other
                if (root.querySelector('.error-form')) {
                    const message = root.querySelector('.error-form').innerText
                    await this.log(`${response.statusCode} - Failed submitting order: ${message}`, 'prompt')
                    return {
                        status: 'fail',
                        message: message,
                        csrf: csrf
                    }
                } else if (root.querySelector('.order-confirmation-details')) {
                    const message = JSON.parse(response.body.split("_etmc.push(['trackConversion', ")[1].split(' ]);')[0]).order_number
                    await this.log(`${response.statusCode} - Check email | Order #${message}`, 'success')
                    return { status: 'success', order_number: message }
                } else {
                    await this.log(`${response.statusCode} - Submit checkout response unknown`, 'error')
                    this.enabled = false
                    return {
                        status: 'fail',
                        csrf: csrf
                    }
                }


            } catch (error) {
                await this.log(`${error.response ? error.response.statusCode : '000'} - Error submitting checkout: ${error.message}`, 'error')
                if (error.response) {
                    if (error.response.statusCode === 403 || error.response.statusCode === 429 || error.response.statusCode === 430) {
                        this.config.agent = await this.proxyGot()
                    }
                    await new Promise(r => setTimeout(r, 1000))
                    return await this.checkoutSubmitOrder()
                } else {
                    if (error.message.includes('tunneling socket could not be established')) {
                        this.config.agent = await this.proxyGot()
                        await this.delay()
                        return await this.checkoutSubmitOrder()
                    } else {
                        this.enabled = false
                    }
                }
            }
        }
    };
}