// Enhanced Cart methods with improved bot detection avoidance
const got = require('got')
const { parse } = require('node-html-parser')
const qs = require('qs')

module.exports = class EnhancedCart {
    constructor (group, name, link, inputType, pInput, oInput, randomOos, quantity, profile, proxyList, platform, mode) {
        this.group = group;
		this.name = name;
		this.link = link;
		this.inputType = inputType;
		this.pInput = pInput;
		this.oInput = oInput;
		this.randomOos = randomOos;
		this.quantity = quantity;
		this.profile = profile;
		this.proxyList = proxyList;
		this.platform = platform;
		this.mode = mode;
    }

    // Get cart with enhanced headers and browser-like behavior
    async cartGet(clear = false) {
        if (this.enabled) {
            try {
                // Add browser-like query parameters
                const timestamp = Date.now();
                const randomId = Math.floor(Math.random() * 1000000);
                
                const options = {
                    ...await this.miscOptions({
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
                        'Accept-Language': this.config.acceptLanguage,
                        'Referer': 'https://www.metallica.com/store/',
                        'DNT': '1',
                        'Connection': 'keep-alive',
                        'Upgrade-Insecure-Requests': '1',
                        'Sec-Fetch-Dest': 'document',
                        'Sec-Fetch-Mode': 'navigate',
                        'Sec-Fetch-Site': 'same-origin',
                        'Sec-Fetch-User': '?1',
                        'TE': 'trailers'
                    }),
                    url: `https://www.metallica.com/on/demandware.store/Sites-Metallica-Site/default/Cart-Show?_=${timestamp}&id=${randomId}`,
                    method: 'GET',
                    responseType: 'text'
                }
                
                const response = await got(options)
                const root = parse(response.body)
                const csrf = root.querySelector('[name="csrf_token"]')?.attrs.value
                
                if (response.body.includes('<h1>Your Shopping Cart is Empty</h1>')) {
                    this.data.cartEmpty = true
                }
                
                await this.log(`${response.statusCode} - Got cart | ${csrf}`)
                return csrf
                
            } catch (error) {
                await this.log(`${error.response ? error.response.statusCode : '000'} - Error getting cart: ${error.message}`, 'error')
                if (error.response) {
                    if (error.response.statusCode === 403 || error.response.statusCode === 429 || error.response.statusCode === 430) {
                        // Only rotate proxy on specific errors
                        this.config.agent = await this.proxyGot()
                    }
                    await new Promise(r => setTimeout(r, 1000))
                    return await this.cartGet(clear)
                } else if (error.message === 'Item in cart is OOS.') {
                    if (clear) {
                        this.config.agent = await this.cartClear()
                    } else {
                        await new Promise(r => setTimeout(r, 1000))
                        return await this.cartGet()
                    }
                } else {
                    if (error.message.includes('tunneling socket could not be established')) {
                        this.config.agent = await this.proxyGot()
                        await this.delay()
                        return await this.cartGet(clear)
                    } else {
                        this.enabled = false
                    }
                }
            }
        }
    }

    // Enhanced cart add method with browser-like behavior
    async cartAdd() {
        if (this.enabled) {
            try {
                // Add browser-like query parameters and timestamp
                const timestamp = Date.now();
                const randomId = Math.floor(Math.random() * 1000000);
                
                const options = {
                    ...await this.miscOptions({
                        'Accept': 'application/json, text/javascript, */*; q=0.01',
                        'Accept-Language': this.config.acceptLanguage,
                        'Referer': `https://www.metallica.com/store/${this.pInput}.html`,
                        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
                        'X-Requested-With': 'XMLHttpRequest',
                        'Origin': 'https://www.metallica.com',
                        'DNT': '1',
                        'Connection': 'keep-alive',
                        'Sec-Fetch-Dest': 'empty',
                        'Sec-Fetch-Mode': 'cors',
                        'Sec-Fetch-Site': 'same-origin'
                    }),
                    url: `https://www.metallica.com/on/demandware.store/Sites-Metallica-Site/default/Cart-AddProduct?format=ajax&_=${timestamp}&id=${randomId}`,
                    method: 'POST',
                    responseType: 'text',
                    body: qs.stringify({
                        "Quantity": `${this.quantity}`,
                        "uuid": "",
                        "cartAction": "update",
                        "variantSelected": true,
                        "pid": `${this.pInput}`,
                        "cgid": "store",
                        "clientDevice": "desktop",
                        "screenWidth": this.config.browserProps.screenWidth,
                        "screenHeight": this.config.browserProps.screenHeight
                    })
                }
                
                const response = await got(options)
                const root = parse(response.body)
                
                if (root.querySelector('.minicart-quantity')?.innerText === '0') {
                    throw 'OOS'
                }
                
                // Find something better than this
                const cart = await JSON.parse(response.body.split("_etmc.push(['trackCart', ")[1].split(']);')[0])
                await this.log(`${response.statusCode} - Added: ${cart.cart[0].name}`)
                return cart
                
            } catch (error) {
                await this.log(`${error.response ? error.response.statusCode : '000'} - Error adding to cart: ${error.message}`, 'error')
                if (error.response) {
                    if (error.response.statusCode === 403 || error.response.statusCode === 429 || error.response.statusCode === 430) {
                        this.config.agent = await this.proxyGot()
                    }
                    await new Promise(r => setTimeout(r, 1000))
                    return await this.cartAdd()
                } else {
                    if (error.message.includes('tunneling socket could not be established')) {
                        this.config.agent = await this.proxyGot()
                        await this.delay()
                        return await this.cartAdd()
                    } else if (error === 'OOS') {
                        await this.log(`${this.pInput} is OOS`, 'gray')
                        await new Promise(r => setTimeout(r, 1000))
                        return await this.cartAdd()
                    } else {
                        this.enabled = false
                    }
                }
            }
        }
    }

    // Enhanced cart clear method
    async cartClear() {
        if (this.enabled) {
            try {
                const options = {
                    ...await this.miscOptions({
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
                        'Accept-Language': this.config.acceptLanguage,
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'Origin': 'https://www.metallica.com',
                        'Referer': 'https://www.metallica.com/on/demandware.store/Sites-Metallica-Site/default/Cart-Show',
                        'Upgrade-Insecure-Requests': '1',
                        'Sec-Fetch-Dest': 'document',
                        'Sec-Fetch-Mode': 'navigate',
                        'Sec-Fetch-Site': 'same-origin',
                        'Sec-Fetch-User': '?1'
                    }),
                    url: 'https://www.metallica.com/on/demandware.store/Sites-Metallica-Site/default/Cart-SubmitForm',
                    method: 'POST',
                    responseType: 'text',
                    body: qs.stringify({
                        'dwfrm_cart_shipments_i0_items_i0_quantity': '1',
                        'dwfrm_cart_shipments_i0_items_i0_quantity': '1',
                        'dwfrm_cart_shipments_i0_items_i0_deleteProduct': 'Remove',
                        'dwfrm_cart_couponCode': '',
                        'dwfrm_cart_updateCart': 'dwfrm_cart_updateCart',
                        'csrf_token': this.data.csrf.cart
                    }),
                }
                
                const response = await got(options)
                const root = parse(response.body)
                
                if (response.body.includes('<h1>Your Shopping Cart is Empty</h1>')) {
                    this.data.cartEmpty = true
                }
                
                await this.log(`${response.statusCode} - ${this.data.cartEmpty ? 'Cart cleared' : 'Cart may not be cleared'}`)
                
            } catch(error) {
                await this.log(`${error.response ? error.response.statusCode : '000'} - Error clearing cart: ${error.message}`, 'error')
                if (error.response) {
                    if (error.response.statusCode === 403 || error.response.statusCode === 429 || error.response.statusCode === 430) {
                        this.config.agent = await this.proxyGot()
                    }
                    await new Promise(r => setTimeout(r, 1000))
                    return await this.cartClear()
                } else {
                    if (error.message.includes('tunneling socket could not be established')) {
                        this.config.agent = await this.proxyGot()
                        await this.delay()
                        return await this.cartClear()
                    } else {
                        this.enabled = false
                    }
                }
            }
        }
    }
}
