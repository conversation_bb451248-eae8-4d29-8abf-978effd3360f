// Enhanced Checkout methods with improved bot detection avoidance
const got = require('got')
const { parse } = require('node-html-parser')
const qs = require('qs')

module.exports = class EnhancedCheckout {
    constructor (group, name, link, inputType, pInput, oInput, randomOos, quantity, profile, proxyList, platform, mode) {
        this.group = group;
		this.name = name;
		this.link = link;
		this.inputType = inputType;
		this.pInput = pInput;
		this.oInput = oInput;
		this.randomOos = randomOos;
		this.quantity = quantity;
		this.profile = profile;
		this.proxyList = proxyList;
		this.platform = platform;
		this.mode = mode;
    }

    // Enhanced checkout creation with browser-like behavior
    async checkoutCreate() {
        if (this.enabled) {
            try {
                // Add browser-like query parameters and timestamp
                const timestamp = Date.now();

                const options = {
                    ...await this.miscOptions({
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
                        'Accept-Language': this.config.acceptLanguage,
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'Origin': 'https://www.metallica.com',
                        'Referer': 'https://www.metallica.com/on/demandware.store/Sites-Metallica-Site/default/Cart-Show',
                        'Upgrade-Insecure-Requests': '1',
                        'Sec-Fetch-Dest': 'document',
                        'Sec-Fetch-Mode': 'navigate',
                        'Sec-Fetch-Site': 'same-origin',
                        'Sec-Fetch-User': '?1'
                    }),
                    url: 'https://www.metallica.com/on/demandware.store/Sites-Metallica-Site/default/Cart-SubmitForm',
                    method: 'POST',
                    responseType: 'text',
                    body: qs.stringify({
                        dwfrm_cart_checkoutCart: "Checkout",
                        // Add browser-like form metadata
                        'form_timestamp': timestamp,
                        'browser_width': this.config.browserProps.screenWidth,
                        'browser_height': this.config.browserProps.screenHeight
                    })
                }

                const response = await got(options)

                const root = await parse(response.body)
                const csrf = await root.querySelector('[name="csrf_token"]').attrs.value
                await this.log(`${response.statusCode} - Generated checkout`)
                return csrf
            } catch (error) {
                await this.log(`${error.response ? error.response.statusCode : '000'} - Error generating checkout: ${error.message}`, 'error')
                if (error.response) {
                    if (error.response.statusCode === 403 || error.response.statusCode === 429 || error.response.statusCode === 430) {
                        this.config.agent = await this.proxyGot()
                    }
                    await new Promise(r => setTimeout(r, 1000))
                    return await this.checkoutCreate()
                } else {
                    if (error.message.includes('tunneling socket could not be established')) {
                        this.config.agent = await this.proxyGot()
                        await this.delay()
                        return await this.checkoutCreate()
                    } else {
                        this.enabled = false
                    }
                }
            }
        }
    }

    // Get available shipping codes with enhanced headers
    async getAvailableShippingCodes() {
        if(this.enabled) {
            try {
                const options = {
                    ...await this.miscOptions({
                        'Accept': 'application/json, text/javascript, */*; q=0.01',
                        'Accept-Language': this.config.acceptLanguage,
                        'Referer': 'https://www.metallica.com/shipping/',
                        'X-Requested-With': 'XMLHttpRequest',
                        'DNT': '1',
                        'Connection': 'keep-alive',
                        'Sec-Fetch-Dest': 'empty',
                        'Sec-Fetch-Mode': 'cors',
                        'Sec-Fetch-Site': 'same-origin',
                        'TE': 'trailers'
                    }),
                    url: 'https://www.metallica.com/on/demandware.store/Sites-Metallica-Site/default/CheckoutShippingServices-UpdateShippingMethodsList?country=US&zipCode=' + this.profile.shippingAddress.postCode,
                    method: 'GET',
                    responseType: 'json'
                }

                const response = await got(options)

                let lowestShippingMethod = {
                    cost: 999999,
                    id: ''
                }

                for (const method of response.body.shipping.applicableShippingMethods) {
                    if (method.shippingCost < lowestShippingMethod.cost) {
                        lowestShippingMethod.cost = method.shippingCost
                        lowestShippingMethod.id = method.ID
                    }
                }

                await this.log(`${response.statusCode} - Got shipping methods | ${lowestShippingMethod.id} - $${lowestShippingMethod.cost}`)
                return lowestShippingMethod.id

            } catch (error) {
                await this.log(`${error.response ? error.response.statusCode : '000'} - Error getting shipping methods: ${error.message}`, 'error')
                if (error.response) {
                    if (error.response.statusCode === 403 || error.response.statusCode === 429 || error.response.statusCode === 430) {
                        this.config.agent = await this.proxyGot()
                    }
                    await new Promise(r => setTimeout(r, 1000))
                    return await this.getAvailableShippingCodes()
                } else {
                    if (error.message.includes('tunneling socket could not be established')) {
                        this.config.agent = await this.proxyGot()
                        await this.delay()
                        return await this.getAvailableShippingCodes()
                    } else {
                        this.enabled = false
                    }
                }
            }
        }
    }

    // Get shipping page with enhanced headers
    async checkoutGetShipping(shipping) {
        if (this.enabled) {
            try {
                const options = {
                    ...await this.miscOptions({
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
                        'Accept-Language': this.config.acceptLanguage,
                        'Referer': 'https://www.metallica.com/checkout/',
                        'Upgrade-Insecure-Requests': '1',
                        'Sec-Fetch-Dest': 'document',
                        'Sec-Fetch-Mode': 'navigate',
                        'Sec-Fetch-Site': 'same-origin',
                        'Sec-Fetch-User': '?1'
                    }),
                    url: 'https://www.metallica.com/shipping/',
                    method: 'GET',
                    responseType: 'text'
                }

                const response = await got(options)
                await this.log(`${response.statusCode} - Got shipping page`)

            } catch (error) {
                await this.log(`${error.response ? error.response.statusCode : '000'} - Error getting shipping page: ${error.message}`, 'error')
                if (error.response) {
                    if (error.response.statusCode === 403 || error.response.statusCode === 429 || error.response.statusCode === 430) {
                        this.config.agent = await this.proxyGot()
                    }
                    await new Promise(r => setTimeout(r, 1000))
                    return await this.checkoutGetShipping(shipping)
                } else {
                    if (error.message.includes('tunneling socket could not be established')) {
                        this.config.agent = await this.proxyGot()
                        await this.delay()
                        return await this.checkoutGetShipping(shipping)
                    } else {
                        this.enabled = false
                    }
                }
            }
        }
    }

    // Submit shipping with enhanced headers and browser-like behavior
    async checkoutSubmitShipping(shipping) {
        if (this.enabled) {
            try {
                // Add browser-like timestamp
                const timestamp = Date.now();

                const options = {
                    ...await this.miscOptions({
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
                        'Accept-Language': this.config.acceptLanguage,
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'Origin': 'https://www.metallica.com',
                        'Referer': 'https://www.metallica.com/shipping/',
                        'Upgrade-Insecure-Requests': '1',
                        'Sec-Fetch-Dest': 'document',
                        'Sec-Fetch-Mode': 'navigate',
                        'Sec-Fetch-Site': 'same-origin',
                        'Sec-Fetch-User': '?1'
                    }),
                    url: 'https://www.metallica.com/on/demandware.store/Sites-Metallica-Site/default/CheckoutShippingServices-SubmitShipping',
                    method: 'POST',
                    responseType: 'text',
                    body: qs.stringify({
                        'dwfrm_shipping_shippingAddress_addressFields_firstName': this.profile.shippingAddress.firstName,
                        'dwfrm_shipping_shippingAddress_addressFields_lastName': this.profile.shippingAddress.lastName,
                        'dwfrm_shipping_shippingAddress_addressFields_address1': this.profile.shippingAddress.addressLine1,
                        'dwfrm_shipping_shippingAddress_addressFields_address2': this.profile.shippingAddress.addressLine2,
                        'dwfrm_shipping_shippingAddress_addressFields_city': this.profile.shippingAddress.city,
                        'dwfrm_shipping_shippingAddress_addressFields_states_stateCode': this.profile.shippingAddress.state,
                        'dwfrm_shipping_shippingAddress_addressFields_postalCode': this.profile.shippingAddress.postCode,
                        'dwfrm_shipping_shippingAddress_addressFields_country': 'US',
                        'dwfrm_shipping_shippingAddress_addressFields_phone': this.profile.shippingAddress.phone,
                        'dwfrm_shipping_shippingAddress_shippingMethodID': shipping,
                        'dwfrm_shipping_shippingAddress_giftMessage': '',
                        'csrf_token': this.data.csrf.checkout,
                        // Add browser-like form metadata
                        'form_timestamp': timestamp,
                        'browser_width': this.config.browserProps.screenWidth,
                        'browser_height': this.config.browserProps.screenHeight
                    })
                }

                const response = await got(options)
                const root = parse(response.body)
                const csrf = root.querySelector('[name="csrf_token"]').attrs.value
                await this.log(`${response.statusCode} - Posted shipping | ${csrf}`)
                return csrf

            } catch (error) {
                await this.log(`${error.response ? error.response.statusCode : '000'} - Error submitting shipping: ${error.message}`, 'error')
                if (error.response) {
                    if (error.response.statusCode === 403 || error.response.statusCode === 429 || error.response.statusCode === 430) {
                        this.config.agent = await this.proxyGot()
                    }
                    await new Promise(r => setTimeout(r, 1000))
                    return await this.checkoutSubmitShipping(shipping)
                } else {
                    if (error.message.includes('tunneling socket could not be established')) {
                        this.config.agent = await this.proxyGot()
                        await this.delay()
                        return await this.checkoutSubmitBilling(shipping)
                    } else {
                        this.enabled = false
                    }
                }
            }
        }
    }

    // Submit billing with enhanced headers and browser-like behavior
    async checkoutSubmitBilling() {
        if (this.enabled) {
            try {
                let cardType;
                switch (this.profile.paymentDetails.cardType) {
                    case 'AmericanExpress':
                        cardType = 'Amex';
                        break;
                    default:
                        cardType = this.profile.paymentDetails.cardType;
                }

                // Add browser-like timestamp
                const timestamp = Date.now();

                const options = {
                    ...await this.miscOptions({
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
                        'Accept-Language': this.config.acceptLanguage,
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'Origin': 'https://www.metallica.com',
                        'Referer': 'https://www.metallica.com/billing/',
                        'Upgrade-Insecure-Requests': '1',
                        'Sec-Fetch-Dest': 'document',
                        'Sec-Fetch-Mode': 'navigate',
                        'Sec-Fetch-Site': 'same-origin',
                        'Sec-Fetch-User': '?1'
                    }),
                    url: 'https://www.metallica.com/on/demandware.store/Sites-Metallica-Site/default/CheckoutServices-SubmitPayment',
                    method: 'POST',
                    responseType: 'text',
                    body: qs.stringify({
                        'dwfrm_billing_billingAddress_addressFields_firstName': this.profile.billingAddress.firstName,
                        'dwfrm_billing_billingAddress_addressFields_lastName': this.profile.billingAddress.lastName,
                        'dwfrm_billing_billingAddress_addressFields_address1': this.profile.billingAddress.addressLine1,
                        'dwfrm_billing_billingAddress_addressFields_address2': this.profile.billingAddress.addressLine2,
                        'dwfrm_billing_billingAddress_addressFields_city': this.profile.billingAddress.city,
                        'dwfrm_billing_billingAddress_addressFields_states_stateCode': this.profile.billingAddress.state,
                        'dwfrm_billing_billingAddress_addressFields_postalCode': this.profile.billingAddress.postCode,
                        'dwfrm_billing_billingAddress_addressFields_country': 'US',
                        'dwfrm_billing_billingAddress_addressFields_phone': this.profile.billingAddress.phone,
                        'dwfrm_billing_paymentMethod': 'CREDIT_CARD',
                        'dwfrm_billing_creditCardFields_cardType': cardType,
                        'dwfrm_billing_creditCardFields_cardNumber': this.profile.paymentDetails.cardNumber,
                        'dwfrm_billing_creditCardFields_expirationMonth': this.profile.paymentDetails.expiryMonth,
                        'dwfrm_billing_creditCardFields_expirationYear': this.profile.paymentDetails.expiryYear,
                        'dwfrm_billing_creditCardFields_securityCode': this.profile.paymentDetails.cvv,
                        'dwfrm_billing_contactInfoFields_email': this.profile.billingAddress.email,
                        'dwfrm_billing_contactInfoFields_emailconfirm': this.profile.billingAddress.email,
                        'csrf_token': this.data.csrf.shipping,
                        // Add browser-like form metadata
                        'form_timestamp': timestamp,
                        'browser_width': this.config.browserProps.screenWidth,
                        'browser_height': this.config.browserProps.screenHeight
                    })
                }

                const response = await got(options)
                const root = parse(response.body)
                const csrf = root.querySelector('[name="csrf_token"]').attrs.value
                await this.log(`${response.statusCode} - Posted billing`)
                return csrf

            } catch (error) {
                await this.log(`${error.response ? error.response.statusCode : '000'} - Error submitting billing: ${error.message}`, 'error')
                if (error.response) {
                    if (error.response.statusCode === 403 || error.response.statusCode === 429 || error.response.statusCode === 430) {
                        this.config.agent = await this.proxyGot()
                    }
                    await new Promise(r => setTimeout(r, 1000))
                    return await this.checkoutSubmitBilling()
                } else {
                    if (error.message.includes('tunneling socket could not be established')) {
                        this.config.agent = await this.proxyGot()
                        await this.delay()
                        return await this.checkoutSubmitBilling()
                    } else {
                        this.enabled = false
                    }
                }
            }
        }
    }

    // Submit order with enhanced headers and browser-like behavior
    async checkoutSubmitOrder() {
        if (this.enabled) {
            try {
                await this.log('000 - Submitting order')

                // Add browser-like timestamp
                const timestamp = Date.now();

                const options = {
                    ...await this.miscOptions({
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
                        'Accept-Language': this.config.acceptLanguage,
                        'Referer': 'https://www.metallica.com/placeorder/',
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'Origin': 'https://www.metallica.com',
                        'DNT': '1',
                        'Connection': 'keep-alive',
                        'Upgrade-Insecure-Requests': '1',
                        'Sec-Fetch-Dest': 'document',
                        'Sec-Fetch-Mode': 'navigate',
                        'Sec-Fetch-Site': 'same-origin',
                        'Sec-Fetch-User': '?1',
                        'TE': 'trailers'
                    }),
                    responseType: 'text',
                    body: qs.stringify({
                        'submit': 'Place Order',
                        'csrf_token': this.data.csrf.billing,
                        // Add browser-like form metadata
                        'form_timestamp': timestamp,
                        'browser_width': this.config.browserProps.screenWidth,
                        'browser_height': this.config.browserProps.screenHeight
                    }),
                    url: 'https://www.metallica.com/orderconfirmation/'
                }

                const response = await got.post(options)
                const root = parse(response.body)
                const csrf = root.querySelector('[name="csrf_token"]')?.attrs.value

                if (root.querySelector('.error-form')) {
                    const errorMessage = root.querySelector('.error-form').innerText
                    await this.log(`${response.statusCode} - Error: ${errorMessage}`, 'error')
                    return {status: 'fail', message: errorMessage, csrf: csrf}
                }

                if (root.querySelector('.order-confirmation-details')) {
                    const message = JSON.parse(response.body.split("_etmc.push(['trackConversion', ")[1].split(' ]);')[0]).order_number
                    await this.log(`${response.statusCode} - Check email | Order #${message}`, 'success')
                    return {status: 'success', order_number: message}
                }

                this.log(`${response.statusCode} - ${response.url}`)
                return {status: 'decline or error', csrf: csrf}

            } catch (error) {
                await this.log(`${error.response ? error.response.statusCode : '000'} - Error submitting checkout: ${error.message}`, 'error')
                if (error.response) {
                    if (error.response.statusCode === 403 || error.response.statusCode === 429 || error.response.statusCode === 430) {
                        this.config.agent = await this.proxyGot()
                    }
                    await new Promise(r => setTimeout(r, 1000))
                    return await this.checkoutSubmitOrder()
                } else {
                    if (error.message.includes('tunneling socket could not be established')) {
                        this.config.agent = await this.proxyGot()
                        await this.delay()
                        return await this.checkoutSubmitOrder()
                    } else {
                        this.enabled = false
                    }
                }
            }
        }
    }
}
