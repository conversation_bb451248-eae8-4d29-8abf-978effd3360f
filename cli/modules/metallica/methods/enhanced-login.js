// Enhanced Login methods with improved bot detection avoidance
const got = require('got')
const { parse } = require('node-html-parser')
const qs = require('qs')

module.exports = class EnhancedLogin {
    constructor (group, name, link, inputType, pInput, oInput, randomOos, quantity, profile, proxyList, platform, mode) {
        this.group = group;
		this.name = name;
		this.link = link;
		this.inputType = inputType;
		this.pInput = pInput;
		this.oInput = oInput;
		this.randomOos = randomOos;
		this.quantity = quantity;
		this.profile = profile;
		this.proxyList = proxyList;
		this.platform = platform;
		this.mode = mode;
    }

    // Get login page with enhanced headers
    async loginGet(login = false) {
        if (this.enabled) {
            try {
                // Add browser-like query parameters and timestamp
                const timestamp = Date.now();
                const randomId = Math.floor(Math.random() * 1000000);
                
                const options = {
                    ...await this.miscOptions({
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
                        'Accept-Language': this.config.acceptLanguage,
                        'Referer': 'https://www.metallica.com/',
                        'Upgrade-Insecure-Requests': '1',
                        'Sec-Fetch-Dest': 'document',
                        'Sec-Fetch-Mode': 'navigate',
                        'Sec-Fetch-Site': 'same-origin',
                        'Sec-Fetch-User': '?1'
                    }),
                    url: `https://www.metallica.com/on/demandware.store/Sites-Metallica-Site/default/Login-LoginForm?scope=&_=${timestamp}&id=${randomId}`,
                    method: 'GET',
                    responseType: 'text'
                }
                
                const response = await got(options)
                const root = parse(response.body)
                const csrf = root.querySelector('[name="csrf_token"]').attrs.value
                await this.log(`${response.statusCode} - Got login page | ${csrf}`)
                return csrf
                
            } catch(error) {
                await this.log(`${error.response ? error.response.statusCode : '000'} - Error retrieving login: ${error.message}`, 'error')
                if (error.response) {
                    if (error.response.statusCode === 403 || error.response.statusCode === 429 || error.response.statusCode === 430) {
                        this.config.agent = await this.proxyGot()
                    }
                    await new Promise(r => setTimeout(r, 1000))
                    return await this.loginGet(login)
                } else {
                    if (error.message.includes('tunneling socket could not be established')) {
                        this.config.agent = await this.proxyGot()
                        await this.delay()
                        return await this.loginGet()
                    } else {
                        this.enabled = false
                    }
                }
            }
        }
    }

    // Post login with enhanced headers and browser-like behavior
    async loginPost(login) {
        if (this.enabled) {
            try {
                // Add browser-like timestamp
                const timestamp = Date.now();
                
                const options = {
                    ...await this.miscOptions({
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
                        'Accept-Language': this.config.acceptLanguage,
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'Origin': 'https://www.metallica.com',
                        'Referer': 'https://www.metallica.com/on/demandware.store/Sites-Metallica-Site/default/Login-LoginForm?scope=',
                        'Upgrade-Insecure-Requests': '1',
                        'Sec-Fetch-Dest': 'document',
                        'Sec-Fetch-Mode': 'navigate',
                        'Sec-Fetch-Site': 'same-origin',
                        'Sec-Fetch-User': '?1'
                    }),
                    url: 'https://www.metallica.com/on/demandware.store/Sites-Metallica-Site/default/Login-LoginForm?scope=',
                    method: 'POST',
                    responseType: 'text',
                    body: qs.stringify({
                        'dwfrm_login_username': this.profile.shippingAddress.email,
                        'dwfrm_login_password': this.settings.metallica.password,
                        'dwfrm_login_login': 'Sign In',
                        'csrf_token': login,
                        // Add browser-like form metadata
                        'form_timestamp': timestamp,
                        'browser_width': this.config.browserProps.screenWidth,
                        'browser_height': this.config.browserProps.screenHeight
                    })
                }
                
                const response = await got(options)
                const root = parse(response.body)
                
                if (response.body.includes('Incorrect username or password')) {
                    await this.log(`${response.statusCode} - Login failed: Incorrect username or password`, 'error')
                    this.enabled = false
                    return false
                }
                
                await this.log(`${response.statusCode} - Logged in`)
                return true
                
            } catch (error) {
                await this.log(`${error.response ? error.response.statusCode : '000'} - Error logging in: ${error.message}`, 'error')
                if (error.response) {
                    if (error.response.statusCode === 403 || error.response.statusCode === 429 || error.response.statusCode === 430) {
                        this.config.agent = await this.proxyGot()
                    }
                    await new Promise(r => setTimeout(r, 1000))
                    return await this.loginPost(login)
                } else {
                    if (error.message.includes('tunneling socket could not be established')) {
                        this.config.agent = await this.proxyGot()
                        await this.delay()
                        return await this.loginPost(login)
                    } else {
                        this.enabled = false
                    }
                }
            }
        }
    }
}
