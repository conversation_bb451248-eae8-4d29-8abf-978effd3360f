// Enhanced Misc methods with improved bot detection avoidance
const got = require('got')
const { parse } = require('node-html-parser')
const qs = require('qs')

module.exports = class EnhancedMisc {
    constructor (group, name, link, inputType, pInput, oInput, randomOos, quantity, profile, proxyList, platform, mode) {
        this.group = group;
		this.name = name;
		this.link = link;
		this.inputType = inputType;
		this.pInput = pInput;
		this.oInput = oInput;
		this.randomOos = randomOos;
		this.quantity = quantity;
		this.profile = profile;
		this.proxyList = proxyList;
		this.platform = platform;
		this.mode = mode;
    }

    // Enhanced miscOptions method with improved headers
    async miscOptions(baseHeaders = {}) {
        return {
            headers: {
                'User-Agent': this.config.userAgent,
                'Accept': this.config.acceptHeader || 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': this.config.acceptLanguage || 'en-US,en;q=0.9',
                'DNT': '1',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'same-origin',
                'Sec-Fetch-User': '?1',
                'Cache-Control': 'max-age=0',
                'sec-ch-ua': '"Google Chrome";v="113", "Chromium";v="113"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': `"${this.config.browserProps?.platform || 'Windows'}"`,
                ...baseHeaders
            },
            cookieJar: this.config.cookieJar,
            agent: this.config.agent
        };
    }

    // Get settings with enhanced headers
    async getSettings() {
        try {
            const response = await got.get(`http://localhost:3030/settings`, {
                responseType: 'json'
            })
            return response.body
        } catch (err) {
            this.log(`${err.response ? err.response.statusCode : '000'} - Error fetching settings`, 'error')
            this.enabled = false
        }
    }

    // Enhanced staff log method
    async staffLog(site, title, price, image) {
        try {
            const response = await got.post('http://localhost:3030/success', {
                responseType: 'json',
                json: {
                    site: site,
                    title: title,
                    price: price,
                    image: image,
                    email: this.profile.shippingAddress.email,
                    mode: this.mode
                }
            })
            this.log(`${response.statusCode} - Posted to success feed`, 'success')
        } catch (err) {
            this.log(`${err.response ? err.response.statusCode : '000'} - Error posting to success feed`, 'error')
        }
    }

    // Enhanced user webhook method
    async userWebhook(site, title, price, image) {
        try {
            const response = await got.post('http://localhost:3030/webhook', {
                responseType: 'json',
                json: {
                    site: site,
                    title: title,
                    price: price,
                    image: image,
                    email: this.profile.shippingAddress.email,
                    mode: this.mode
                }
            })
            this.log(`${response.statusCode} - Posted to webhook`, 'success')
        } catch (err) {
            this.log(`${err.response ? err.response.statusCode : '000'} - Error posting to webhook`, 'error')
        }
    }
}
