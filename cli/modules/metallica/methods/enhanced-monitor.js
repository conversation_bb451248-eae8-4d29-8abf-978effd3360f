// Enhanced Monitor methods with improved bot detection avoidance
const got = require('got')
const { parse } = require('node-html-parser')

module.exports = class EnhancedMonitor {
    constructor (group, name, link, inputType, pInput, oInput, randomOos, quantity, profile, proxyList, platform, mode) {
        this.group = group;
		this.name = name;
		this.link = link;
		this.inputType = inputType;
		this.pInput = pInput;
		this.oInput = oInput;
		this.randomOos = randomOos;
		this.quantity = quantity;
		this.profile = profile;
		this.proxyList = proxyList;
		this.platform = platform;
		this.mode = mode;
    }

    // Enhanced keyword monitor with browser-like behavior
    async monitorKW() {
        if (this.enabled) {
            try {
                let search = this.positives.map(kw => {
                    return kw.substr(1, kw.length-1)
                })
                
                // Add browser-like query parameters and timestamp
                const timestamp = Date.now();
                const randomId = Math.floor(Math.random() * 1000000);
                
                const options = {
                    ...await this.miscOptions({
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
                        'Accept-Language': this.config.acceptLanguage,
                        'Referer': 'https://www.metallica.com/',
                        'DNT': '1',
                        'Connection': 'keep-alive',
                        'Upgrade-Insecure-Requests': '1',
                        'Sec-Fetch-Dest': 'document',
                        'Sec-Fetch-Mode': 'navigate',
                        'Sec-Fetch-Site': 'same-origin',
                        'Sec-Fetch-User': '?1'
                    }),
                    url: `https://www.metallica.com/search/?q=${search.join(' ')}&lang=default&cgid=${this.randomStr()}&viewAll=true&_=${timestamp}&id=${randomId}`,
                    method: 'GET',
                    responseType: 'text',
                    agent: await this.proxyGot('monitor'),
                    followRedirect: false
                }
                
                const response = await got(options)
                let redirect = response.headers.location ? response.headers.location : response.url
                
                if (redirect.includes('https://www.metallica.com/store/')) {
                    redirect = redirect.split('.html')[0].split('/')
                    redirect = redirect[redirect.length - 1]
                    await this.log(`${response.statusCode} - ${redirect}`)
                    return redirect
                } else if (redirect.includes('https://www.metallica.com/search/?q=')) {
                    const root = await parse(response.body)
                    const parsed = root.querySelectorAll('.product-tile')
                    await this.log(`${response.statusCode} - ${parsed.length} products loaded`)
                    
                    for (let i = 0; i < parsed.length; i++) {
                        const product = {
                            name: parsed[i].attrs['data-itemname'],
                            PID: parsed[i].attrs['data-itemid']
                        }
                        
                        // Seeing if product.name matches keywords
                        const pMatches = await this.positives.filter(keyword => 
                            product.name.toLowerCase().includes(keyword.slice(1, keyword.length))
                        )
                        const nMatches = await this.negatives.filter(keyword => 
                            product.name.toLowerCase().includes(keyword.slice(1, keyword.length))
                        )
                        
                        if (pMatches.length === this.positives.length && nMatches.length === 0) {
                            this.log(`000 - ${product.name}`)
                            return product.PID
                        }
                    }
                }
    
            } catch (err) {
                await this.log(`${err.response ? err.response.statusCode : '000'} - Error KW monitoring ${err.response ? err.response.url : ''}`, 'error')
                if (err.response) {
                    if (err.response.statusCode === 403 || err.response.statusCode === 429 || err.response.statusCode === 430) {
                        // Only rotate proxy on specific errors
                        this.config.agent = await this.proxyGot('monitor')
                    }
                }
            }
            
            // Keep the same timing as original
            await new Promise(r => setTimeout(r, 1000))
            return await this.monitorKW()
        }
    }
    
    // Enhanced direct product monitor
    async monitorDirect() {
        if (this.enabled) {
            try {
                // Add browser-like query parameters and timestamp
                const timestamp = Date.now();
                const randomId = Math.floor(Math.random() * 1000000);
                
                const options = {
                    ...await this.miscOptions({
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
                        'Accept-Language': this.config.acceptLanguage,
                        'Referer': 'https://www.metallica.com/',
                        'DNT': '1',
                        'Connection': 'keep-alive',
                        'Upgrade-Insecure-Requests': '1',
                        'Sec-Fetch-Dest': 'document',
                        'Sec-Fetch-Mode': 'navigate',
                        'Sec-Fetch-Site': 'same-origin',
                        'Sec-Fetch-User': '?1'
                    }),
                    url: `https://www.metallica.com/store/${this.pInput}.html?_=${timestamp}&id=${randomId}`,
                    method: 'GET',
                    responseType: 'text',
                    agent: await this.proxyGot('monitor'),
                    followRedirect: false
                }
                
                const response = await got(options)
                const root = parse(response.body)
                
                // Check if product is available
                if (root.querySelector('.product-availability .availability-msg')?.innerText.includes('Out of Stock')) {
                    await this.log(`${response.statusCode} - Product is out of stock`, 'gray')
                    
                    // Keep the same timing as original
                    await new Promise(r => setTimeout(r, 1000))
                    return await this.monitorDirect()
                }
                
                await this.log(`${response.statusCode} - Product is in stock`)
                return this.pInput
                
            } catch (err) {
                await this.log(`${err.response ? err.response.statusCode : '000'} - Error direct monitoring: ${err.message}`, 'error')
                if (err.response) {
                    if (err.response.statusCode === 403 || err.response.statusCode === 429 || err.response.statusCode === 430) {
                        // Only rotate proxy on specific errors
                        this.config.agent = await this.proxyGot('monitor')
                    }
                }
                
                // Keep the same timing as original
                await new Promise(r => setTimeout(r, 1000))
                return await this.monitorDirect()
            }
        }
    }
}
