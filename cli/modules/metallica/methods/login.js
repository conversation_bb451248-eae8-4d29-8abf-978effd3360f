// Login methods

const got = require('got')
const qs = require('qs')
const  { parse } = require('node-html-parser')

module.exports = class CLogin {
    constructor (group, name, link, inputType, pInput, oInput, randomOos, quantity, profile, proxyList, platform, mode) {
        this.group = group;
		this.name = name;
		this.link = link;
		this.inputType = inputType;
		this.pInput = pInput;
		this.oInput = oInput;
		this.randomOos = randomOos;
		this.quantity = quantity;
		this.profile = profile;
		this.proxyList = proxyList;
		this.platform = platform;
		this.mode = mode;
    }

    async loginGet() {
        if (this.enabled) {
            try {
                const options = {
                    ...await this.miscOptions(),
                    url: 'https://www.metallica.com/login/?original=%2Faccount%2F',
                    method: 'GET',
                }

                options.headers = {
                    ...options.headers,
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8', 
                    'Accept-Language': 'en-US,en;q=0.5', 
                    'Referer': 'https://www.metallica.com/', 
                    'DNT': '1', 
                    'Connection': 'keep-alive', 
                    'Upgrade-Insecure-Requests': '1', 
                    'Sec-Fetch-Dest': 'document', 
                    'Sec-Fetch-Mode': 'navigate', 
                    'Sec-Fetch-Site': 'same-origin', 
                    'Sec-Fetch-User': '?1', 
                    'Cache-Control': 'max-age=0', 
                    'TE': 'trailers'
                }

                const response = await got(options)

                const root = await parse(response.body)
                const csrf = await root.querySelector('[name="csrf_token"]').attrs.value
                const inputs = await root.querySelectorAll('.input-text')
                const loginCode = (await inputs.find(input => input.attrs.id.includes('dwfrm_login_username_'))).attrs.id.split('_')[3]
                this.data.csrf.login = csrf
                return {
                    username: `dwfrm_login_username_${loginCode}`,
                    password: `dwfrm_login_password_${loginCode}`
                }
            } catch(error) {
                await this.log(`${error.response ? error.response.statusCode : '000'} - Error retreiving login: ${error.message}`, 'error')
                if (error.response) {
                    if (error.response.statusCode === 403 || error.response.statusCode === 429 || error.response.statusCode === 430) {
                        this.config.agent = await this.proxyGot()
                    }
                    await new Promise(r => setTimeout(r, 1000))
                    return await this.loginGet(login)
                } else {
                    if (error.message.includes('tunneling socket could not be established')) {
                        this.config.agent = await this.proxyGot()
                        await this.delay()
                        return await this.loginGet()
                    } else {
                        this.enabled = false
                    }
                }
            }
        }
    };

    async loginPost (login) {
        if (this.enabled) {
            try {

                const options = {
                    ...await this.miscOptions(),
                    url: 'https://www.metallica.com/on/demandware.store/Sites-Metallica-Site/default/Login-LoginForm?scope=',
                    method: 'POST',
                    responseType: 'text',
                    body: qs.stringify({
                        [login.username]: this.profile.shippingAddress.email,
                        [login.password]: this.data.account,
                        "dwfrm_login_login": "Login",
                        "csrf_token": this.data.csrf.login
                    }),
                    followRedirect: false


                }

                options.headers = {
                    ...options.headers,
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8', 
                    'Accept-Language': 'en-US,en;q=0.5', 
                    'Content-Type': 'application/x-www-form-urlencoded', 
                    'Origin': 'https://www.metallica.com', 
                    'DNT': '1', 
                    'Connection': 'keep-alive', 
                    'Referer': 'https://www.metallica.com/login/?original=%2Faccount%2F', 
                    'Upgrade-Insecure-Requests': '1', 
                    'Sec-Fetch-Dest': 'document', 
                    'Sec-Fetch-Mode': 'navigate', 
                    'Sec-Fetch-Site': 'same-origin', 
                    'Sec-Fetch-User': '?1', 
                    'Pragma': 'no-cache', 
                    'Cache-Control': 'no-cache', 
                    'TE': 'trailers'
                }

                const response = await got(options)

                if (response.statusCode === 302) {
                    await this.log(`${response.statusCode} - Logged in`)
                } else {
                    throw new Error(`Invalid account:password?`)
                }

            } catch (error) {
                await this.log(`${error.response ? error.response.statusCode : '000'} - Error logging in: ${error.message}`, 'error')
                if (error.response) {
                    if (error.response.statusCode === 403 || error.response.statusCode === 429 || error.response.statusCode === 430) {
                        this.config.agent = await this.proxyGot()
                    }
                    await new Promise(r => setTimeout(r, 1000))
                    return await this.loginPost(login)
                } else {
                    if (error.message.includes('tunneling socket could not be established')) {
                        this.config.agent = await this.proxyGot()
                        await this.delay()
                        return await this.loginPost(login)
                    } else {
                        this.enabled = false
                    }
                }
            }
        }
    };

}