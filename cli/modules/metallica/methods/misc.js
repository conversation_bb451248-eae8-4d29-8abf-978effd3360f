// Login methods

const got = require('got')

module.exports = class Misc {
    constructor (group, name, link, inputType, pInput, oInput, randomOos, quantity, profile, proxyList, platform, mode) {
        this.group = group;
		this.name = name;
		this.link = link;
		this.inputType = inputType;
		this.pInput = pInput;
		this.oInput = oInput;
		this.randomOos = randomOos;
		this.quantity = quantity;
		this.profile = profile;
		this.proxyList = proxyList;
		this.platform = platform;
		this.mode = mode;
    }

    async miscOptions(headers = {}) {
        try {
            const options = {
                cookieJar: this.config.cookieJar,
                agent: this.config.agent,
                responseType: 'text',
                headers: {
                    ...headers,
                    'User-Agent': this.config.userAgent,
                }
            }

            return options
        } catch (error) {
            return false
        }
    }

}