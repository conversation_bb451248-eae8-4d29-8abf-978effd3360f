// Login methods

const got = require('got')
const  { parse } = require('node-html-parser')

module.exports = class Monitor {
    constructor (group, name, link, inputType, pInput, oInput, randomOos, quantity, profile, proxyList, platform, mode) {
        this.group = group;
		this.name = name;
		this.link = link;
		this.inputType = inputType;
		this.pInput = pInput;
		this.oInput = oInput;
		this.randomOos = randomOos;
		this.quantity = quantity;
		this.profile = profile;
		this.proxyList = proxyList;
		this.platform = platform;
		this.mode = mode;
    }

    async monitorKW() {
        if (this.enabled) {
            try {
                let search = this.positives.map(kw => {
                    return kw.substr(1, kw.length-1)
                })
                // const response = await got.get(`https://www.metallica.com/search/?q=${search.join(' ')}&lang=default&id=${Math.floor(Math.random() * 9999999)}`, {
                const response = await got.get(`https://www.metallica.com/search/?q=${search.join(' ')}&lang=default&cgid=${this.randomStr()}&viewAll=true`, {
                    responseType: 'text',
                    headers: {
                        'User-Agent': this.userAgent(),
                        'Accept-Language': 'en-US,en;q=0.5', 
                        'DNT': '1', 
                        'Connection': 'keep-alive', 
                        'Upgrade-Insecure-Requests': '1', 
                        'Sec-Fetch-Dest': 'document', 
                        'Sec-Fetch-Mode': 'navigate', 
                        'Sec-Fetch-Site': 'none', 
                        'Sec-Fetch-User': '?1'
                    },
                    agent: await this.proxyGot('monitor'),
                    followRedirect: false
                })

                let redirect = response.headers.location ? response.headers.location : response.url

                if (redirect.includes('https://www.metallica.com/store/')) {
                    redirect = redirect.split('.html')[0].split('/')
                    redirect = redirect[redirect.length - 1]
                    await this.log(`${response.statusCode} - ${redirect}`)
                    return redirect
                } else if (redirect.includes('https://www.metallica.com/search/?q=')) {
                    const root = await parse(response.body)
                    const parsed = root.querySelectorAll('.product-tile')
                    await this.log(`${response.statusCode} - ${parsed.length} products loaded`)
                    for (let i = 0; i < parsed.length; i++) {
                        const product = {
                            name: parsed[i].attrs['data-itemname'],
                            PID: parsed[i].attrs['data-itemid']
                        }
                        // Seeing if product.name matches keywords
                        const pMatches = await this.positives.filter(keyword => product.name.toLowerCase().includes(keyword.slice(1, keyword.length)))
                        const nMatches = await this.negatives.filter(keyword => product.name.toLowerCase().includes(keyword.slice(1, keyword.length)))
                        if (pMatches.length === this.positives.length && nMatches.length === 0) {
                            this.log(`000 - ${product.name}`)
                            return product.PID
                        }
                    }

                }
    
            } catch (err) {
                await this.log(`${err.response ? err.response.statusCode : '000'} - Error KW monitoring ${err.response ? err.response.url : ''}`, 'error')
            }
            await new Promise(r => setTimeout(r, 1000))
            return await this.monitorKW()
        }
    };

}