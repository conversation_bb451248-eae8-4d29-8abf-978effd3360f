// Metallica, just easier on the eyes and better error handling
const Utils = require('../utils/utils');
const Checkout = require('./methods/checkout');
const Misc = require('./methods/misc');
const Login = require('./methods/login');
const Cart = require('./methods/cart');
const Monitor = require('./methods/monitor');

const { Mixin } = require('ts-mixer');
const { CookieJar } = require('tough-cookie');
const { resolve } = require('path');
const got = require('got')

const MENormal = class MENormal extends Mixin(Utils, Checkout, Misc, Login, Cart, Monitor) {

    constructor(group, name, inputType, pInput, quantity, profile, proxyList, port) {
        super(group, name, 'https://www.metallica.com', inputType, pInput, null, null, quantity, profile, proxyList, 'Metallica', 'Normal')
        this.fullPath = resolve(`${__filename}`)
        this.port = port,
            this.enabled = false
        this.data = {
            csrf: {},
            keys: {}
        }
        this.retry = 0
        this.config = {}
    }

    async flow() {
        try {

            // Config
            this.enabled = true
            this.config.userAgent = this.userAgent()
            this.config.cookieJar = new CookieJar();
            this.config.agent = await this.proxyGot()
            this.settings = await this.getSettings()
            const states = { Alabama: 'AL', Alaska: 'AK', Arizona: 'AZ', Arkansas: 'AR', California: 'CA', Colorado: 'CO', Connecticut: 'CT', Delaware: 'DE', Florida: 'FL', Georgia: 'GA', Hawaii: 'HI', Idaho: 'ID', Illinois: 'IL', Indiana: 'IN', Iowa: 'IA', Kansas: 'KS', Kentucky: 'KY', Louisiana: 'LA', Maine: 'ME', Maryland: 'MD', Massachusetts: 'MA', Michigan: 'MI', Minnesota: 'MN', Mississippi: 'MS', Missouri: 'MO', Montana: 'MT', Nebraska: 'NE', Nevada: 'NV', 'New Hampshire': 'NH', 'New Jersey': 'NJ', 'New Mexico': 'NM', 'New York': 'NY', 'North Carolina': 'NC', 'North Dakota': 'ND', Ohio: 'OH', Oklahoma: 'OK', Oregon: 'OR', Pennsylvania: 'PA', 'Rhode Island': 'RI', 'South Carolina': 'SC', 'South Dakota': 'SD', Tennessee: 'TN', Texas: 'TX', Utah: 'UT', Vermont: 'VT', Virginia: 'VA', Washington: 'WA', 'West Virginia': 'WV', Wisconsin: 'WI', Wyoming: 'WY' }
            this.profile.billingAddress.state = states[this.profile.billingAddress.state]
            this.profile.shippingAddress.state = states[this.profile.shippingAddress.state]
            this.settings = await this.getSettings()

            // Get account
            const accounts = (await got.get('http://localhost:3030/accounts', { responseType: 'json' })).body
            if (accounts['www.metallica.com']) {
                if (accounts['www.metallica.com'][this.profile.shippingAddress.email]) {
                    this.data.account = accounts['www.metallica.com'][this.profile.shippingAddress.email]
                } else {
                    await this.log(`000 - No matching account found for ${this.profile.shippingAddress.email}, please add one under www.metallica.com`, 'error')
                    this.enabled = false
                }
            } else {
                await this.log(`000 - No matching account found for ${this.profile.shippingAddress.email}, please add one under www.metallica.com`, 'error')
                this.enabled = false
            }

            // Login
            const cookieId = await this.randomStr()
            const loginPage = await this.loginGet()
            const loggedIn = await this.loginPost(loginPage)
            if (this.enabled) {
                await got.post('http://localhost:3030/cookies', {
                    responseType: 'json',
                    json: {
                        id: cookieId,
                        cookies: JSON.stringify(await this.config.cookieJar.getCookies('https://www.metallica.com'))
                    }
                })

                // Get cart and clear it
                this.data.csrf.cart = await this.cartGet(true)
                await this.cartClear()

                // Monitor
                switch (this.inputType) {
                    case 'KW':
                        this.pInput = this.pInput.map(p => p.toLowerCase())
                        this.positives = this.pInput.filter(keyword => keyword[0] === '+')
                        this.negatives = this.pInput.filter(keyword => keyword[0] === '-')
                        this.pInput = await this.monitorKW()
                        break;
                    case 'DL':
                        this.pInput = this.pInput.split('.html')[0].split('/')
                        this.pInput = this.pInput[this.pInput.length - 1]
                        break;
                    case 'PID':
                        break;
                }

                // Add to cart
                this.data.cart = await this.cartAdd()
                if (this.enabled) {
                    // change this to this.feed?
                    await got.post('http://localhost:3030/monitor', {
                        responseType: 'json',
                        json: {
                            platform: `Metallica - ${this.name} - ${this.profile.shippingAddress.email}`,
                            site: 'Metallica',
                            type: 'Checkout',
                            title: this.data.cart.cart[0].name,
                            link: this.data.cart.cart[0].url,
                            price: (this.data.cart.cart[0].price).toFixed(2),
                            image: 'https://cdn.discordapp.com/attachments/903009397637283920/923088597723279360/Frame_4stackd-icon-bg.png',
                            variants: [`https://www.metallica.com/shipping`],
                            cookies: cookieId
                        }
                    })

                    // Checkout steps
                    await new Promise(r => setTimeout(r, 1))
                    this.data.csrf.cart = await this.cartGet()
                    await new Promise(r => setTimeout(r, 1))
                    this.data.csrf.checkout = await this.checkoutCreate()
                    await new Promise(r => setTimeout(r, 1))
                    const lowestShippingMethod = await this.getAvailableShippingCodes()
                    await new Promise(r => setTimeout(r, 1))
                    await this.checkoutGetShipping(lowestShippingMethod)
                    await new Promise(r => setTimeout(r, 1))
                    this.data.csrf.shipping = await this.checkoutSubmitShipping(lowestShippingMethod)

                    // Submit checkout flow
                    await this.flowComplete()

                } else {
                    await this.log(`000 - Task stopped`, 'error')
                }
            } else {
                await this.log(`000 - Task stopped`, 'error')
            }
        } catch (error) {
            await this.log(`000 - Error in flow: ${error.message}`, 'error')
            this.enabled = false
        }
    };

    // Complete checkout flow
    async flowComplete() {
        if (this.enabled && this.retry < 3) {
            try {

                // Submit billing
                this.data.csrf.billing = await this.checkoutSubmitBilling()
                await new Promise(r => setTimeout(r, 1))

                // Submit order
                const submitOrder = await this.checkoutSubmitOrder()

                // submit order result logic
                // Success
                if (this.enabled && submitOrder && submitOrder.status === 'success') {

                    await this.staffLog('https://www.metallica.com',
                        this.data.cart.cart[0].name,
                        parseFloat(this.data.cart.cart[0].price).toFixed(2),
                        'https://cdn.discordapp.com/attachments/714346599735754762/843321954688565278/blue.png'
                    )
                    await this.successAnalytics('https://www.metallica.com/on/demandware.store/Sites-Metallica-Site/default/Login-LoginForm?scope=', 'Metallica', submitOrder.order_number) //url, type, order_number = false
                    await this.userWebhook(`https://www.metallica.com`, this.data.cart.cart[0].name, parseFloat(this.data.cart.cart[0].price).toFixed(2), `https://cdn.discordapp.com/attachments/903009397637283920/923088597723279360/Frame_4stackd-icon-bg.png`)

                    // Fail
                } else if (this.enabled && submitOrder && submitOrder.status === 'fail') {
                    // Figure out what kind of error it was
                    if (submitOrder.message.includes('Please review your payment settings and try again')) {
                        await this.log('000 - Card decline', 'error')
                        this.retry++
                        await new Promise(r => setTimeout(r, 1000))
                        return await this.flowComplete()
                    } else if (submitOrder.message) {
                        // This is needs to be retried, I think this just means their server died a little
                        // Oops! Something went wrong and your order could not be completed. Don&rsquo;t worry - you haven&rsquo;t been charged. Please wait a minute and give it another shot! Have a question? Submit a support ticket at http://www.metallica.com/contactus. 
                        await this.log('000 - Submitting order error unknown. retrying...', 'error')
                        await new Promise(r => setTimeout(r, 1000))
                        return await this.flowComplete()
                    } else {
                        await this.log('000 - Submitting order error unknown. retrying...', 'error')
                        this.retry++
                        await new Promise(r => setTimeout(r, 1000))
                        return await this.flowComplete()
                    }
                }
            } catch (error) {
                await this.log(`000 - Error in complete checkout flow: ${error.message}`, 'error')
                this.enabled = false
            }
        } else {
            await this.log(`000 - Task stopped`, 'error')
        }
    }
}

module.exports.flow = async function flow(obj) {
    const instance = new MENormal(obj.group, obj.name, obj.inputType, obj.pInput, obj.quantity, obj.profile, obj.proxyList, obj.port);
    await instance.flow();
}

module.exports.MENormal = MENormal

// !TODO
// - Remove the sleep(1) if i remember correctly I did this to avoid blocks