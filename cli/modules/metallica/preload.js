const kleur = require('kleur');
const moment = require("moment");
const qs = require('qs');
const fs = require('fs')
const { parse } = require('node-html-parser')
const Utils = require('../utils/utils')
const got = require('got')
const tunnel = require('tunnel')
const {CookieJar} = require('tough-cookie')
const {promisify} = require('util')

const MEPreload = class MEPreload extends Utils {
    constructor (group, name, inputType, pInput, quantity, profile, proxyList) {
        super(group, name, 'https://www.metallica.com', inputType, pInput, null, null, quantity, profile, proxyList, 'Metallica', 'Preload (TESTING')
        this.fullPath = resolve(`${__filename}`)
		this.port = port
    }

    async flow () {
        this.enabled = true
        this.settings = await this.getSettings()
        this.check = true
        this.retry = 0
        this.obj = {}
        this.obj.csrf = {}
        this.obj.keys = {}
        this.obj.ua = this.userAgent()
        this.proxy = await this.proxyGot()
        this.cookieJar = new CookieJar();
        const setCookie = promisify(this.cookieJar.setCookie.bind(this.cookieJar));
        const states = {Alabama:'AL',Alaska:'AK',Arizona:'AZ',Arkansas:'AR',California:'CA',Colorado:'CO',Connecticut:'CT',Delaware:'DE',Florida:'FL',Georgia:'GA',Hawaii:'HI',Idaho:'ID',Illinois:'IL',Indiana:'IN',Iowa:'IA',Kansas:'KS',Kentucky:'KY',Louisiana:'LA',Maine:'ME',Maryland:'MD',Massachusetts:'MA',Michigan:'MI',Minnesota:'MN',Mississippi:'MS',Missouri:'MO',Montana:'MT',Nebraska:'NE',Nevada:'NV','New Hampshire':'NH','New Jersey':'NJ','New Mexico':'NM','New York':'NY','North Carolina':'NC','North Dakota':'ND',Ohio:'OH',Oklahoma:'OK',Oregon:'OR',Pennsylvania:'PA','Rhode Island':'RI','South Carolina':'SC','South Dakota':'SD',Tennessee:'TN',Texas:'TX',Utah:'UT',Vermont:'VT',Virginia:'VA',Washington:'WA','West Virginia':'WV',Wisconsin:'WI',Wyoming:'WY'}
        this.profile.billingAddress.state = states[this.profile.billingAddress.state]
        this.profile.shippingAddress.state = states[this.profile.shippingAddress.state]

        const accounts = (await got.get('http://localhost:3030/accounts', {responseType: 'json'})).body
        if (accounts['www.metallica.com']) {
            if (accounts['www.metallica.com'][this.profile.shippingAddress.email]) {
                // Start account here
                this.account = accounts['www.metallica.com'][this.profile.shippingAddress.email]
                // console.log(this.account)
                // await this.login()
            } else {
                await this.log(`000 - No matching account found, please add a matching ${this.profile.shippingAddress.email}`, 'error')
                this.enabled = false
            }
        } else {
            await this.log(`000 - No account found, please add a matching ${this.profile.shippingAddress.email}`, 'error')
            this.enabled = false
        }
        const cookieId = this.randomStr()
        const loginPage = await this.getLogin()
        const loggedIn = await this.postLogin(loginPage)
        await got.post('http://localhost:3030/cookies', {
            responseType: 'json',
            json: {
                id: cookieId,
                cookies: JSON.stringify(await this.cookieJar.getCookies('https://www.metallica.com'))
            }
        })
        this.obj.csrf.cart = await this.getCart(true)
        await this.clearCart()

        // await console.log(this.obj.csrf.cart) // I think it should cus nothing is in cart or if there is then well it would
        switch (this.inputType) {
            case 'KW':
                this.pInput = this.pInput.map(p => p.toLowerCase())
                this.positives = this.pInput.filter(keyword => keyword[0] === '+')
                this.negatives = this.pInput.filter(keyword => keyword[0] === '-')
                this.pInput = await this.kwMonitor()
                break;
            case 'DL':
                this.pInput = this.pInput.split('.html')[0].split('/')
                this.pInput = this.pInput[this.pInput.length - 1]
                break;
            case 'PID':
                break;
        }
        this.obj.cart = await this.atc()
        if (this.enabled) {
            await new Promise(r => setTimeout(r, 1))
            await got.post('http://localhost:3030/monitor', {
                responseType: 'json',
                json: {
                    platform: `Metallica - ${this.name} - ${this.profile.shippingAddress.email}`,
                    site: 'Metallica',
                    type: 'Checkout',
                    title: this.obj.cart.cart[0].name,
                    link:  this.obj.cart.cart[0].url,
                    price: (this.obj.cart.cart[0].price).toFixed(2),
                    image: 'https://cdn.discordapp.com/attachments/714346599735754762/843321954688565278/blue.png',
                    variants: [`https://www.metallica.com/shipping`],
                    cookies: cookieId
                }
            })
            await new Promise(r => setTimeout(r, 1))
            this.obj.csrf.cart = await this.getCart()
            await new Promise(r => setTimeout(r, 1))
            this.obj.csrf.checkout = await this.genCheckout()
            await new Promise(r => setTimeout(r, 1))
            const getShipping = await this.getShipping()
            await new Promise(r => setTimeout(r, 1))
            this.obj.csrf.shipping = await this.postShipping()
            await new Promise(r => setTimeout(r, 1))
            this.obj.csrf.billing =  await this.postBilling()
            await new Promise(r => setTimeout(r, 1))
            const submitOrder = await this.submitOrder()
            if (submitOrder && submitOrder.status === 'success') {
                await this.staffLog('https://www.metallica.com', 
                    this.obj.cart.cart[0].name, 
                    parseFloat(this.obj.cart.cart[0].price).toFixed(2), 
                    'https://cdn.discordapp.com/attachments/714346599735754762/843321954688565278/blue.png'
                )
                await this.userWebhook('https://www.metallica.com', 
                    this.obj.cart.cart[0].name, 
                    parseFloat(this.obj.cart.cart[0].price).toFixed(2), 
                    'https://cdn.discordapp.com/attachments/714346599735754762/843321954688565278/blue.png'
                )
                await this.successAnalytics ('https://www.metallica.com/on/demandware.store/Sites-Metallica-Site/default/Login-LoginForm?scope=', 'Metallica', submitOrder.order_number) //url, type, order_number = false

                //'https://www.metallica.com/on/demandware.store/Sites-Metallica-Site/default/Login-LoginForm?scope='
            }
        }
    }

    async getLogin() {
        if (this.enabled) {
            try {
                const response = await got.get('https://www.metallica.com/login/?original=%2Faccount%2F', {
                    responseType: 'text',
                    headers: {
                        'User-Agent': this.obj.ua, 
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8', 
                        'Accept-Language': 'en-US,en;q=0.5', 
                        'Referer': 'https://www.metallica.com/', 
                        'DNT': '1', 
                        'Connection': 'keep-alive', 
                        'Upgrade-Insecure-Requests': '1', 
                        'Sec-Fetch-Dest': 'document', 
                        'Sec-Fetch-Mode': 'navigate', 
                        'Sec-Fetch-Site': 'same-origin', 
                        'Sec-Fetch-User': '?1', 
                        'Cache-Control': 'max-age=0', 
                        'TE': 'trailers'
                    },
                    cookieJar: this.cookieJar,
                    agent: this.proxyList === 'local' ? false : this.proxy
                })
                const root = parse(response.body)
                const csrf = root.querySelector('[name="csrf_token"]').attrs.value
                const inputs = root.querySelectorAll('.input-text')
                let loginCode = ''
                for (let i = 0; i < inputs.length; i++) {
                    const input = inputs[i]
                    if (input.attrs.id.includes('dwfrm_login_username_')) {
                        loginCode = input.attrs.id.split('_')[3]
                        break;
                    }
                }
                // console.log()
                // await this.log(`${response.statusCode} - Got login page | ${loginCode} | ${csrf}`)
                await this.log(`${response.statusCode} - Got login page`)
                this.obj.csrf.login = csrf
                // const loginUsername = `dwfrm_login_username_${loginCode}`
                // const loginPassword = `dwfrm_login_password_${loginCode}`
                return {
                    username: `dwfrm_login_username_${loginCode}`,
                    password: `dwfrm_login_password_${loginCode}`
                }
            } catch(err) {
                await this.log(`${err.response ? err.response.statusCode : '000'} - Error getting login`, 'error')
                this.enabled = false
            }
        }
    };

    async postLogin (login) {
        if (this.enabled) {
            try {
                const response = await got.post('https://www.metallica.com/on/demandware.store/Sites-Metallica-Site/default/Login-LoginForm?scope=', {
                    responseType: 'text',
                    body: qs.stringify({
                        [login.username]: this.profile.shippingAddress.email,
                        [login.password]: this.account,
                        "dwfrm_login_login": "Login",
                        "csrf_token": this.obj.csrf.login
                    }),
                    headers: {
                        'User-Agent': this.obj.ua, 
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8', 
                        'Accept-Language': 'en-US,en;q=0.5', 
                        'Content-Type': 'application/x-www-form-urlencoded', 
                        'Origin': 'https://www.metallica.com', 
                        'DNT': '1', 
                        'Connection': 'keep-alive', 
                        'Referer': 'https://www.metallica.com/login/?original=%2Faccount%2F', 
                        'Upgrade-Insecure-Requests': '1', 
                        'Sec-Fetch-Dest': 'document', 
                        'Sec-Fetch-Mode': 'navigate', 
                        'Sec-Fetch-Site': 'same-origin', 
                        'Sec-Fetch-User': '?1', 
                        'Pragma': 'no-cache', 
                        'Cache-Control': 'no-cache', 
                        'TE': 'trailers'
                    },
                    cookieJar: this.cookieJar,
                    agent: this.proxyList === 'local' ? false : this.proxy,
                    followRedirect: true,
                    followRedirect: false

                })
                if (response.statusCode === 302) {
                    await this.log(`${response.statusCode} - Logged in`)
                } else {
                    throw 'login error'
                }
                // await fs.writeFile('./test-site.html', response.data, err => {err && console.log('Error updating profiles')})
            } catch (err) {
                // console.log(err.response.config)
                await this.log(`${err.response ? err.response.statusCode : '000'} - Error logging in: ${err.response ? '' : err.message}`, 'error') // NEED MAJOR FIX, AXIOS WAS WORKING WITHOUT THE 410, fix is reditect false and 302 vs 200
            }
        }
    };

    async getCart(clear = false) {
        if (this.enabled) {
            try {
                const response = await got.get('https://www.metallica.com/cart/', {
                    responseType: 'text',
                    headers: {
                        'User-Agent': this.obj.ua, 
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8', 
                        'Accept-Language': 'en-US,en;q=0.5', 
                        'Referer': `https://www.metallica.com/store/${this.pInput}.html?cgid=store`, 
                        'DNT': '1', 
                        'Connection': 'keep-alive', 
                        'Upgrade-Insecure-Requests': '1', 
                        'Sec-Fetch-Dest': 'document', 
                        'Sec-Fetch-Mode': 'navigate', 
                        'Sec-Fetch-Site': 'same-origin', 
                        'Sec-Fetch-User': '?1', 
                        'TE': 'trailers'
                    },
                    cookieJar: this.cookieJar,
                    agent: this.proxyList === 'local' ? false : this.proxy,
                })
                // await fs.writeFile('./test-site.html', response.body, err => {err && console.log('Error updating profiles')})
                const root = parse(response.body)
                if (root.querySelector('[name="csrf_token"]')) {
                    const csrf = root.querySelector('[name="csrf_token"]').attrs.value
                    if (root.querySelector('.notavailable')) {
                        if (root.querySelector('.notavailable').innerText.includes('Out of Stock')) {
                            throw 'OOS'
                        }
                    } else {
                        // this.obj.userNameCode = root.querySelector('.emailAddress').attrs.id
                        // await this.log(`${response.status} - Recieved cart | ${this.obj.userNameCode}`)
                        this.cartEmpty = false
                        // await this.log(`${response.statusCode} - Recieved cart | ${csrf}`)
                        await this.log(`${response.statusCode} - Recieved cart`)
                    }
                    return csrf
                } else {
                    this.cartEmpty = true
                }
            } catch (err) {
                await this.log(`${err.response ? err.response.statusCode : '000'} - ${err === 'OOS' ? 'OOS CART' : 'Error getting cart'}`, 'error')
                // console.log(err)
                if (err.response) {
                    if (err.response.status === 403 || err.response.status === 429 || err.response.status === 430) {
                        await this.proxyGot()
                    }
                    await new Promise(r => setTimeout(r, 1000))
                    const reGetCart = await this.getCart()
                    return reGetCart
                } else if (err === 'OOS') {
                    if (clear) {
                        await this.clearCart()
                    } else {
                        await new Promise(r => setTimeout(r, 1000))
                        const reGetCart = await this.getCart()
                        return reGetCart
                    }
                } else {
                    this.enabled = false
                    await console.log(err)
                }
            }
        }
    };

    async clearCart() {
        if (this.enabled && !this.cartEmpty) {
            try {
                const response = await got.post('https://www.metallica.com/on/demandware.store/Sites-Metallica-Site/default/Cart-SubmitForm', {
                    responseType: 'text',
                    body: qs.stringify({
                        'dwfrm_cart_shipments_i0_items_i0_quantity': '1',
                        'dwfrm_cart_shipments_i0_items_i0_quantity': '1',
                        // 'dwfrm_cart_shipments_i0_items_i0_quantity': '1',
                        'dwfrm_cart_shipments_i0_items_i0_deleteProduct': 'Remove',
                        'dwfrm_cart_couponCode': '',
                        'dwfrm_cart_updateCart': 'dwfrm_cart_updateCart',
                        'csrf_token': this.obj.csrf.cart
                    }),
                    headers: {
                        'User-Agent': this.obj.ua, 
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8', 
                        'Accept-Language': 'en-US,en;q=0.5', 
                        'Referer': 'https://www.metallica.com/cart/', 
                        'Content-Type': 'application/x-www-form-urlencoded', 
                        'Origin': 'https://www.metallica.com', 
                        'DNT': '1', 
                        'Connection': 'keep-alive', 
                        'Upgrade-Insecure-Requests': '1', 
                        'Sec-Fetch-Dest': 'document', 
                        'Sec-Fetch-Mode': 'navigate', 
                        'Sec-Fetch-Site': 'same-origin', 
                        'Sec-Fetch-User': '?1', 
                        'TE': 'trailers'
                    },
                    cookieJar: this.cookieJar,
                    agent: this.proxyList === 'local' ? false : this.proxy,
                })
                const root = parse(response.body)
                // const csrf = root.querySelector('[name="csrf_token"]').attrs.value
                if (response.body.includes('<h1>Your Shopping Cart is Empty</h1>')) {
                    this.cartEmpty = true
                }
                await this.log(`${response.statusCode} - ${this.cartEmpty ? 'Cart cleared' : 'Cart may not be cleared'}`)
                
            } catch(err) {
                await console.log(err)
            }
        }
    };

    async kwMonitor() {
        if (this.enabled) {
            try {
                // this.log('000 - KW monitor')
                let search = this.positives.map(kw => {
                    return kw.substr(1, kw.length-1)
                })
                // const response = await got.get(`https://www.metallica.com/search/?q=${search.join(' ')}&lang=default&id=${Math.floor(Math.random() * 9999999)}`, {
                const response = await got.get(`https://www.metallica.com/search/?q=${search.join(' ')}&lang=default&cgid=${this.randomStr()}`, {
                    responseType: 'text',
                    headers: {
                        'User-Agent': this.userAgent(),
                        'Accept-Language': 'en-US,en;q=0.5', 
                        'DNT': '1', 
                        'Connection': 'keep-alive', 
                        'Upgrade-Insecure-Requests': '1', 
                        'Sec-Fetch-Dest': 'document', 
                        'Sec-Fetch-Mode': 'navigate', 
                        'Sec-Fetch-Site': 'none', 
                        'Sec-Fetch-User': '?1'
                    },
                    agent: await this.proxyGot('monitor'),
                    followRedirect: false
                })
                // await this.log(`${}`)
                // console.log(response.headers)
                // console.log(response.url)
                // console.log(response.data.split('html')[0])
                let redirect = response.headers.location ? response.headers.location : response.url
                // console.log(redirect)

                if (redirect.includes('https://www.metallica.com/store/')) {
                    redirect = redirect.split('.html')[0].split('/')
                    redirect = redirect[redirect.length - 1]
                    await this.log(`${response.statusCode} - ${redirect}`)
                    return redirect
                } else if (redirect.includes('https://www.metallica.com/search/?q=')) {
                    const root = parse(response.body)
                    const parsed = root.querySelectorAll('.product-tile')
                    await this.log(`${response.statusCode} - ${parsed.length} products loaded`)
                    for (let i = 0; i < parsed.length; i++) {
                        const product = {
                            name: parsed[i].attrs['data-itemname'],
                            PID: parsed[i].attrs['data-itemid']
                        }
                        // Seeing if product.name matches keywords
                        const pMatches = await this.positives.filter(keyword => product.name.toLowerCase().includes(keyword.slice(1, keyword.length)))
                        const nMatches = await this.negatives.filter(keyword => product.name.toLowerCase().includes(keyword.slice(1, keyword.length)))
                        if (pMatches.length === this.positives.length && nMatches.length === 0) {
                            this.log(`000 - ${product.name}`)
                            return product.PID
                        }
                    }

                }
    
            } catch (err) {
                await this.log(`${err.response ? err.response.status : '000'} - Error KW monitoring ${err.response ? err.response.url : ''}`, 'error')
                // console.log(err)
            }
            await new Promise(r => setTimeout(r, 1000))
            // this.enabled = false
            const reMonitor = await this.kwMonitor()
            return reMonitor
        }
    };

    async pageGet (link) {
        try {
            const edit = `${link}&cgid=${this.randomStr()}`
            const response = await got.get(edit)
            const root = parse(response.body)
            const parsed = root.querySelectorAll('.product-tile')
            await this.log(`${response.statusCode} - ${parsed.length} products loaded`)
            for (let i = 0; i < parsed.length; i++) {
                const product = {
                    name: parsed[i].attrs['data-itemname'],
                    PID: parsed[i].attrs['data-itemid']
                }
                // Seeing if product.name matches keywords
                const pMatches = await this.positives.filter(keyword => product.name.toLowerCase().includes(keyword.slice(1, keyword.length)))
                const nMatches = await this.negatives.filter(keyword => product.name.toLowerCase().includes(keyword.slice(1, keyword.length)))
                if (pMatches.length === this.positives.length && nMatches.length === 0) {
                    await this.log(`000 - ${product.name}`)
                    return product.PID
                }
            }
        } catch (err) {
            await this.log(`${err.response ? err.response.statusCode : '000'}`)
        }
    }

    async atc() {
        if (this.enabled) {
            try {
                const response = await got.post('https://www.metallica.com/on/demandware.store/Sites-Metallica-Site/default/Cart-AddProduct?format=ajax', {
                    responseType: 'text',
                    body: qs.stringify({
                        // "maxQtyAvailable": "25",
                        // "purchaseLimit": "25.0",
                        // "totalQty": "36132",
                        "Quantity": `${this.quantity}`, // May need to remove this as I dont think it's the proper way of quantity
                        "uuid": "",
                        "cartAction": "update",
                        "variantSelected": true,
                        "pid": `${this.pInput}`,
                        "cgid": "store",
                        // "pageInstanceId": "product"
                    }),
                    headers: {
                        'User-Agent': this.obj.ua, 
                        'Accept': '*/*', 
                        'Accept-Language': 'en-US,en;q=0.5', 
                        'Referer': 'https://www.metallica.com/store/?srule=newest&viewAll=true', 
                        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8', 
                        'X-Requested-With': 'XMLHttpRequest', 
                        'Origin': 'https://www.metallica.com', 
                        'DNT': '1', 
                        'Connection': 'keep-alive', 
                        'Sec-Fetch-Dest': 'empty', 
                        'Sec-Fetch-Mode': 'cors', 
                        'Sec-Fetch-Site': 'same-origin', 
                        'TE': 'trailers'
                    },
                    cookieJar: this.cookieJar,
                    agent: this.proxyList === 'local' ? false : this.proxy,
                })
                const root = parse(response.body)
                // await fs.writeFile('./test-site.html', response.data, err => {err && console.log('Error updating profiles')})

                if (root.querySelector('.minicart-quantity').innerText === '0') {
                    throw 'OOS'
                }
                // Find something better than this
                const cart = JSON.parse(response.body.split("_etmc.push(['trackCart', ")[1].split(']);')[0])
                await this.log(`${response.statusCode} - Added: ${cart.cart[0].name}`)
                return cart
            } catch (err) {
                await this.log(`${err.response ? err.response.statusCode : '000'} - ${err === 'OOS' ? `${this.pInput} is OOS` : 'Error adding to cart' }`, err === 'OOS' ? 'gray' : 'error')
                // console.log(err)
                // await this.proxyGot()
                await new Promise(r => setTimeout(r, 1000))
                const reAtc = await this.atc()
                return reAtc
            }
        }
    };

    // Generates a checkout
    async genCheckout() {
        if (this.enabled) {
            try {
                const response = await got.post(`https://www.metallica.com/on/demandware.store/Sites-Metallica-Site/default/Cart-SubmitForm`, {
                    responseType: 'text',
                    body: qs.stringify({
                        // 'sign-up': 'on',
                        // [this.obj.userNameCode]: this.profile.billingAddress.email,
                        // 'dwfrm_login_unregistered': 'Checkout as Guest',
                        // 'csrf_token': this.obj.csrf.cart 
                        dwfrm_cart_checkoutCart:	"Checkout"
                    }),
                    headers: {
                        'User-Agent': this.obj.ua, 
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8', 
                        'Accept-Language': 'en-US,en;q=0.5', 
                        'Referer': 'https://www.metallica.com/cart/', 
                        'Content-Type': 'application/x-www-form-urlencoded', 
                        'Origin': 'https://www.metallica.com', 
                        'DNT': '1', 
                        'Connection': 'keep-alive', 
                        'Upgrade-Insecure-Requests': '1', 
                        'Sec-Fetch-Dest': 'document', 
                        'Sec-Fetch-Mode': 'navigate', 
                        'Sec-Fetch-Site': 'same-origin', 
                        'Sec-Fetch-User': '?1', 
                        'TE': 'trailers'
                    },
                    cookieJar: this.cookieJar,
                    agent: this.proxyList === 'local' ? false : this.proxy,
                })
                const root = parse(response.body)
                // fs.writeFile('./test-site.html', response.body, err => {err && console.log('Error updating profiles')})
                const csrf = root.querySelector('[name="csrf_token"]').attrs.value
                // await this.log(`${response.statusCode} - Generated checkout | ${csrf}`)
                await this.log(`${response.statusCode} - Generated checkout`)

                return csrf
            } catch (err) {
                await this.log(`${err.response ? err.response.statusCode : '000'} - Error generating checkout`, 'error')
                if (err.response) {
                    if (err.response.statusCode === 403 || err.response.statusCode === 429 || err.response.statusCode === 430) {
                        await this.proxyGot()
                    }
                    await new Promise(r => setTimeout(r, 1000))
                    const reGenCheckout = await this.genCheckout()
                    return reGenCheckout
                } else {
                    console.log(err)
                    this.enabled = false
                }
            }
        }
    }
    
    // Gets a shipping method
    async getShipping() {
        if (this.enabled) {
                try {
                    //&shippingMethodID=shqcustom-ups_mail_innovations&customFlag=
                    const response = await got.get(`https://www.metallica.com/on/demandware.store/Sites-Metallica-Site/default/COShipping-SelectShippingMethod?address1=${this.profile.shippingAddress.line1}&address2=${this.profile.shippingAddress.line2}&countryCode=us&postalCode=${this.profile.shippingAddress.postCode}&city=${this.profile.shippingAddress.city}&stateCodeRequired=true&stateCode=${this.profile.shippingAddress.state}&shippingMethodID=shqcustom-ups_mail_innovations&customFlag=`, {
                        responseType: 'json',
                        headers: {
                            'User-Agent': this.obj.ua, 
                            'Accept': 'application/json, text/javascript, */*; q=0.01', 
                            'Accept-Language': 'en-US,en;q=0.5', 
                            'Referer': 'https://www.metallica.com/shipping/', 
                            'X-Requested-With': 'XMLHttpRequest', 
                            'DNT': '1', 
                            'Connection': 'keep-alive', 
                            'Sec-Fetch-Dest': 'empty', 
                            'Sec-Fetch-Mode': 'cors', 
                            'Sec-Fetch-Site': 'same-origin', 
                            'TE': 'trailers'
                        },
                        cookieJar: this.cookieJar,
                        agent: this.proxyList === 'local' ? false : this.proxy,
                    })
                    // await console.log(response)
                    await this.log(`${response.statusCode} - Shipping codes ${response.body.shippingMethodID}`)
                    return response.body.shippingMethodID
                } catch(err) {
                    await this.log(`${err.response ? err.response.statusCode : '000'} - Error getting shipping rates`, 'error')
                    // if (err.response) {
                    //     if (err.response.statusCode === 403 || err.response.statusCode === 429 || err.response.statusCode === 430) {
                    //         this.proxy = await this.proxyGot()
                    //     }
                    //     await new Promise(r => setTimeout(r, 1000))
                    //     const reGetShipping = await this.getShipping()
                    //     return reGetShipping
                    // } else {
                    //     this.enabled = false
                    // }
                }
        }
    };

    // Async postShipping
    async postShipping() {
        if (this.enabled) {
            try {
                const response = await got.post(`https://www.metallica.com/billing-checkout/`, {
                    responseType: 'text',
                    body: qs.stringify({
                        'ups_isAddressUpdated': 'true',
                        'dwfrm_singleshipping_shippingAddress_addressFields_firstName': `${this.profile.shippingAddress.name.split(' ')[0]}`,
                        'dwfrm_singleshipping_shippingAddress_addressFields_lastName': `${this.profile.shippingAddress.name.split(' ')[1]}`,
                        'dwfrm_singleshipping_shippingAddress_addressFields_address1': `${this.profile.shippingAddress.line1}`,
                        'dwfrm_singleshipping_shippingAddress_addressFields_address2': `${this.profile.shippingAddress.line2}`,
                        'dwfrm_singleshipping_shippingAddress_addressFields_city': `${this.profile.shippingAddress.city}`,
                        'dwfrm_singleshipping_shippingAddress_addressFields_states_state': `${this.profile.shippingAddress.state}`,
                        'dwfrm_singleshipping_shippingAddress_addressFields_castates_castate': '',
                        'dwfrm_singleshipping_shippingAddress_addressFields_otherstates': '',
                        'dwfrm_singleshipping_shippingAddress_addressFields_country': 'us',
                        'dwfrm_singleshipping_shippingAddress_addressFields_postal': `${this.profile.shippingAddress.postCode}`,
                        'dwfrm_singleshipping_shippingAddress_addressFields_phone': `${this.profile.shippingAddress.phone}`,
                        'dwfrm_singleshipping_shippingAddress_useAsBillingAddress': 'true',
                        'dwfrm_singleshipping_shippingAddress_giftMessage': '',
                        'dwfrm_singleshipping_shippingAddress_shippingMethodID': 'shqusps-Priority Mail',
                        'dwfrm_singleshipping_shippingAddress_save': 'Continue to Billing',
                        'csrf_token': this.obj.csrf.checkout 
                    }),
                    headers: {
                        'User-Agent': this.obj.ua, 
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8', 
                        'Accept-Language': 'en-US,en;q=0.5', 
                        'Referer': 'https://www.metallica.com/shipping/', 
                        'Content-Type': 'application/x-www-form-urlencoded', 
                        'Origin': 'https://www.metallica.com', 
                        'DNT': '1', 
                        'Connection': 'keep-alive', 
                        'Upgrade-Insecure-Requests': '1', 
                        'Sec-Fetch-Dest': 'document', 
                        'Sec-Fetch-Mode': 'navigate', 
                        'Sec-Fetch-Site': 'same-origin', 
                        'Sec-Fetch-User': '?1', 
                        'TE': 'trailers'
                    },
                    cookieJar: this.cookieJar,
                    agent: this.proxyList === 'local' ? false : this.proxy,
                })

                const root = parse(response.body)
                // fs.writeFile('./test-site.html', response.body, err => {err && console.log('Error updating profiles')})
                const csrf = root.querySelector('[name="csrf_token"]').attrs.value
                // await this.log(`${response.statusCode} - Posted shipping | ${csrf}`)
                await this.log(`${response.statusCode} - Posted shipping`)
                return csrf
            } catch (err) {
                await this.log(`${err.response ? err.response.statusCode : '000'} - ${ err === 'OOS' ? 'Shipping => Cart | Item OOS' : 'Error posting shipping'}`, err === 'OOS' ? 'gray' : 'error')
                if (err === 'OOS' || err.response) {
                    if (err.response.statusCode === 403 || err.response.statusCode === 429 || err.response.statusCode === 430) {
                        await this.proxyGot()
                    }
                    await new Promise(r => setTimeout(r, 1000))
                    const rePostShipping = await this.postShipping(shipping)
                    return rePostShipping
                } else {
                    this.enabled = false
                }
            }
        }
    };

    // Posts email and card info
    async postBilling () {
        if (this.enabled) {
            try {
                const config = {
                    headers: { 
                        'User-Agent': this.obj.ua, 
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8', 
                        'Accept-Language': 'en-US,en;q=0.5', 
                        'Referer': 'https://www.metallica.com/billing/', 
                        'Content-Type': 'application/x-www-form-urlencoded', 
                        'Origin': 'https://www.metallica.com', 
                        'DNT': '1', 
                        'Connection': 'keep-alive', 
                        'Upgrade-Insecure-Requests': '1', 
                        'Sec-Fetch-Dest': 'document', 
                        'Sec-Fetch-Mode': 'navigate', 
                        'Sec-Fetch-Site': 'same-origin', 
                        'Sec-Fetch-User': '?1', 
                        'TE': 'trailers'
                    },
                    withCredentials: true
                }
                if (this.proxy !== 'local') {
                    config.httpsAgent = this.proxy
                }
                const response = await got.post('https://www.metallica.com/placeorder/', {
                    responseType: 'text',
                    body: qs.stringify({
                        'dwfrm_billing_paymentMethods_selectedPaymentMethodID': 'CREDIT_CARD',
                        'dwfrm_billing_paymentMethods_bml_year': '',
                        'dwfrm_billing_paymentMethods_bml_month': '',
                        'dwfrm_billing_paymentMethods_bml_day': '',
                        'dwfrm_billing_paymentMethods_bml_ssn': '',
                        'dwfrm_billing_paymentMethods_creditCard_owner': `${this.profile.billingAddress.name}`,
                        'dwfrm_billing_paymentMethods_creditCard_type': `${this.profile.paymentDetails.cardType}`,
                        'dwfrm_billing_paymentMethods_creditCard_selectedCardID': '',
                        'dwfrm_billing_paymentMethods_creditCard_number': `${this.profile.paymentDetails.cardNumber}`,
                        'dwfrm_billing_paymentMethods_creditCard_expiration_month': `${this.profile.paymentDetails.cardExpMonth}`,
                        'dwfrm_billing_paymentMethods_creditCard_expiration_year': `${this.profile.paymentDetails.cardExpYear}`,
                        'dwfrm_billing_paymentMethods_creditCard_cvn': `${this.profile.paymentDetails.cardCvv}`,
                        'dwfrm_billing_paymentMethods_creditCard_selectedCardID': '',
                        'dwfrm_billing_paymentMethods_creditCard_selectedCardID': '',
                        'csrf_token': this.obj.csrf.shipping,
                        'dwfrm_billing_giftCertCode': '',
                        'dwfrm_billing_couponCode': '',
                        'dwfrm_billing_save': 'true',
                        'dwfrm_billing_billingAddress_useAsBillingAddress': 'true',
                        'dwfrm_billing_billingAddress_addressFields_firstName': `${this.profile.billingAddress.name.split(' ')[0]}`,
                        'dwfrm_billing_billingAddress_addressFields_lastName': `${this.profile.billingAddress.name.split(' ')[1]}`,
                        'dwfrm_billing_billingAddress_addressFields_address1': `${this.profile.billingAddress.line1}`,
                        'dwfrm_billing_billingAddress_addressFields_address2': `${this.profile.billingAddress.line2}`,
                        'dwfrm_billing_billingAddress_addressFields_city': `${this.profile.billingAddress.city}`,
                        'dwfrm_billing_billingAddress_addressFields_postal': `${this.profile.billingAddress.postCode}`,
                        'dwfrm_billing_billingAddress_addressFields_states_state': `${this.profile.billingAddress.state}`,
                        'dwfrm_billing_billingAddress_addressFields_country': 'us',
                        'dwfrm_billing_billingAddress_addressFields_castates_castate': '',
                        'dwfrm_billing_billingAddress_addressFields_otherstates': '',
                        'dwfrm_billing_billingAddress_addressFields_phone': `${this.profile.billingAddress.phone}`,
                        'dwfrm_billing_billingAddress_email_emailAddress': `${this.profile.billingAddress.email}`,
                        'pp_ready': '0',
                        'dwfrm_billing_save': 'Continue to Place Order' 
                    }),
                    headers: {
                        'User-Agent': this.obj.ua, 
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8', 
                        'Accept-Language': 'en-US,en;q=0.5', 
                        'Referer': 'https://www.metallica.com/billing/', 
                        'Content-Type': 'application/x-www-form-urlencoded', 
                        'Origin': 'https://www.metallica.com', 
                        'DNT': '1', 
                        'Connection': 'keep-alive', 
                        'Upgrade-Insecure-Requests': '1', 
                        'Sec-Fetch-Dest': 'document', 
                        'Sec-Fetch-Mode': 'navigate', 
                        'Sec-Fetch-Site': 'same-origin', 
                        'Sec-Fetch-User': '?1', 
                        'TE': 'trailers'
                    },
                    cookieJar: this.cookieJar,
                    agent: this.proxyList === 'local' ? false : this.proxy,
                })
                // fs.writeFile('./test-site.html', response.body, err => {err && console.log('Error updating profiles')})
                const root = parse(response.body)
                const csrf = root.querySelector('[name="csrf_token"]').attrs.value
                // await this.log(`${response.statusCode} - Posted billing | ${csrf}`)
                await this.log(`${response.statusCode} - Posted billing`)
                return csrf
            } catch (err) {
                await this.log(`${err.respone ? err.response.statusCode : '000'} - Error posting billing`, 'error')
                if (err.response) {
                    if (err.response.statusCode === 403 || err.response.statusCode === 429 || err.response.statusCode === 430) {
                        await this.proxyGot()
                    }
                    await new Promise(r => setTimeout(r, 1000))
                    const rePostBilling = await this.postBilling()
                    return rePostBilling
                } else {
                    this.enabled = false
                }
            }
        }
    };

    async submitOrder () {
        if (this.enabled) {
            try {
                await this.log('000 - Submitting order')
                const response = await got.post('https://www.metallica.com/orderconfirmation/', {
                    responseType: 'text',
                    body: qs.stringify({
                        'submit': 'Place Order',
                        'csrf_token': this.obj.csrf.billing
                    }),
                    headers: {
                        'User-Agent': this.obj.ua, 
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8', 
                        'Accept-Language': 'en-US,en;q=0.5', 
                        'Referer': 'https://www.metallica.com/placeorder/', 
                        'Content-Type': 'application/x-www-form-urlencoded', 
                        'Origin': 'https://www.metallica.com', 
                        'DNT': '1', 
                        'Connection': 'keep-alive', 
                        'Upgrade-Insecure-Requests': '1', 
                        'Sec-Fetch-Dest': 'document', 
                        'Sec-Fetch-Mode': 'navigate', 
                        'Sec-Fetch-Site': 'same-origin', 
                        'Sec-Fetch-User': '?1', 
                        'TE': 'trailers'
                    },
                    cookieJar: this.cookieJar,
                    agent: this.proxyList === 'local' ? false : this.proxy,
                })
                // fs.writeFile('./test-site.html', response.body, err => {err && console.log('Error updating profiles')})
                const root = parse(response.body)
                const csrf = root.querySelector('[name="csrf_token"]').attrs.value
                if (root.querySelector('.error-form')) { //error here .innerText.includes('could not')
                    throw root.querySelector('.error-form').innerText
                }

                if (root.querySelector('.order-confirmation-details')) {
                    const message = JSON.parse(response.body.split("_etmc.push(['trackConversion', ")[1].split(' ]);')[0]).order_number
                    await this.log(`${response.statusCode} - Check email | Order #${message}`, 'success')
                    return {status: 'success', order_number: message}
                }
                this.log(`${response.statusCode} - ${response.url}`)
                return {status: 'decline or error', csrf: csrf}

            } catch (err) {
                await this.log(`${err.response ? err.response.statusCode : '000'} - ${typeof err === 'string' ? err : 'Error submitting order, check monitor to checkout'}`, 'prompt')
                // console.log(err.response.headers)
                // fs.writeFile('./test-site.html', err.response.body, err => {err && console.log('Error updating profiles')})
                // console.log(err.response.url)
                // console.log(err)
                if (err.response) {
                    if (err.response.statusCode === 403 || err.response.statusCode === 429 || err.response.statusCode === 430) {
                        this.proxy = await this.proxyGot()
                    }
                    // await new Promise(r => setTimeout(r, 1000))
                    // const reSubmitOrder = await this.submitOrder()
                    // return reSubmitOrder
                } else {
                    // await new Promise(r => setTimeout(r, 1000))
                    // const reSubmitOrder = await this.submitOrder()
                    // return reSubmitOrder
                }
            }
        }
    };
};

module.exports.flow = async function flow(obj) {
    const instance = new MEPreload(obj.group, obj.name, obj.inputType, obj.pInput, obj.quantity, obj.profile, obj.proxyList, obj.port);
    await instance.flow();
}

module.exports.MEPreload = MEPreload