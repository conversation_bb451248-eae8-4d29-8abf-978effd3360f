const got = require('got');
const qs = require('qs');
const { match } = require('../utils/keyword');
const Utils = require('../utils/utils');
const { parse } = require('node-html-parser');
const xmlParser = require('fast-xml-parser');
const _flatten = require('lodash/flatten');
const _find = require('lodash/find');
const tunnel = require('tunnel');

const feathers = require('@feathersjs/feathers');
const socketio = require('@feathersjs/socketio-client');
const io = require('socket.io-client');
const socket = io('http://localhost:3030');
const app = feathers();
app.configure(socketio(socket));

module.exports = class Methods extends Utils {
	constructor(group, name, link, inputType, pInput, oInput, randomOos, quantity, profile, proxyList, platform, mode) {
		super(group, name, link, inputType, pInput, oInput, randomOos, quantity, profile, proxyList, platform, mode);
	}

	// Create a session to retrieve the crumb
	async generateSession() {
		if (this.enabled) {
			this.log('Generating session');
			try {
				const response = await got.get(`${this.parsedLink.origin}/?format=json-pretty`, {
					responseType: 'json',
					headers: {
						'User-Agent': this.userAgent(),
						Accept: 'text/javascript, text/html, application/xml, text/xml, */*',
						'Accept-Language': 'en-US,en;q=0.5',
						Referer: `${this.link}/`,
						'X-Requested-With': 'XMLHttpRequest',
						'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
						DNT: '1',
						Connection: 'keep-alive',
						'Sec-Fetch-Dest': 'empty',
						'Sec-Fetch-Mode': 'cors',
						'Sec-Fetch-Site': 'same-origin',
						TE: 'trailers'
					},
					cookieJar: this.cookieJar,
					agent: this.proxyList === 'local' ? false : this.proxy
				});
				// const response = await this.getWithTimeout(`${this.link}`, config)
				let crumb = this.parseCrumb(response);
				this.obj.storeInfo = response.body.website;
				this.obj.storeSetting = response.body.websiteSettings;
				return crumb;
			} catch (err) {
				if (err !== undefined) {
					err.response ? this.log(`${err.response.statusText}`, 'error') : this.log(`${err.message}`, 'error');
				}
				this.proxy = await this.proxyGot();
			}
			return await this.generateSession();
		}
	}

	async atc() {
		if (this.enabled) {
			try {
				const url = `${this.parsedLink.protocol}//${this.parsedLink.host
					}/api/commerce/shopping-cart/entries?${qs.stringify({
						crumb: this.obj.crumb
					})}`;
				const response = await got.post(url, {
					responseType: 'json',
					headers: {
						accept: '*/*',
						'accept-encoding': 'gzip, br',
						'accept-language': 'en-US,en;q=0.9',
						connection: 'keep-alive',
						'content-type': 'application/json',
						'user-agent': this.userAgent()
					},
					json: {
						additionalFields: 'null',
						itemId: this.obj.item.OID,
						quantity: this.quantity,
						sku: this.obj.item.sku ?? null
					},
					cookieJar: this.cookieJar,
					agent: this.proxyList === 'local' ? false : this.proxy
				});

				if (response.statusCode === 200) return response.body;
			} catch (err) {
				if (err !== undefined) {
					const msg = 'Error carting';
					err.response
						? this.log(`${msg} ${JSON.stringify(err.response.body) || err.response.statusCode}`, 'error')
						: this.log(`${msg} ${err.message}`, 'error');
				}
				this.proxy = await this.proxyGot();
			}
			await new Promise((r) => setTimeout(r, 1000));
			return await this.atc();
		}
	}

	async generateCheckout(hardCodedUrl = undefined) {
		let checkoutConfig = {
			cartToken: undefined,
			xsrfToken: undefined,
			stripeLivePublicApiKey: undefined,
			stripeUserId: undefined,
			supportedCountries: [],
			supportedRegions: []
		};
		try {
			this.log(`Generating checkout`);
			const response = await got.get(
				hardCodedUrl ? hardCodedUrl : `${this.parsedLink.protocol}//${this.parsedLink.host}/commerce/goto-checkout`,
				{
					responseType: 'text',
					headers: {
						accept: '*/*',
						'accept-encoding': 'gzip, br',
						'accept-language': 'en-US,en;q=0.9',
						connection: 'keep-alive',
						'content-type': 'application/json',
						'user-agent': this.userAgent()
					},
					cookieJar: this.cookieJar,
					agent: this.proxyList === 'local' ? false : this.proxy
				}
			);

			try {
				this.obj.crumb = this.parseSecureCrumb(response);
				this.obj.upgradedToSecure = true;
			} catch (err) {
				this.obj.crumb = this.parseCrumb(response);
				// make sure to set crumb to false, important if doing preauth flow
				this.obj.upgradedToSecure = false;
			}
			checkoutConfig.cartToken = this.parseCartToken(response.url);
			Object.assign(checkoutConfig, this.parseCheckoutConfig(response.body));
			for (let key in checkoutConfig) {
				if (
					checkoutConfig[key] === undefined &&
					key !== 'customCheckoutForm' &&
					key !== 'stripeUserId' &&
					key !== 'stripeLivePublicApiKey' &&
					key !== 'paypalMerchantId'
				) {
					throw new Error(`Something went wrong configuring checkout`);
				}
				if (checkoutConfig['items'].length === 0) {
					throw new Error(`CART_EMPTY`)
				}
			}
			if (!checkoutConfig['stripeLivePublicApiKey'] && !checkoutConfig['paypalMerchantId'])
				throw new Error(`Store does not have a payment method configured.`);
			return checkoutConfig;
		} catch (err) {
			if (err !== undefined) {
				const msg = 'Error creating checkout';
				err.response
					? this.log(`${msg} ${JSON.stringify(err.response.body) || err.response.statusCode}`, 'error')
					: this.log(`${msg} ${err.message}`, 'error');
				if (err.message === `CART_EMPTY`) {
					this.log(`Cart empty. Retrying generating checkout.`)
				}
			}
			this.proxy = await this.proxyGot();
		}
		await new Promise((r) => setTimeout(r, 1000));
		return await this.generateCheckout();
	}

	async submitEmail() {
		if (this.enabled) {
			try {
				this.log(`Submitting contact information`);
				const { email } = this.profile.shippingAddress;
				const { cartToken, xsrfToken } = this.obj.checkoutConfig;
				// const url = `${this.parsedLink.protocol}//${this.parsedLink.host}/api/3/commerce/cart/${cartToken}/customer`;
				// const url = `https://secure.squarespace.com/api/3/commerce/cart/${cartToken}/customer`
				const response = await got.put(
					this.obj.upgradedToSecure
						? `https://secure.squarespace.com/api/3/commerce/cart/${cartToken}/customer`
						: `${this.parsedLink.protocol}//${this.parsedLink.host}/api/3/commerce/cart/${cartToken}/customer`,
					{
						responseType: 'json',
						json: { email },
						headers: {
							accept: '*/*',
							'accept-encoding': 'gzip, br',
							'accept-language': 'en-US,en;q=0.9',
							connection: 'keep-alive',
							'content-type': 'application/json',
							'x-csrf-token': this.obj.crumb,
							'x-siteuser-xsrf-token': xsrfToken,
							'user-agent': this.userAgent()
						},
						cookieJar: this.cookieJar,
						agent: this.proxyList === 'local' ? false : this.proxy
					}
				);
				if (response.statusCode === 200) {
					return true;
				} else throw new Error(`Something went wrong submitting contact information`);
			} catch (err) {
				if (err !== undefined) {
					const msg = `Something went wrong submitting contact information`;
					err.response
						? this.log(`${msg} ${JSON.stringify(err.response.body) || err.response.statusCode}`, 'error')
						: this.log(`${msg} ${err.message}`, 'error');
				}
				this.proxy = await this.proxyGot();
			}
			await new Promise((r) => setTimeout(r, 1000));
			return await this.submitEmail();
		}
	}

	async submitShipping() {
		if (this.enabled) {
			try {
				this.log(`Submitting shipping information`);
				const { cartToken, xsrfToken } = this.obj.checkoutConfig;
				const selectedCountry = _find(
					this.countryRegionData,
					(o) => o.countryName === this.profile.shippingAddress.country
				);
				const selectedState = _find(selectedCountry.regions, (o) => o.name === this.profile.shippingAddress.state);
				// const url = `${this.parsedLink.protocol}//${this.parsedLink.host}/api/3/commerce/cart/${cartToken}/shipping/location`;
				// const url = `https://secure.squarespace.com/api/3/commerce/cart/${cartToken}/shipping/location`;
				const response = await got.put(
					this.obj.upgradedToSecure
						? `https://secure.squarespace.com/api/3/commerce/cart/${cartToken}/shipping/location`
						: `${this.parsedLink.protocol}//${this.parsedLink.host}/api/3/commerce/cart/${cartToken}/shipping/location`,
					{
						responseType: 'json',
						json: {
							city: this.profile.shippingAddress.city,
							country: selectedCountry.countryShortCode,
							line1: this.profile.shippingAddress.line1,
							line2: this.profile.shippingAddress.line2,
							postalCode: this.profile.shippingAddress.postCode,
							region: selectedState.shortCode
						},
						headers: {
							accept: '*/*',
							'accept-encoding': 'gzip, br',
							'accept-language': 'en-US,en;q=0.9',
							connection: 'keep-alive',
							'content-type': 'application/json',
							'x-csrf-token': this.obj.crumb,
							'x-siteuser-xsrf-token': xsrfToken,
							'user-agent': this.userAgent()
						},
						cookieJar: this.cookieJar,
						agent: this.proxyList === 'local' ? false : this.proxy
					}
				);
				if (response.statusCode === 200) return (this.obj.orderConfig = response.body);
			} catch (err) {
				if (err !== undefined) {
					const msg = `Something went wrong submitting shipping information`;
					err.response
						? this.log(`${msg} ${JSON.stringify(err.response.body) || err.response.statusCode}`, 'error')
						: this.log(`${msg} ${err.message}`, 'error');
				}
				this.proxy = await this.proxyGot();
			}
			await new Promise((r) => setTimeout(r, 1000));
			return await this.submitShipping();
		}
	}

	// stripe
	async generatePaymentToken() {
		if (this.enabled) {
			this.log(`Generating payment token`);
			try {
				const stripeIds = await got.post('https://m.stripe.com/6', {
					responseType: 'json',
					headers: {
						accept: '*/*',
						'accept-encoding': 'gzip, br',
						'accept-language': 'en-US,en;q=0.9',
						connection: 'keep-alive',
						'user-agent': this.userAgent()
					},
					agent: this.proxyList === 'local' ? false : this.proxy
				});
				let urlEncodedParams = new URLSearchParams();
				// billing info
				urlEncodedParams.append('type', 'card');
				urlEncodedParams.append('billing_details[name]', this.profile.billingAddress.name.replace(' ', '+'));
				urlEncodedParams.append('billing_details[address][line1]', this.profile.billingAddress.line1);
				urlEncodedParams.append('billing_details[address][line2]', this.profile.billingAddress.line2);
				urlEncodedParams.append('billing_details[address][state]', this.profile.billingAddress.state);
				urlEncodedParams.append('billing_details[address][postal_code]', this.profile.billingAddress.postCode);
				urlEncodedParams.append('billing_details[address][country]', 'US');
				// card details
				urlEncodedParams.append('card[number]', this.profile.paymentDetails.cardNumber);
				urlEncodedParams.append('card[cvc]', this.profile.paymentDetails.cardCvv);
				urlEncodedParams.append('card[exp_month]', this.profile.paymentDetails.cardExpMonth);
				urlEncodedParams.append('card[exp_year]', this.profile.paymentDetails.cardExpYear);
				//TODO: scrape the following in real-time via the stripe iframe
				// https://m.stripe.com/6 (maybe from here) (pass cookie in stripe session)
				urlEncodedParams.append('guid', stripeIds.body.guid);
				urlEncodedParams.append('muid', stripeIds.body.muid);
				urlEncodedParams.append('sid', stripeIds.body.sid);
				urlEncodedParams.append('pasted_fields', 'number');
				urlEncodedParams.append('payment_user_agent', this.obj.stripePaymentAgent);
				//TODO: experiment if this can be auto generated ( not sure how important it is)
				urlEncodedParams.append('time_on_page', '1091405');
				urlEncodedParams.append('referrer', 'https://secure.squarespace.com/');
				urlEncodedParams.append('key', this.obj.checkoutConfig.stripeLivePublicApiKey);

				const response = await got.post('https://api.stripe.com/v1/payment_methods', {
					responseType: 'json',
					searchParams: urlEncodedParams,
					headers: {
						accept: '*/*',
						'accept-encoding': 'gzip, br',
						'accept-language': 'en-US,en;q=0.9',
						connection: 'keep-alive',
						'user-agent': this.userAgent()
					},
					agent: this.proxyList === 'local' ? false : this.proxy
				});

				if (response.statusCode === 200) return response.body['id'];
			} catch (err) {
				if (err !== undefined) {
					const msg = 'Error generating payment token';
					err.response
						? this.log(`${msg} ${JSON.stringify(err.response.body) || err.response.statusCode}`, 'error')
						: this.log(`${msg} ${err.message}`, 'error');
				}
				this.proxy = await this.proxyGot();
			}
			await new Promise((r) => setTimeout(r, 1000));
			return await this.generatePaymentToken();
		}
	}

	async paypalPreAuthLogin() {
		this.obj.paypal_token = await this.generatePaypalToken();
		this.obj.paypal_session_id = this.paypalUniqueID();
		this.obj.paypal_button_session_id = this.paypalUniqueID();
		this.obj.paypal_sdk = 'eyJ1cmwiOiJodHRwczovL3d3dy5wYXlwYWxvYmplY3RzLmNvbS9hcGkvY2hlY2tvdXQubWluLmpzIn0';
		this.obj.paypal_login_url = await this.generatePaypalCheckoutURL();
		this.paypal_proxy = await this.proxyGot('paypal');
		const taskCaptchaData = {
			id: this.name,
			cookies: this.cookieJar.toJSON().cookies,
			data: {
				email: '',
				password: ''
			},
			url: this.obj.paypal_login_url,
			type: 'Paypal Login',
			userAgent: this.ua
		};

		// choose best proxies depending on whats available in the proxy rotator service
		if (this.paypal_proxy) taskCaptchaData['proxy'] = this.paypal_proxy.https.proxyOptions;
		else if (this.proxy) taskCaptchaData['proxy'] = this.proxy.https.proxyOptions;
		else taskCaptchaData['proxy'] = false;

		await app.service('captcha').create(taskCaptchaData);
		// await this.resolveCaptchaSolver();
		// await this.paypalAuthenticatedSession();

		// // swap back to 'checkout' proxies
		// this.proxy = await this.proxyGot();
	}

	// paypal
	async generatePaypalToken() {
		if (this.enabled) {
			this.log('Generating Paypal token');
			try {
				const { cartToken, xsrfToken } = this.obj.checkoutConfig;
				const selectedCountry = _find(
					this.countryRegionData,
					(o) => o.countryName === this.profile.shippingAddress.country
				);
				const selectedState = _find(selectedCountry.regions, (o) => o.name === this.profile.shippingAddress.state);
				const response = await got.post(
					this.obj.upgradedToSecure
						? `https://secure.squarespace.com/api/commerce/paypal/checkout/cart`
						: `${this.parsedLink.protocol}//${this.parsedLink.host}/api/commerce/paypal/checkout/cart`,
					{
						responseType: 'json',
						json: {
							checkoutType: 'SHOPPING_CART',
							requestShippingAddress: false,
							shippingAddress: {
								id: '',
								firstName: this.profile.shippingAddress.name.split(' ')[0],
								lastName: this.profile.shippingAddress.name.split(' ')[1],
								line1: this.profile.shippingAddress.line1,
								line2: this.profile.shippingAddress.line2,
								city: this.profile.shippingAddress.city,
								region: selectedState.shortCode,
								postalCode: this.profile.shippingAddress.postCode,
								country: selectedCountry.countryShortCode,
								phoneNumber: this.profile.shippingAddress.phone
							},
							overrideShippingAddress: true,
							giftCardProductId: null,
							giftCardVariantSku: null,
							donatePageId: null,
							donateAmountDue: null,
							donatePageTitle: null,
							buttonId: 'paypal-button',
							locale: 'en-US',
							cartToken: cartToken
						},
						headers: {
							accept: '*/*',
							'accept-encoding': 'gzip, br',
							'accept-language': 'en-US,en;q=0.9',
							connection: 'keep-alive',
							'content-type': 'application/json',
							'x-csrf-token': this.obj.crumb,
							'x-siteuser-xsrf-token': xsrfToken,
							'user-agent': this.userAgent()
						},
						cookieJar: this.cookieJar,
						agent: this.proxyList === 'local' ? false : this.proxy
					}
				);
				if (response.statusCode === 200) return response.body.token;
			} catch (error) {
				if (error !== undefined) {
					const msg = 'Error generating Paypal token';
					err.response
						? this.log(`${msg} ${JSON.stringify(error.response.body) || error.response.statusCode}`, 'error')
						: this.log(`${msg} ${error.message}`, 'error');
				}
				this.proxy = await this.proxyGot();
			}
			await new Promise((r) => setTimeout(r, 1000));
			return await this.generatePaypalToken();
		}
	}

	async generatePaypalCheckoutURL() {
		try {
			if (this.enabled) {
				const url = `https://www.paypal.com/checkoutnow`;
				const qsParam = {
					'locale.x': 'en_US',
					fundingSource: 'paypal',
					sessionID: this.obj.paypal_session_id,
					buttonSessionID: this.obj.paypal_button_session_id,
					env: 'production',
					fundingOffered: 'paypal',
					logLevel: 'warn',
					sdkMeta: this.obj.paypal_sdk,
					uid: 5451091973,
					version: 'min',
					token: this.obj.paypal_token,
					xcomponent: 1
				};

				return `${url}?${qs.stringify(qsParam)}`;
			}
		} catch (error) {
			if (error !== undefined) {
				const msg = 'Error generating Paypal login url';
				error.response
					? this.log(`${msg} ${JSON.stringify(error.response.body) || error.response.statusCode}`, 'error')
					: this.log(`${msg} ${error.message}`, 'error');
			}
			this.proxy = await this.proxyGot();
		}
		await new Promise((r) => setTimeout(r, 1000));
		return await this.getPaypalLogin();
	}

	async paypalAuthenticatedSession() {
		try {
			if (this.enabled) {
				const url = this.obj.paypal_return_flow_url;
				let cookies;
				// extract all cookeis beloning to paypal
				this.cookieJar.store.findCookies('www.paypal.com', '/', function (err, cb) {
					cookies = cb;
				});
				// call hermes?flow=P-1, expected status should be 200
				const response = await got.get(`${url}&country.x=US`, {
					responseType: 'text',
					headers: {
						'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:96.0) Gecko/******** Firefox/96.0',
						accept: 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
						'accept-language': 'en-US,en;q=0.5',
						'accept-encoding': 'gzip, deflate, br',
						origin: 'https://www.paypal.com',
						cookie: cookies.map((i) => i.cookieString()).join('; '),
						referer: this.obj.paypal_login_url,
						TE: 'trailers'
					},
					// cookieJar: this.cookieJar,
					followRedirect: true,
					agent:
						this.proxyList === 'local'
							? false
							: this.obj.paypal_return_flow_proxy
								? {
									https: tunnel.httpsOverHttp({
										proxy: {
											...this.obj.paypal_return_flow_proxy
										}
									})
								}
								: this.proxy
				});
				const root = parse(response.body, {
					comment: false,
					blockTextElements: { script: true }
				});
				let initialDataScript = root.querySelector('script[id=initial-data-script]');
				let re = /[\r\n\s\.\'\_\(\w\,\;\/]+window.__INITIAL_DATA__\s=\s([,:{}\[\]0-9.\-\n\r\t\w\d\W\D]+)console.log/;
				let match = initialDataScript.innerHTML.match(re);
				let parsedInitialDataScript = JSON.parse(match[1]);
				this.obj.paypal_initial_data_script = parsedInitialDataScript;
				this.obj.paypal_euat = parsedInitialDataScript.user.auth.access_token;

				let paymentInfoScript = response.body.substring(
					response.body.indexOf('window.renderApp({"data":') + 'window.renderApp({"data":'.length,
					response.body.toString().search(/}\)[\r\n\s\;]+window.logCPLPhase\(\'RenderApp\'\,\s\'comp\'\)\;/)
				);

				const paymentInfoParsed = JSON.parse(paymentInfoScript);
				const { checkoutSession } = paymentInfoParsed;
				this.obj.paypal_address_id = checkoutSession.shippingAddresses[0].addressId;
				this.obj.paypal_payment_id = checkoutSession.fundingOptions[0].fundingInstrument.id;
				return;
			}
		} catch (error) {
			if (error !== undefined) {
				const msg = 'Error generating Paypal login url';
				error.response
					? this.log(`${msg} ${JSON.stringify(error.response.body) || error.response.statusCode}`, 'error')
					: this.log(`${msg} ${error.message}`, 'error');
			}
			this.proxy = await this.proxyGot();
		}
		await new Promise((r) => setTimeout(r, 1000));
		return await this.paypalAuthenticatedSession();
	}

	async paypalApproveMemberPayment() {
		try {
			if (this.enabled) {
				const url = 'https://www.paypal.com/graphql/';
				let cookies = undefined;
				this.cookieJar.store.findCookies('www.paypal.com', '/', function (err, cb) {
					cookies = cb;
				});
				const response = await got.post(url, {
					responseType: 'json',
					body: JSON.stringify({
						operationName: 'approveMemberPayment',
						variables: {
							experiences: ['IFRAME'],
							token: this.obj.paypal_token,
							selectedAddressId: this.obj.paypal_address_id,
							preferredShippingAddressId: '',
							preferredFundingOptionId: this.obj.paypal_payment_id,
							selectedFundingOptionId: this.obj.paypal_payment_id,
							secondaryFundingOptionIds: [],
							preAuthorizationRequired: false,
							fundingMethodType: 'INSTANT',
							scaCompleted: false,
							paycodeEncrypted: '',
							setStickyFiRequired: true,
							buttonText: 'Continue',
							exitLoaderMessagingApplied: true,
							exitLoaderExpMessages: {
								subText: 'Now finish paying'
							}
						},
						query:
							'mutation approveMemberPayment($token: String!, $selectedAddressId: String, $preferredShippingAddressId: String, $selectedFundingOptionId: String!, $preferredFundingOptionId: String, $secondaryFundingOptionIds: [String!], $selectedInstallmentOption: InstallmentsInput, $rewards: RewardsInput, $preAuthorizationRequired: Boolean, $fundingMethodType: FundingInstrumentMethodType, $shareAddressWithDonatee: Boolean, $setStickyFiRequired: Boolean, $scaCompleted: Boolean!, $experiences: [ThreeDSPaymentExperience], $cryptocurrencyQuoteId: String, $paycodeEncrypted: String) {\n  approveMemberPayment(\n    token: $token\n    selectedAddressId: $selectedAddressId\n    preferredShippingAddressId: $preferredShippingAddressId\n    primaryFundingOptionId: $selectedFundingOptionId\n    preferredFundingOptionId: $preferredFundingOptionId\n    secondaryFundingOptionIds: $secondaryFundingOptionIds\n    selectedInstallmentOption: $selectedInstallmentOption\n    rewards: $rewards\n    preAuthorizationRequired: $preAuthorizationRequired\n    fundingMethodType: $fundingMethodType\n    shareAddressWithDonatee: $shareAddressWithDonatee\n    setStickyFiRequired: $setStickyFiRequired\n    strongCustomerAuthenticationCompleted: $scaCompleted\n    cryptocurrencyQuoteId: $cryptocurrencyQuoteId\n    paycodeEncrypted: $paycodeEncrypted\n  ) {\n    state\n    buyer {\n      userId\n      email {\n        stringValue\n        __typename\n      }\n      name {\n        fullName\n        __typename\n      }\n      __typename\n    }\n    cart {\n      intent\n      paymentId\n      billingToken\n      returnUrl {\n        href\n        pathname\n        __typename\n      }\n      cancelUrl {\n        href\n        pathname\n        __typename\n      }\n      intent\n      items {\n        quantity\n        name\n        sku\n        unitPrice {\n          currencyValue\n          __typename\n        }\n        itemOptionSelections {\n          name\n          description\n          __typename\n        }\n        __typename\n      }\n      amounts {\n        subtotal {\n          currencyCode\n          currencyFormat\n          __typename\n        }\n        tax {\n          currencyCode\n          currencyFormat\n          __typename\n        }\n        shipping {\n          currencyCode\n          currencyFormat\n          __typename\n        }\n        insurance {\n          currencyCode\n          currencyFormat\n          __typename\n        }\n        handlingFee {\n          currencyCode\n          currencyFormat\n          __typename\n        }\n        total {\n          currencyCode\n          currencyFormat\n          __typename\n        }\n        discount {\n          currencyCode\n          currencyFormat\n          __typename\n        }\n        __typename\n      }\n      __typename\n    }\n    paymentContingencies {\n      ...PaymentContingenciesFragment\n      threeDomainSecure(experiences: $experiences) {\n        ...ThreeDomainSecureFields\n        __typename\n      }\n      paycodeRequired {\n        causeName\n        attempts\n        name\n        __typename\n      }\n      __typename\n    }\n    completedPaymentInfo {\n      transactionId\n      transactionState\n      softDescriptor\n      postbackData\n      __typename\n    }\n    merchant {\n      preferences {\n        autoReturnToMerchant\n        enablePaymentDataTransfer\n        returnUrl\n        __typename\n      }\n      __typename\n    }\n    __typename\n  }\n}\n\nfragment PaymentContingenciesFragment on PaymentContingencies {\n  confirmCreditCardCvvData {\n    ...ConfirmCreditCardCvvFields\n    __typename\n  }\n  sepaMandateAcceptanceNeeded {\n    ...SepaMandateAcceptanceNeededFragment\n    __typename\n  }\n  threeDomainSecure(experiences: $experiences) {\n    ...ThreeDomainSecureFields\n    __typename\n  }\n  threeDSContingencyData {\n    ...ThreeDomainSecureContingencyFragment\n    __typename\n  }\n  needConsentForBankAccountInfoRetrieval {\n    encryptedId\n    __typename\n  }\n  needRealTimeBalanceForBankAccount {\n    encryptedId\n    __typename\n  }\n  strongCustomerAuthenticationRequired {\n    contextId\n    __typename\n  }\n  __typename\n}\n\nfragment ConfirmCreditCardCvvFields on ConfirmCreditCardCvvContingency {\n  encryptedId\n  lastFourDigits\n  type\n  __typename\n}\n\nfragment SepaMandateAcceptanceNeededFragment on BankAccount {\n  id\n  type\n  accountNumber\n  accountNumberShort\n  bankIdentifierCode\n  currencyCode\n  beneficiaries {\n    fullName\n    __typename\n  }\n  billingAddress {\n    city\n    country\n    state\n    fullAddress\n    __typename\n  }\n  issuer {\n    name\n    __typename\n  }\n  creditor {\n    id\n    name\n    address {\n      fullAddress\n      __typename\n    }\n    __typename\n  }\n  __typename\n}\n\nfragment ThreeDomainSecureFields on ThreeDomainSecureContingency {\n  redirectUrl {\n    href\n    __typename\n  }\n  status\n  method\n  parameter\n  encryptedId\n  experience\n  requestParams {\n    key\n    value\n    __typename\n  }\n  __typename\n}\n\nfragment ThreeDomainSecureContingencyFragment on ThreeDomainSecureResolutionContingency {\n  name\n  causeName\n  resolution {\n    type\n    resolutionName\n    paymentCard {\n      id\n      type\n      number\n      encryptedNumber\n      bankIdentificationNumber\n      billingAddress {\n        line1\n        line2\n        state\n        city\n        postalCode\n        country\n        __typename\n      }\n      expireYear\n      expireMonth\n      currencyCode\n      cardProductClass\n      __typename\n    }\n    contingencyContext {\n      source\n      reason\n      referenceId\n      deviceDataCollectionUrl {\n        href\n        __typename\n      }\n      jwtSpecification {\n        jwtIssuer\n        jwtOrgUnitId\n        jwtDuration\n        __typename\n      }\n      __typename\n    }\n    __typename\n  }\n  __typename\n}\n'
					}),
					headers: {
						'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:96.0) Gecko/******** Firefox/96.0',
						Accept: '*/*',
						'Accept-Language': 'en-US,en;q=0.5',
						'content-type': 'application/json',
						'paypal-client-context': this.obj.paypal_token,
						'paypal-client-metadata-id': this.obj.paypal_token,
						Cookie: cookies.map((i) => i.cookieString()).join('; '),
						'x-paypal-internal-euat': this.obj.paypal_euat,
						'x-app-name': 'checkoutuinodeweb_hermione',
						'accept-encoding': 'gzip, deflate, br',
						origin: 'https://www.paypal.com',
						DNT: '1',
						Connection: 'keep-alive',
						'Sec-Fetch-Dest': 'empty',
						'Sec-Fetch-Mode': 'cors',
						'Sec-Fetch-Site': 'same-origin',
						Pragma: 'no-cache',
						'Cache-Control': 'no-cache',
						TE: 'trailers',
						referer: this.obj.paypal_return_flow_url
					},
					// cookieJar: this.cookieJar,
					agent:
						this.proxyList === 'local'
							? false
							: this.obj.paypal_return_flow_proxy
								? {
									https: tunnel.httpsOverHttp({
										proxy: {
											...this.obj.paypal_return_flow_proxy
										}
									})
								}
								: this.proxy
				});
				this.obj.paypal_buyer_user_id = response.body.data.approveMemberPayment.buyer.userId;
				return;
			}
		} catch (error) {
			if (error !== undefined) {
				const msg = 'Error posting to Paypal gql';
				error.response
					? this.log(`${msg} ${JSON.stringify(error.response.body) || error.response.statusCode}`, 'error')
					: this.log(`${msg} ${error.message}`, 'error');
			}
			this.proxy = await this.proxyGot();
		}
		await new Promise((r) => setTimeout(r, 1000));
		return await this.paypalApproveMemberPayment();
	}

	async paypalSubmitOrder() {
		try {
			if (this.enabled) {
				const { xsrfToken } = this.obj.checkoutConfig;
				const response = await got.put(
					this.obj.upgradedToSecure
						? `https://secure.squarespace.com/api/commerce/paypal/checkout?token=${this.obj.paypal_token}&payerId=${this.obj.paypal_buyer_user_id}`
						: `${this.parsedLink.protocol}//${this.parsedLink.host}/api/2/commerce/paypal/checkout?token=${this.obj.paypal_token}&payerId=${this.obj.paypal_buyer_user_id}`,
					{
						responseType: 'json',
						headers: {
							accept: 'application/json',
							'accept-encoding': 'gzip, br',
							'accept-language': 'en-US,en;q=0.9',
							connection: 'keep-alive',
							'x-csrf-token': this.obj.crumb,
							'x-siteuser-xsrf-token': xsrfToken,
							'user-agent': this.userAgent()
						},
						cookieJar: this.cookieJar,
						agent: this.proxyList === 'local' ? false : this.proxy
					}
				);
				return;
			}
		} catch (err) {
			if (err !== undefined) {
				const msg = `Error submitting checkout`;
				err.response
					? this.log(`${msg} ${JSON.stringify(err.response.body) || err.response.statusCode}`, 'error')
					: this.log(`${msg} ${err.message}`, 'error');
			}
			this.proxy = await this.proxyGot();
			if (err.response !== undefined && err.response.hasOwnProperty('body') && err.response.body !== undefined)
				return err.response.body;
		}
		await new Promise((r) => setTimeout(r, 1000));
		return await this.paypalSubmitOrder();
	}

	async charge(config) {
		if (this.obj.orderCheckIndex !== 0 && this.obj.stripe && this.enabled) {
			this.obj.payment_token = await this.generatePaymentToken();
		}

		if (!this.obj.stripe && this.obj.paypal && this.enabled) {
			await this.paypalApproveMemberPayment();
			await this.paypalSubmitOrder(); // maybe not needed idk
		}
		this.obj.order = await this.submitCheckout();
		this.obj.status = this.checkOrder();

		if (this.obj.order) {
			switch (this.obj.status) {
				case 'PAYMENT_DECLINED':
					// case 'CART_TOTAL_OUT_OF_SYNC':
					if (this.obj.orderCheckIndex === 0) {
						await this.feed();
						this.log(`Card decline, check monitor`, 'warn');
					} else this.log(`Card declined`, 'error');
					if (this.obj.orderCheckIndex >= 5) {
						// set monitor property so its not randomOos
						this.randomOos = false;
						this.obj.item = await this.monitor();
						// reset orderCheckIndex
						this.obj.orderCheckIndex = 0;
					} else {
						await new Promise((r) => setTimeout(r, 1000));
					}

					this.obj.orderCheckIndex = this.obj.orderCheckIndex + 1;
					await new Promise((r) => setTimeout(r, 1000));
					return await this.charge(config);
					break;
				case 'CART_ITEM_OUT_OF_STOCK':
				case 'CART_EMPTY':
					if (this.obj.orderCheckIndex === 0) this.log('Item went OOS while in checkout, switching to restock mode');
					if (this.obj.orderCheckIndex >= 5) {
						// set monitor property so its not randomOos
						this.randomOos = false;
						this.obj.item = await this.monitor();
						// reset orderCheckIndex
						this.obj.orderCheckIndex = 0;
					} else {
						await new Promise((r) => setTimeout(r, 1000));
					}

					this.obj.orderCheckIndex = this.obj.orderCheckIndex + 1;
					return await this.charge();
					// this.obj.cart = false;
					break;
				case 'INVALID_PAYMENT_AMOUNT':
					// not sure why this happens, it happens on penny items?
					this.log('Unknown payment amount error.', 'error');
					this.obj.cart = false;
					this.enabled = false;
					break;
				case 'ORDER_CONFIRMED':
					this.log('Check email', 'success');
					this.obj.success = true;
					this.obj.cart = false;
					await this.staffLog(
						this.link,
						this.obj.item.title,
						this.obj.order.grandTotal.decimalValue,
						this.obj.item.image
					);
					await this.userWebhook(
						this.link,
						this.obj.item.title,
						this.obj.order.grandTotal.decimalValue,
						this.obj.item.image
					);
					await this.feed();
					await this.successAnalytics(this.link, this.platform, this.obj.order.orderId);

					break;
				default:
					this.log('Unknown error has occured.', 'error');
					this.obj.cart = false;
					this.enabled = false;
			}
		}
	}

	checkOrder() {
		if (this.obj.order !== undefined) {
			if (this.obj.order.hasOwnProperty('submissionStatus')) {
				this.obj.status = this.obj.order.submissionStatus;
				return this.obj.status;
			} else if (this.obj.order.hasOwnProperty('failureType')) {
				this.obj.status = this.obj.order.failureType;
				return this.obj.status;
			}
		} else {
			this.obj.status = 'UNKNOWN_ERROR';
			this.enabled = false;
			return this.obj.status;
		}
	}

	async submitCheckout() {
		if (this.enabled) {
			this.log('Submitting checkout');
			try {
				const { xsrfToken } = this.obj.checkoutConfig;
				const response = await got.post(
					this.obj.upgradedToSecure
						? `https://secure.squarespace.com/api/2/commerce/orders`
						: `${this.parsedLink.protocol}//${this.parsedLink.host}/api/2/commerce/orders`,
					{
						responseType: 'json',
						headers: {
							accept: '*/*',
							'accept-encoding': 'gzip, br',
							'accept-language': 'en-US,en;q=0.9',
							connection: 'keep-alive',
							'content-type': 'application/json',
							'x-csrf-token': this.obj.crumb,
							'x-siteuser-xsrf-token': xsrfToken,
							'user-agent': this.userAgent()
						},
						json: {
							email: this.profile.shippingAddress.email,
							subscribeToList: true,
							shippingAddress: {
								id: '',
								firstName: this.profile.shippingAddress.name.split(' ')[0] || '',
								lastName:
									this.profile.shippingAddress.name.split(' ').length > 1
										? this.profile.shippingAddress.name.split(' ')[1]
										: '',
								line1: this.obj.orderConfig.shippingLocation.line1,
								line2: this.obj.orderConfig.shippingLocation.line2,
								city: this.obj.orderConfig.shippingLocation.city,
								region: this.obj.orderConfig.shippingLocation.region,
								postalCode: this.obj.orderConfig.shippingLocation.postalCode,
								country: this.obj.orderConfig.shippingLocation.country,
								phoneNumber: this.profile.shippingAddress.phone
							},
							createNewUser: false,
							newUserPassword: null,
							saveShippingAddress: true,
							makeDefaultShippingAddress: false,
							customFormData: JSON.stringify(this.obj.checkoutConfig.customFormData),
							shippingAddressId: null,
							proposedAmountDue: {
								decimalValue: this.obj.orderConfig.amountDue.decimalValue,
								currencyCode: this.obj.orderConfig.amountDue.currencyCode
							},
							cartToken: this.obj.checkoutConfig.cartToken,
							paymentToken:
								this.obj.stripe && this.obj.paypal
									? {
										stripePaymentTokenType: 'PAYMENT_METHOD_ID',
										token: this.obj.payment_token,
										type: 'STRIPE'
									}
									: {
										type: 'PAYPAL',
										token: this.obj.paypal_token,
										payerId: this.obj.paypal_buyer_user_id
									},
							billToShippingAddress: this.obj.stripe
								? this.profile.shippingAddress.line1 + this.profile.shippingAddress.line2 ===
								this.profile.billingAddress.line1 + this.profile.billingAddress.line2
								: false,
							billingAddress: {
								id: '',
								firstName: this.profile.billingAddress.name.split(' ')[0] || '',
								lastName:
									this.profile.billingAddress.name.split(' ').length > 0
										? this.profile.billingAddress.name.split(' ')[1]
										: '',
								line1: this.obj.orderConfig.shippingLocation.line1,
								line2: this.obj.orderConfig.shippingLocation.line2,
								city: this.obj.orderConfig.shippingLocation.city,
								region: this.obj.orderConfig.shippingLocation.region,
								postalCode: this.obj.orderConfig.shippingLocation.postalCode,
								country: this.obj.orderConfig.shippingLocation.country,
								phoneNumber: this.profile.billingAddress.phone
							},
							savePaymentInfo: false,
							makeDefaultPayment: false,
							paymentCardId: null
						},
						cookieJar: this.cookieJar,
						agent: this.proxyList === 'local' ? false : this.proxy
					}
				);
				return response.body;
			} catch (err) {
				if (err !== undefined) {
					const msg = `Error submitting checkout`;
					err.response
						? this.log(`${msg} ${JSON.stringify(err.response.body) || err.response.statusCode}`, 'error')
						: this.log(`${msg} ${err.message}`, 'error');
				}
				this.proxy = await this.proxyGot();
				if (err.response !== undefined && err.response.hasOwnProperty('body') && err.response.body !== undefined)
					return err.response.body;
			}
			await new Promise((r) => setTimeout(r, 1000));
			return await this.submitCheckout();
		}
	}

	async feed() {
		try {
			this.log(
				this.obj.upgradedToSecure
					? `https://secure.squarespace.com/checkout?cartToken=${this.obj.checkoutConfig.cartToken}&websiteId=${this.obj.storeInfo.id}`
					: `${this.parsedLink.protocol}//${this.parsedLink.host}/checkout?cartToken=${this.obj.checkoutConfig.cartToken}`,
				'debug'
			);
			await got.post('http://localhost:3030/monitor', {
				responseType: 'text',
				headers: {
					accept: '*/*',
					'accept-encoding': 'gzip, br',
					'accept-language': 'en-US,en;q=0.9',
					connection: 'keep-alive',
					'content-type': 'application/json'
				},
				json: {
					platform: `${this.platform} - ${this.name} - ${this.profile.shippingAddress.email}`,
					site: `${this.platform}`,
					type: 'Checkout',
					title:
						this.obj.orderConfig.items[0].variantOptions.length > 0
							? `${this.obj.orderConfig.items[0].variantOptions[0].value} - ${this.obj.orderConfig.items[0].productName}`
							: this.obj.orderConfig.items[0].productName,
					link: this.obj.success
						? `${this.parsedLink.origin}/checkout/order-confirmed?oid=${this.obj.order.orderId}&authCode=${this.obj.order.authCode}`
						: this.obj.upgradedToSecure
							? `https://secure.squarespace.com/checkout?cartToken=${this.obj.checkoutConfig.cartToken}&websiteId=${this.obj.storeInfo.id}`
							: `${this.parsedLink.protocol}//${this.parsedLink.host}/checkout?cartToken=${this.obj.checkoutConfig.cartToken}`,
					price: parseFloat(this.obj.orderConfig.amountDue.decimalValue).toFixed(2),
					image:
						this.obj.orderConfig.items[0].image.urls.https !== null
							? this.obj.orderConfig.items[0].image.urls.https
							: 'https://cdn.discordapp.com/attachments/714346599735754762/843321954688565278/blue.png',
					variants: [
						this.obj.upgradedToSecure
							? `https://secure.squarespace.com/checkout?cartToken=${this.obj.checkoutConfig.cartToken}&websiteId=${this.obj.storeInfo.id}`
							: `${this.parsedLink.protocol}//${this.parsedLink.host}/checkout?cartToken=${this.obj.checkoutConfig.cartToken}`
					]
				}
			});
		} catch (err) {
			this.log('Extension seems to be down.', 'error');
		}
	}

	// // helper method(s)
	parseCrumb(response) {
		let crumb;
		response.request.options.cookieJar.store.findCookie(this.parsedLink.host, '/', 'crumb', (err, cookie) => {
			crumb = cookie.value;
		});
		return crumb;
	}

	parseSecureCrumb(response) {
		let crumb;
		response.request.options.cookieJar.store.findCookie('secure.squarespace.com', '/', 'crumb', (err, cookie) => {
			crumb = cookie.value;
		});
		return crumb;
	}

	parseCartToken(url) {
		let parsed = new URL(url);
		return parsed.searchParams.get('cartToken');
	}

	parseCheckoutConfig(responseData) {
		const root = parse(responseData, {
			comment: false,
			blockTextElements: { script: true }
		});
		let bootstrapScript = root.querySelector('script[id=bootstrap]');
		let bootstrapJson = bootstrapScript.innerText;
		let bootstrapObj = JSON.parse(bootstrapJson);
		this.obj.store_id = bootstrapObj.checkoutBaseContext.websiteId;
		this.obj.stripe =
			bootstrapObj.checkoutBaseContext.stripeLivePublicApiKey !== undefined &&
			bootstrapObj.checkoutBaseContext.stripeLivePublicApiKey.length > 0;
		this.obj.paypal = bootstrapObj.checkoutConfig.paymentConfig.paypalEnabled;
		return {
			paypalMerchantId: bootstrapObj.checkoutConfig.paymentConfig.paypalMerchantId,
			xsrfToken: bootstrapObj.checkoutConfig.xsrfToken,
			stripeLivePublicApiKey: bootstrapObj.checkoutBaseContext.stripeLivePublicApiKey,
			stripeTestPublicApiKey: bootstrapObj.checkoutBaseContext.stripeTestPublicApiKey,
			stripeUserId: bootstrapObj.checkoutBaseContext.stripeUserId,
			supportCountries: bootstrapObj.checkoutConfig.shippingAddressFormConfig.supportedCountries,
			supportedRegions: bootstrapObj.checkoutConfig.shippingAddressFormConfig.supportedRegions,
			customCheckoutForm: bootstrapObj.customCheckoutForm,
			items: bootstrapObj.shoppingCart.items
		};
	}

	retrieveSmallingShippingRate() {
		return this.obj.orderConfig.fulfillmentOptions.reduce((a, b) => (a.price.value < b.price.value ? a : b));
	}

	paypalUniqueID() {
		var chars = '0123456789abcdef';
		return (
			'uid_' +
			'xxxxxxxxxx'.replace(/./g, function () {
				return chars.charAt(Math.floor(Math.random() * chars.length));
			}) +
			'_' +
			this.base64encode(new Date().toISOString().slice(11, 19).replace('T', '.'))
				.replace(/[^a-zA-Z0-9]/g, '')
				.toLowerCase()
		);
	}

	base64encode(str) {
		if ('function' == typeof btoa)
			return btoa(
				encodeURIComponent(str).replace(/%([0-9A-F]{2})/g, function (m, p1) {
					return String.fromCharCode(parseInt(p1, 16));
				})
			).replace(/[=]/g, '');
		if ('undefined' != typeof Buffer) return Buffer.from(str, 'utf8').toString('base64').replace(/[=]/g, '');
		throw new Error('Can not find window.btoa or Buffer');
	}

	// Awaits for captcha solve data from server/solver
	async resolveCaptchaSolver() {
		let that = this;
		this.log('000 - Paypal login required', 'prompt');
		this.port.on('message', async (message) => {
			// that.captchaEventEmitter.emit('answered', message.data);
			if (message.data.id === this.name && message.data.status === 'Solved') {
				this.obj.paypal_return_flow_url = message.data.url;
				this.obj.paypal_return_flow_proxy = message.data.proxy;
				await message.data.cookies.forEach(async (cookie) => {
					// console.log(cookie)
					try {
						if (cookie.name) {
							await this.cookieJar.setCookie(
								`${cookie.name}=${cookie.value}`,
								`https://${cookie.domain}`,
								async (err, cookie) => {
									if (err) {
										console.log(err);
										console.log(cookie);
									}
								}
							);
						}
					} catch (error) {
						this.log(`000 - Error setting cookie: ${error.message}`, 'error');
					}
				});
				this.obj.checkpoint = message.data.data;
				await that.captchaEventEmitter.emit('answered', message.data);
			}
		});
		return new Promise(function (resolve, reject) {
			that.log(`000 - Solver connected, awaiting Paypal login.`);
			that.captchaEventEmitter.on('answered', async (response) => {
				// this.obj.checkpoint = response.data
				that.log(`000 - Received Paypal login session for ${response.type.toLowerCase()}`);
				resolve();
			});
		});
	}

	// question solver
	async promptQuestionSolver() {
		const data = {
			taskGroupName: this.name.split('-')[0],
			questions: this.obj.promptQuestions,
			answered: false
		};
		this.port.postMessage({ action: 'QUESTION_REQUIRED', data: data });
		this.questionSolverEventEmitter.emit('create', data);
	}

	async resolveQuestionSolver() {
		let that = this;
		this.port.on('message', async (message) => {
			if (message.action === 'questionAnswered') {
				that.questionSolverEventEmitter.emit('answered', message.data);
			}
		});
		return new Promise(function (resolve, reject) {
			that.log(`Question(s) required. Check extension!`, 'prompt');
			that.questionSolverEventEmitter.on('answered', async (response) => {
				const { questions } = response;
				Object.assign(that.obj.checkoutConfig.customCheckoutForm, { questions });
				that.port.postMessage({ action: 'QUESTION_ANSWERED' });
				resolve();
			});
		});
	}

	createCustomFormData() {
		try {
			this.log(`Question(s) answered.`);
			const { customCheckoutForm } = this.obj.checkoutConfig;
			const data = {};
			for (const idx in customCheckoutForm.items) {
				let item = customCheckoutForm.items[idx];
				if (item.id.indexOf('section-yui') === -1) {
					let question = customCheckoutForm.questions.find((f) => f.id === item.id);
					if (question !== undefined) {
						// Object.defineProperty(data, item.id, { value: question.answer });
						data[item.id] = question.answer;
					} else {
						// Object.defineProperty(data, item.id, { value: item.answer });
						let answer = this.obj.answeredQuestions.find((f) => Object.getOwnPropertyNames(f)[0] === item.id);
						if (answer) data[item.id] = answer[item.id];
					}
				}
			}
			return data;
		} catch (err) {
			if (err !== undefined) {
				const msg = 'Error Submitting Answer(s)';
				err.response
					? this.log(`${msg} ${JSON.stringify(err.response.body) || err.response.statusCode}`, 'error')
					: this.log(`${msg} ${err.message}`, 'error');
			}
		}
	}

	answerCheckbox(q) {
		let { id, options } = q;
		let values = options.map((o) => o[Object.keys(o)[0]]);
		for (let idx in values) {
			let value = values[idx];
			if (value.toLowerCase() === 'y' || value.toLowerCase() === 'yes') {
				Object.defineProperty(
					this.obj.checkoutConfig.customCheckoutForm.items.find((f) => f.id === id),
					'answer',
					{ value: [value] }
				);
			}
		}
	}

	answerSurvey(q) {
		let { id, questions } = q;
		let answer = {};
		for (let idx in questions) {
			let question = questions[idx];
			answer[question.text] = '0'.toString();
		}
		Object.defineProperty(
			this.obj.checkoutConfig.customCheckoutForm.items.find((f) => f.id === id),
			'answer',
			{ value: answer }
		);
	}

	answerText(q) {
		let { id, type } = q;
		let answer = '';
		switch (type) {
			case 'email':
				answer = this.profile.shippingAddress.email;
				break;
			case 'currency':
				answer = 'USD';
				break;
			case 'number':
				answer = this.quantity.toString();
				break;
			case 'password':
				answer = 'Password123!';
				break;
			default:
				break;
		}
		Object.defineProperty(
			this.obj.checkoutConfig.customCheckoutForm.items.find((f) => f.id === id),
			'answer',
			{ value: answer }
		);
	}

	answerDate(q) {
		let { id } = q;
		let dateStr = moment().format('MM:DD:yyyy');
		let dateArr = dateStr.split(':');
		let mm = dateArr[0];
		let dd = dateArr[1];
		let yyyy = dateArr[2];
		let answer = [mm.toString(), dd.toString(), yyyy.toString()];
		Object.defineProperty(
			this.obj.checkoutConfig.customCheckoutForm.items.find((f) => f.id === id),
			'answer',
			{ value: answer }
		);
	}

	answerTime(q) {
		let { id } = q;
		let timeStr = moment().format('hh:mm:ss:A');
		let timeArr = timeStr.split(':');
		let hh = timeArr[0];
		let mm = timeArr[1];
		let ss = timeArr[2];
		let a = timeArr[3];
		let answer = [hh.toString(), mm.toString(), ss.toString(), a.toString()];
		Object.defineProperty(
			this.obj.checkoutConfig.customCheckoutForm.items.find((f) => f.id === id),
			'answer',
			{ value: answer }
		);
	}

	answerAddress(q) {
		let { id } = q;
		let { line1, line2, city, state, postCode, country } = this.profile.shippingAddress;
		let answer = [line1 || '', line2 || '', city || '', state || '', postCode || '', country || ''];
		Object.defineProperty(
			this.obj.checkoutConfig.customCheckoutForm.items.find((f) => f.id === id),
			'answer',
			{ value: answer }
		);
	}

	answerName(q) {
		let { id } = q;
		let { name } = this.profile.shippingAddress;
		let nameArr = name.split(' ');
		let fName = nameArr[0];
		let lName = nameArr[1];
		let answer = [fName, lName];
		Object.defineProperty(
			this.obj.checkoutConfig.customCheckoutForm.items.find((f) => f.id === id),
			'answer',
			{ value: answer }
		);
	}

	answerPhone(q) {
		let { id } = q;
		let { phone } = this.profile.shippingAddress;
		if (phone !== undefined) {
			// xxx xxx xxxx
			// TODO: make logic smarter to detect international code
			let areaCode = phone.substring(0, 3);
			let firstThree = phone.substring(3, 6);
			let lastFour = phone.substring(6, 10);
			if (areaCode.length > 0 && firstThree.length > 0 && lastFour.length > 0) {
				let answer = ['', areaCode, firstThree, lastFour];
				Object.defineProperty(
					this.obj.checkoutConfig.customCheckoutForm.items.find((f) => f.id === id),
					'answer',
					{ value: answer }
				);
			}
		}
	}

	parseCustomForm() {
		const { customCheckoutForm } = this.obj.checkoutConfig;
		if (
			customCheckoutForm !== undefined &&
			customCheckoutForm.id !== undefined &&
			customCheckoutForm.items.length > 0
		) {
			this.obj.hasCustomForm = true;
			for (let key in customCheckoutForm.items) {
				let question = customCheckoutForm.items[key];
				let { type, required } = question;
				if (required) {
					if (type === 'address') {
						this.answerAddress(question);
					} else if (type === 'checkbox') {
						this.obj.isQuestionRequired = true;
						this.obj.promptQuestions.push(question);
					} else if (type === 'currency') {
						this.answerText(question);
					} else if (type === 'date') {
						this.answerDate(question);
					} else if (type === 'email') {
						this.answerText(question);
					} else if (type === 'likert') {
						this.answerSurvey(question);
					} else if (type === 'name') {
						this.answerName(question);
					} else if (type === 'number') {
						// this.obj.isQuestionRequired = true;
						// this.obj.promptQuestions.push(question);
						this.answerText(question);
					} else if (type === 'password') {
						this.answerText(question);
					} else if (type === 'phone') {
						this.answerPhone(question);
					} else if (type === 'radio') {
						this.obj.isQuestionRequired = true;
						this.obj.promptQuestions.push(question);
					} else if (type === 'currency') {
						this.answerText(question);
					} else if (type === 'select') {
						this.obj.isQuestionRequired = true;
						this.obj.promptQuestions.push(question);
					} else if (type === 'textarea') {
						this.obj.isQuestionRequired = true;
						this.obj.promptQuestions.push(question);
					} else if (type === 'text') {
						this.obj.isQuestionRequired = true;
						this.obj.promptQuestions.push(question);
					} else if (type === 'time') {
						this.answerTime(question);
					} else if (type === 'twitter') {
						this.obj.isQuestionRequired = true;
						this.obj.promptQuestions.push(question);
					} else if (type === 'website') {
						this.obj.isQuestionRequired = true;
						this.obj.promptQuestions.push(question);
					} else {
						this.obj.isQuestionRequired = true;
						this.obj.promptQuestions.push(question);
					}
				} else {
					if (type === 'address') {
						this.obj.answeredQuestions.push(
							Object.defineProperty({}, question.id, {
								value: ['', '', '', '', '', '']
							})
						);
					} else if (type === 'checkbox') {
						this.obj.answeredQuestions.push(Object.defineProperty({}, question.id, { value: [] }));
					} else if (type === 'date') {
						this.obj.answeredQuestions.push(Object.defineProperty({}, question.id, { value: ['', '', ''] }));
					} else if (type === 'likert') {
						// Survey
						this.obj.answeredQuestions.push(Object.defineProperty({}, question.id, { value: {} }));
					} else if (type === 'name') {
						this.obj.answeredQuestions.push(Object.defineProperty({}, question.id, { value: ['', ''] }));
					} else if (type === 'phone' || type === 'time') {
						this.obj.answeredQuestions.push(
							Object.defineProperty({}, question.id, {
								value: ['', '', '', '']
							})
						);
					} else if (
						type === 'password' ||
						type === 'radio' ||
						type === 'select' ||
						type === 'textarea' ||
						type === 'text' ||
						type === 'twitter' ||
						type === 'website' ||
						type === 'email' ||
						type === 'number' ||
						type === 'currency'
					) {
						this.obj.answeredQuestions.push(Object.defineProperty({}, question.id, { value: '' }));
					}
				}
			}
		}
	}

	// monitor
	async monitor() {
		this.log(`Monitoring ${this.link.split('/')[2]}`, 'monitor');
		// Lowering oInput
		this.oInput = this.oInput.toLowerCase();

		let monitored;
		// Is the product input (pInput) a direct link, option ID, or Keyword
		switch (this.inputType) {
			case 'DL':
				monitored = await this.dlMonitor(this.pInput);
				break;
			case 'PID':
				monitored = { link: this.link, OID: this.pInput };
				break;
			case 'KW':
				this.pInput = this.pInput.map((p) => p.toLowerCase());
				this.positives = this.pInput.filter((keyword) => keyword[0] === '+');
				this.negatives = this.pInput.filter((keyword) => keyword[0] === '-');
				monitored = await this.kwMonitor();
				break;
		}
		if (monitored) {
			return monitored;
		}
	}

	async dlMonitor(dl) {
		this.log(`Monitoring ${dl}`, 'monitor');
		let found = false;
		try {
			const response = await got.get(`${dl}?format=json-pretty&${Math.floor(Math.random() * 999999999)}`, {
				responseType: 'json',
				headers: {
					'User-Agent': this.userAgent(),
					Accept: 'text/javascript, text/html, application/xml, text/xml, */*',
					'Accept-Language': 'en-US,en;q=0.5',
					Referer: `${this.link}/`,
					'X-Requested-With': 'XMLHttpRequest',
					'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
					DNT: '1',
					Connection: 'keep-alive',
					'Sec-Fetch-Dest': 'empty',
					'Sec-Fetch-Mode': 'cors',
					'Sec-Fetch-Site': 'same-origin',
					TE: 'trailers'
				},
				agent: await this.proxyGot('monitor')
			});
			const { body } = response;
			let matchedItem = {};
			let monitorFound = undefined;
			if (body !== undefined && body.hasOwnProperty('item')) {
				const { item } = body;
				const productName = item.title;
				let imageUrl = item.assetUrl;
				let itemId = item.id;
				if (this.oInput.length > 0 || this.randomOos || item.variants.length === 1) {
					const { variants } = item;
					const opts = variants.map((m) => m.attributes);
					let matchedOption = [],
						existingOptions = [];
					if (variants.length > 1) {
						matchedOption = opts.filter((opt, i) => Object.values(opt)[0].toLowerCase().includes(this.oInput));
						existingOptions = opts.filter((opt, i) => Object.values(opt)[0].toLowerCase());
					}

					const availableOptions = variants.filter((variant, i) => variant.qtyInStock > 0 || variant.unlimited);
					if (availableOptions.length === 0) throw new Error('No available variants');
					if (matchedOption.length > 0 && availableOptions.some((f) => f.attributes === matchedOption[0])) {
						const matchedVariant = variants[existingOptions.indexOf(matchedOption[0])];
						// fix for singular variant images
						if (item.variants.length === 1 && item.variants[0].hasOwnProperty('mainImage')) {
							imageUrl = item.variant[0].mainImage.assetUrl;
						} else {
							if (item.variants[existingOptions.indexOf(matchedOption[0])].hasOwnProperty('mainImage'))
								imageUrl = item.variants[existingOptions.indexOf(matchedOption[0])].mainImage.assetUrl;
							else imageUrl = item.items[0].assetUrl;
						}
						matchedItem.title = `${productName.replace(/\n/gi, '').trim()} - ${Object.values(matchedOption[0])}`;
						matchedItem.sku = matchedVariant.sku;
						matchedItem.imageUrl = imageUrl;
						matchedItem.cost = matchedVariant.priceMoney.value;
					} else if (this.randomOos || (matchedOption.length === 0 && variants.length === 1)) {
						const randomIdx = Math.floor(Math.random() * availableOptions.length);
						const randomVariant = availableOptions[randomIdx];
						if (variants.length === 1) matchedItem.title = `${productName.replace(/\n/gi, '').trim()}`;
						else
							matchedItem.title = `${productName.replace(/\n/gi, '').trim()} - ${Object.values(availableOptions[randomIdx].attributes)[0]
								}`;
						// fix for singular variant images
						if (item.variants.length === 1 && item.variants[0].hasOwnProperty('assetUrl')) {
							imageUrl = item.items[0].assetUrl;
						} else {
							if (item.variants[randomIdx].hasOwnProperty('mainImage')) {
								imageUrl = item.variants[randomIdx].mainImage.assetUrl;
							}
						}
						matchedItem.sku = randomVariant.sku;
						matchedItem.imageUrl = imageUrl;
						matchedItem.cost = randomVariant.priceMoney.value;
					}
				}
				if (matchedItem.hasOwnProperty('title') && itemId.length > 0) {
					monitorFound = {
						title: matchedItem.title.replace(/\n/gi, '').trim(),
						OID: itemId,
						image: matchedItem.imageUrl,
						cost: parseFloat(matchedItem.cost).toFixed(2),
						sku: matchedItem.sku
					};
					found = true;
				}
				if (found) {
					this.log(`Found ${monitorFound.title}`, 'monitor');
					return monitorFound;
				}
			}
		} catch (err) {
			if (err !== undefined) this.log(err.response ? err.response.statusCode : err.message, 'error');
		}
		if (this.counter % 10 === 0) {
			this.log('Still monitoring');
		}
		this.counter++;
		await new Promise((r) => setTimeout(r, 1000));
		return await this.dlMonitor(dl);
	}

	async randomItemMonitor() {
		this.log(`Monitoring ${this.pInput}`, 'monitor');
		try {
			let items = [];
			// determine if parsedLink contains products grid
			if (this.parsedLink.pathname === undefined || this.parsedLink.pathname === '/') {
				// parse sitemap
				let productGridPages = await this.parseSiteMap(this.link);
				items = await Promise.all(productGridPages.map((link) => this.parseCollection(link, 2500))).then(_flatten);
			} else {
				items = await this.parseCollection(this.link, 2500);
			}
			if (items.length > 0) {
				let mappedItem = items.map((i) => {
					return {
						itemId: i.id,
						variants: i.variants.map((v) => {
							if (v.qtyInStock > 0 || v.unlimited) return { itemId: i.id, ...v };
						})
					};
				});
				const item = mappedItem[0];
				const variant = item.variants[0];
				return {
					OID: item.itemId,
					sku: variant.sku,
					qty: 1
				};
			} else return await this.randomItemMonitor();
		} catch (err) {
			if (err !== undefined) {
				const msg = 'KW monitor encountered an error';
				err.response
					? this.log(`${msg} ${JSON.stringify(err.response.body) || err.response.statusCode}`, 'error')
					: this.log(`${msg} ${err.message}`, 'error');
			}
		}
		this.counter++;
		await new Promise((r) => setTimeout(r, 1000));
		return await this.randomItemMonitor();
	}

	async kwMonitor() {
		this.log(`Monitoring ${this.pInput}`, 'monitor');
		try {
			let items = [];
			// determine if parsedLink contains products grid
			if (this.parsedLink.pathname === undefined || this.parsedLink.pathname === '/') {
				// parse sitemap
				let productGridPages = await this.parseSiteMap(this.link);
				items = await Promise.all(productGridPages.map((link) => this.parseCollection(link, 2500))).then(_flatten);
			} else {
				items = await this.parseCollection(this.link, 2500);
			}
			for (let ii = 0; ii < items.length; ii++) {
				const item = items[ii];
				let parsedItems = this.parseProduct(item);
				for (let vv = 0; vv < parsedItems.length; vv++) {
					let variant = parsedItems[vv];
					let isMatch = match(variant.title.toLowerCase(), this.pInput);
					if (isMatch) return await this.dlMonitor(variant.link);
				}
			}
			return await this.kwMonitor();
		} catch (err) {
			if (err !== undefined) {
				const msg = 'KW monitor encountered an error';
				err.response
					? this.log(`${msg} ${JSON.stringify(err.response.body) || err.response.statusCode}`, 'error')
					: this.log(`${msg} ${err.message}`, 'error');
			}
		}
		this.counter++;
		await new Promise((r) => setTimeout(r, 1000));
		return await this.kwMonitor();
	}

	parseProduct(item) {
		let parsedItems = [];
		let { title, variants, assetUrl, fullUrl } = item;
		if (variants !== undefined && variants.length > 0) {
			variants.forEach((variant) => {
				try {
					parsedItems.push({
						title,
						sku: variant.sku,
						image: variant.hasOwnProperty('images') && variant.images.length > 0 ? variant.images[0] : assetUrl,
						link: `${this.parsedLink.origin}${fullUrl}`,
						cost: variant.price,
						qtyInStock: variant.qtyInStock,
						isAvailable: variant.qtyInStock > 0,
						optValue: typeof variant.attributes === 'object' ? Object.values(variant.attributes)[0] : undefined
					});
				} catch (err) {
					if (err !== undefined) {
						const msg = 'Error parsing variant';
						this.log(`${msg} ${err.message}`, 'error');
					}
				}
			});
		}
		return parsedItems;
	}

	async parseSiteMap(link) {
		let productPages = [];
		// Getting sitemap.xml
		const siteMapPath = 'sitemap.xml';
		const response = await got.get(`${link}${siteMapPath}?${Math.floor(Math.random() * 999999999)}`, {
			responseType: 'text',
			headers: {
				Accept: '*/*',
				'Accept-Language': 'en-US,en;q=0.5',
				Connection: 'keep-alive',
				'User-Agent': this.userAgent()
			},
			agent: await this.proxyGot('monitor')
		});
		const responseData = response.body;
		try {
			const parsedXml = xmlParser.parse(responseData);
			let existingPath = [];
			if (parsedXml.hasOwnProperty('urlset') && parsedXml.urlset.hasOwnProperty('url'))
				for (let i in parsedXml.urlset.url) {
					if (parsedXml.urlset.url.hasOwnProperty(i)) {
						let urlNode = parsedXml.urlset.url[i];
						let p = new URL(urlNode.loc);
						let pathname = p.pathname.substr(1, p.pathname.length);
						if (pathname.indexOf('/') > -1) {
							pathname = pathname.split('/')[0];
						}

						if (urlNode.hasOwnProperty('image:image') && existingPath.indexOf(pathname) === -1) {
							existingPath.push(pathname);
							productPages.push(`${p.origin}/${pathname}`);
						}
					}
				}
		} catch (err) {
			if (err !== undefined) {
				const msg = 'Error parsing site';
				err.response
					? this.log(`${msg} ${JSON.stringify(err.response.body) || err.response.statusCode}`, 'error')
					: this.log(`${msg} ${err.message}`, 'error');
			}
			return await this.parseSiteMap(link);
		}
		return productPages;
	}

	async parseCollection(link) {
		try {
			const response = await got.get(`${link}?format=json-pretty&${Math.floor(Math.random() * 999999999)}`, {
				responseType: 'json',
				headers: {
					Accept: '*/*',
					'Accept-Language': 'en-US,en;q=0.5',
					Connection: 'keep-alive',
					'User-Agent': this.userAgent()
				},
				agent: await this.proxyGot('monitor')
			});
			if (response !== undefined) {
				const responseData = response.body;
				const { items } = responseData;
				if (items !== undefined) return items;
			}
			return [];
		} catch (err) {
			if (err !== undefined) {
				const msg = 'Error parsing site';
				err.response
					? this.log(`${msg} ${JSON.stringify(err.response.body) || err.response.statusCode}`, 'error')
					: this.log(`${msg} ${err.message}`, 'error');
			}
		}
		this.counter++;
		return await this.parseCollection(link);
	}
};
