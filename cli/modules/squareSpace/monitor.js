const axios = require('axios');
const kleur = require('kleur');
const moment = require('moment');
const randomUseragent = require('random-useragent');
const HttpsProxyAgent = require('@ihof/https-proxy-agent-timeout');
const { parse } = require('node-html-parser');
const xmlParser = require('fast-xml-parser');
const _flatten = require('lodash/flatten');
const { match } = require('../utils/keyword');
const randomint = require('../utils/randomint');

module.exports = class Monitor {
	constructor(group, name, link, inputType, pInput, oInput, randomOos, proxyList) {
		this.name = name;
		this.link = link;
		this.inputType = inputType;
		this.pInput = pInput;
		this.oInput = oInput;
		this.randomOos = randomOos;
		this.counter = 0;
		this.monCounter = 0;
		this.parsedLink = undefined;
		this.proxyList = proxyList;
		if (link !== undefined) this.parsedLink = new URL(link);
	}

	async flow(proxyManager) {
		this.proxyManager = proxyManager;
		await this.logger(`000 - Monitoring ${this.link.split('/')[2]}`);
		// Lowering oInput
		this.oInput = this.oInput.toLowerCase();

		let monitored;
		// Is the product input (pInput) a direct link, option ID, or Keyword
		switch (this.inputType) {
			case 'DL':
				monitored = await this.dlMonitor(this.pInput);
				break;
			case 'PID':
				monitored = { link: this.link, OID: this.pInput };
				break;
			case 'KW':
				this.pInput = this.pInput.map((p) => p.toLowerCase());
				this.positives = this.pInput.filter((keyword) => keyword[0] === '+');
				this.negatives = this.pInput.filter((keyword) => keyword[0] === '-');
				monitored = await this.kwMonitor();
				break;
		}
		if (monitored) {
			return monitored;
		}
	}

	parseProduct(item) {
		let parsedItems = [];
		let { title, variants, assetUrl, fullUrl } = item;
		if (variants !== undefined && variants.length > 0) {
			variants.forEach((variant) => {
				try {
					parsedItems.push({
						title,
						sku: variant.sku,
						image: variant.hasOwnProperty('images') && variant.images.length > 0 ? variant.images[0] : assetUrl,
						link: `${this.parsedLink.origin}${fullUrl}`,
						cost: variant.price,
						qtyInStock: variant.qtyInStock,
						isAvailable: variant.qtyInStock > 0,
						optValue: typeof variant.attributes === 'object' ? Object.values(variant.attributes)[0] : undefined
					});
				} catch (err) {
					if (err !== undefined) {
						this.logger(err.message, 'red');
					}
				}
			});
		}
		return parsedItems;
	}

	async getResponse(link, config) {
		try {
			const response = await axios.get(link, config);
			return response;
		} catch (err) {
			this.logger('Bad request', 'red');
		}
	}

	async returnGetResponseWithTimeout(link, config, timeout) {
		let that = this;
		return new Promise(function (resolve, reject) {
			that.getResponse(link, config).then(resolve, reject);
			setTimeout(reject, timeout);
		});
	}

	async dlMonitor(dl) {
		this.logger(`Monitoring ${dl}`);
		let found = false;
		try {
			let config = {
				headers: {
					'User-Agent': randomUseragent.getRandom()
				}
			};
			let proxy = this.proxyManager.rand();
			if (this.proxyList !== 'local') {
				config.httpsAgent = new HttpsProxyAgent({
					host: `${proxy.split(':')[0]}`,
					port: `${proxy.split(':')[1]}`,
					auth: `${proxy.split(':')[2]}:${proxy.split(':')[3]}`
				});
			}
			const response = await this.returnGetResponseWithTimeout(
				`${dl}?format=json-pretty&${Math.floor(Math.random() * 999999999)}`,
				config,
				2500
			);
			// const response = await axios.get(`${dl}?format=json-pretty&${Math.floor(Math.random() * 999999999)}`, config);
			const { data } = response;
			let matchedItem = {};
			let monitorFound = undefined;
			if (data !== undefined && data.hasOwnProperty('item')) {
				const { item } = data;
				const productName = item.title;
				let imageUrl = item.assetUrl;
				let itemId = item.id;
				if (this.oInput.length > 0 || this.randomOos || item.variants.length === 1) {
					const { variants } = item;
					const opts = variants.map((m) => m.attributes);
					let matchedOption = [],
						existingOptions = [];
					if (variants.length > 1) {
						matchedOption = opts.filter((opt, i) => Object.values(opt)[0].toLowerCase().includes(this.oInput));
						existingOptions = opts.filter((opt, i) => Object.values(opt)[0].toLowerCase());
					}

					const availableOptions = variants.filter((variant, i) => variant.qtyInStock > 0 || variant.unlimited);
					if (availableOptions.length === 0) throw new Error('No available variants');
					if (matchedOption.length > 0 && availableOptions.some((f) => f.attributes === matchedOption[0])) {
						const matchedVariant = variants[existingOptions.indexOf(matchedOption[0])];
						matchedItem.title = `${productName.replace(/\n/gi, '').trim()} - ${Object.values(matchedOption[0])}`;
						matchedItem.sku = matchedVariant.sku;
						matchedItem.imageUrl = matchedVariant.images.length ? matchedVariant.images[0] : imageUrl;
						matchedItem.cost = matchedVariant.priceMoney.value;
					} else if (this.randomOos || (matchedOption.length === 0 && variants.length === 1)) {
						const randomIdx = Math.floor(Math.random() * availableOptions.length);
						const randomVariant = availableOptions[randomIdx];
						if (variants.length === 1) matchedItem.title = `${productName.replace(/\n/gi, '').trim()}`;
						else
							matchedItem.title = `${productName.replace(/\n/gi, '').trim()} - ${
								Object.values(availableOptions[randomIdx].attributes)[0]
							}`;
						matchedItem.sku = randomVariant.sku;
						matchedItem.imageUrl = randomVariant.images.length ? randomVariant.images[0] : imageUrl;
						matchedItem.cost = randomVariant.priceMoney.value;
					}
				}
				if (matchedItem.hasOwnProperty('title') && itemId.length > 0) {
					monitorFound = {
						title: matchedItem.title.replace(/\n/gi, '').trim(),
						OID: itemId,
						image: matchedItem.imageUrl,
						cost: parseFloat(matchedItem.cost).toFixed(2),
						sku: matchedItem.sku
					};
					found = true;
				}
				if (found) {
					this.logger(`Found ${monitorFound.title}`);
					return monitorFound;
				}
			}
		} catch (err) {
			if (err !== undefined) this.logger(err.response ? err.response.status : err.message);
		}
		if (this.counter % 10 === 0) {
			this.logger('Still monitoring');
		}
		this.counter += 1;

		await new Promise((r) => setTimeout(r, 1000));
		const reDlMonitor = await this.dlMonitor(dl);
		return reDlMonitor;
	}

	async kwMonitor() {
		this.logger(`Monitoring ${this.pInput}`);
		try {
			let items = [];
			// determine if parsedLink contains products grid
			if (this.parsedLink.pathname === undefined || this.parsedLink.pathname === '/') {
				// parse sitemap
				let productGridPages = await this.parseSiteMap(this.link);
				items = await Promise.all(productGridPages.map((link) => this.parsedItemGridWithTimeout(link, 2500))).then(
					_flatten
				);
			} else {
				items = await this.parsedItemGridWithTimeout(this.link, 2500);
			}
			for (let ii = 0; ii < items.length; ii++) {
				const item = items[ii];
				let parsedItems = this.parseProduct(item);
				for (let vv = 0; vv < parsedItems.length; vv++) {
					let variant = parsedItems[vv];
					let isMatch = match(variant.title.toLowerCase(), this.pInput);
					if (isMatch) return await this.dlMonitor(variant.link);
				}
			}
			return await this.kwMonitor();
		} catch (err) {
			if (err !== undefined) {
				this.logger(err !== undefined && err.response ? err.response.status : err, 'red');
			}
			return await this.kwMonitor();
		}
	}

	async parseSiteMap(link) {
		let config = {
			headers: {
				'User-Agent': randomUseragent.getRandom()
			}
		};
		let proxy = this.proxyManager.rand();
		if (this.proxyList !== 'local') {
			config.httpsAgent = new HttpsProxyAgent({
				host: `${proxy.split(':')[0]}`,
				port: `${proxy.split(':')[1]}`,
				auth: `${proxy.split(':')[2]}:${proxy.split(':')[3]}`
			});
		}
		let productPages = [];
		// Getting sitemap.xml
		const siteMapPath = 'sitemap.xml';
		const response = await axios.get(`${link}${siteMapPath}?${Math.floor(Math.random() * 999999999)}`, config);
		const responseData = response.data;
		try {
			const parsedXml = xmlParser.parse(responseData);
			let existingPath = [];
			if (parsedXml.hasOwnProperty('urlset') && parsedXml.urlset.hasOwnProperty('url'))
				for (let i in parsedXml.urlset.url) {
					if (parsedXml.urlset.url.hasOwnProperty(i)) {
						let urlNode = parsedXml.urlset.url[i];
						let p = new URL(urlNode.loc);
						let pathname = p.pathname.substr(1, p.pathname.length);
						if (pathname.indexOf('/') > -1) {
							pathname = pathname.split('/')[0];
						}

						if (urlNode.hasOwnProperty('image:image') && existingPath.indexOf(pathname) === -1) {
							existingPath.push(pathname);
							productPages.push(`${p.origin}/${pathname}`);
						}
					}
				}
		} catch (err) {
			if (err !== undefined) {
				if (err.response) this.logger(err.response.message, 'red');
				else this.logger(err.message, 'red');
			}

			this.counter++;
			const retry = await this.parseSiteMap(link);
			return retry;
		}

		return productPages;
	}

	async parsedItemGridWithTimeout(link, timeout) {
		let that = this;
		return new Promise(function (resolve, reject) {
			that.parseItemGrid(link).then(resolve, reject);
			setTimeout(reject, timeout);
		});
	}

	async parseItemGrid(link) {
		let config = {
			headers: {
				'User-Agent': randomUseragent.getRandom()
			}
		};
		let proxy = this.proxyManager.rand();
		if (this.proxyList !== 'local') {
			config.httpsAgent = new HttpsProxyAgent({
				host: `${proxy.split(':')[0]}`,
				port: `${proxy.split(':')[1]}`,
				auth: `${proxy.split(':')[2]}:${proxy.split(':')[3]}`
			});
		}
		try {
			const response = await axios.get(`${link}?format=json-pretty&${Math.floor(Math.random() * 999999999)}`, config);
			if (response !== undefined) {
				const responseData = response.data;
				const { items } = responseData;
				if (items !== undefined) return items;
			}
			return [];
		} catch (err) {
			if (err !== undefined) {
				if (err.response) this.logger(err.response.message, 'red');
				else this.logger(err.message, 'red');
			}

			this.counter++;
			const retry = await this.parsedItemGridWithTimeout(link, 2500);
			return retry;
		}
	}

	async logger(message) {
		console.log(
			kleur
				.magenta()
				.dim(
					`[${moment().format('MM/DD/YY HH:mm:ss.SSS')}] Squarespace [Monitor] ${this.name}:` +
						kleur.reset().gray(` ${message}`)
				)
		);
	}
};
