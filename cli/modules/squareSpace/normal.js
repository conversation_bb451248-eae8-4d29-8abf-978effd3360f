const Methods = require('./methods');
const countryRegionData = require('../../assets/country-region.json');
const { CookieJar } = require('tough-cookie');
const { EventEmitter } = require('events');
const { resolve } = require('path');

const feathers = require('@feathersjs/feathers');
const socketio = require('@feathersjs/socketio-client');
const io = require('socket.io-client');
const socket = io('http://localhost:3030');
const app = feathers();
app.configure(socketio(socket));

const SSNormal = class SSNormal extends Methods {
	constructor(group, name, link, inputType, pInput, oInput, randomOos, quantity, profile, proxyList, port) {
		super(
			group,
			name,
			link,
			inputType,
			pInput,
			oInput,
			randomOos,
			quantity,
			profile,
			proxyList,
			'Squarespace',
			'Normal'
		);
		this.fullPath = resolve(`${__filename}`);
		this.questionSolverEventEmitter = new EventEmitter();
		this.captchaEventEmitter = new EventEmitter();
		this.port = port;
	}

	async flow() {
		this.enabled = true;
		this.settings = await this.getSettings();
		this.error = 0;
		this.cookieJar = new CookieJar();
		this.obj = {
			stripe: false,
			success: false,
			cart: false,
			store_id: false,
			crumb: undefined,
			upgradedToSecure: false,
			item: undefined,
			orderConfig: undefined,
			checkoutConfig: {},
			orderConfig: undefined,
			orderCheckIndex: 0,
			hasCustomForm: false,
			isQuestionRequired: false,
			promptQuestions: [],
			answeredQuestions: []
		};
		// set country data mapping
		if (countryRegionData !== undefined && this.enabled) this.countryRegionData = countryRegionData;
		this.proxy = await this.proxyGot();
		this.obj.stripePaymentAgent = await this.getStripePaymentAgent()

		if (this.enabled) {
			const storeInfo = await this.getStoreInfo();
			this.obj.preload = false; // default to false unless store-info found
			if (storeInfo && storeInfo.hasOwnProperty('stripeLiveKey')) {
				this.log('Preload information found');
				if (storeInfo.hasOwnProperty('stripeLiveKey')) {
					this.obj.preload = true;
					this.obj.checkoutConfig.stripeLivePublicApiKey = storeInfo.stripeLiveKey;
					this.obj.payment_token = await this.generatePaymentToken();
				}
			}

			this.obj.item = await this.monitor();
			this.obj.crumb = await this.generateSession();
			this.obj.cart = await this.atc();
			this.obj.checkoutConfig = await this.generateCheckout();

			if (this.obj.stripe && !this.obj.preload) {
				this.obj.payment_token = await this.generatePaymentToken();
				// do not call await on purpose, fire and forget
				this.createStoreInfo({
					storeName: this.parsedLink.host,
					storeType: 'Squarespace',
					storeId: this.obj.store_id,
					stripeLiveKey: this.obj.checkoutConfig.stripeLivePublicApiKey,
					stripeTestKey: this.obj.checkoutConfig.stripeTestPublicApiKey,
					paypalMerchantId: this.obj.checkoutConfig.paypalMerchantId
				});
			}
			if (this.obj.paypal && !this.obj.stripe) {
				this.paypalPreAuthLogin();
				await this.resolveCaptchaSolver();
				await this.paypalAuthenticatedSession();
				// swap back to 'checkout' proxies
				this.proxy = await this.proxyGot();
			}
			await this.submitEmail();
			await this.submitShipping();

			this.parseCustomForm();
			if (this.obj.isQuestionRequired) {
				await this.promptQuestionSolver();
				await this.resolveQuestionSolver();
				this.obj.checkoutConfig.customFormData = this.createCustomFormData();
			}
			await this.charge();
		}
	}
};

module.exports.flow = async function flow(obj) {
	const instance = new SSNormal(
		obj.group,
		obj.name,
		obj.link,
		obj.inputType,
		obj.pInput,
		obj.oInput,
		obj.randomOos,
		obj.quantity,
		obj.profile,
		obj.proxyList,
		obj.port
	);
	await instance.flow();
};

module.exports.SSNormal = SSNormal;
