const got = require('got');
const Methods = require('./methods');
const countryRegionData = require('../../assets/country-region.json');
const { CookieJar } = require('tough-cookie');
const { EventEmitter } = require('events');
const { resolve } = require('path');

const feathers = require('@feathersjs/feathers');
const socketio = require('@feathersjs/socketio-client');
const io = require('socket.io-client');
const socket = io('http://localhost:3030');
const app = feathers();
app.configure(socketio(socket));

const SSPreload = class SSPreload extends Methods {
	constructor(group, name, link, inputType, pInput, oInput, randomOos, quantity, profile, proxyList, port) {
		super(
			group,
			name,
			link,
			inputType,
			pInput,
			oInput,
			randomOos,
			quantity,
			profile,
			proxyList,
			'Squarespace',
			'Normal'
		);
		this.fullPath = resolve(`${__filename}`);
		this.questionSolverEventEmitter = new EventEmitter();
		this.captchaEventEmitter = new EventEmitter();
		this.port = port;
	}

	async flow() {
		this.enabled = true;
		this.settings = await this.getSettings();
		this.error = 0;
		this.cookieJar = new CookieJar();
		this.obj = {
			stripe: false,
			success: false,
			cart: false,
			store_id: false,
			crumb: undefined,
			upgradedToSecure: false,
			item: undefined,
			orderConfig: undefined,
			checkoutConfig: {},
			orderConfig: undefined,
			orderCheckIndex: 0,
			hasCustomForm: false,
			isQuestionRequired: false,
			promptQuestions: [],
			answeredQuestions: []
		};
		// set country data mapping
		if (countryRegionData !== undefined && this.enabled) this.countryRegionData = countryRegionData;
		this.proxy = await this.proxyGot();
		this.obj.stripePaymentAgent = await this.getStripePaymentAgent();

		if (this.enabled) {
			this.obj.item = await this.randomItemMonitor();

			//this.obj.item = await this.monitor();
			this.obj.crumb = await this.generateSession();
			this.obj.cart = await this.atc();
			this.obj.checkoutConfig = await this.generateCheckout();

			this.obj.preload = false;
			// determine stripe or paypal
			// stripe or paypal waterfall logic, tbh users should run normal if stripe is supported
			if (this.obj.stripe) {
				this.obj.payment_token = await this.generatePaymentToken();
				this.obj.preload = true;
			}

			if (this.obj.paypal && !this.obj.stripe) {
				this.paypalPreAuthLogin();
				await this.resolveCaptchaSolver();
				await this.paypalAuthenticatedSession();
				this.obj.preload = true;
			}

			// clear session logic
			if (this.obj.preload) {
				// swap back to 'checkout' proxies
				this.proxy = await this.proxyGot();
				// since we generate a checkout session with our test store using the hard-coded url
				// we must now clear the cookie before proceeding to generate a checkout with the real store
				// or else it will bitch about the crumb and the checkout session not existing
				this.cookieJar.store.removeCookies('secure.squarespace.com', '/', function (err, cb) {
					if (err) throw new Error('Error removing secure domain cookies');
				});
				this.cookieJar.store.removeCookies(this.parsedLink.host, '/', function (err, cb) {
					if (err) throw new Error(`Error removing domain cookies`);
				});
				// clear pre-loaded item
				delete this.obj.item;
				delete this.obj.cart;
				delete this.obj.checkoutConfig;
			}

			this.obj.item = await this.monitor();
			this.obj.crumb = await this.generateSession();
			this.obj.cart = await this.atc();
			this.obj.checkoutConfig = await this.generateCheckout();

			await this.submitEmail();
			await this.submitShipping();

			this.parseCustomForm();
			if (this.obj.isQuestionRequired) {
				await this.promptQuestionSolver();
				await this.resolveQuestionSolver();
				this.obj.checkoutConfig.customFormData = this.createCustomFormData();
			}
			await this.charge();
		}
	}
};

module.exports.flow = async function flow(obj) {
	const instance = new SSPreload(
		obj.group,
		obj.name,
		obj.link,
		obj.inputType,
		obj.pInput,
		obj.oInput,
		obj.randomOos,
		obj.quantity,
		obj.profile,
		obj.proxyList,
		obj.port
	);
	await instance.flow();
};

module.exports.SSPreload = SSPreload;
