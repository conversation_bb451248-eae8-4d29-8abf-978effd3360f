const { parse } = require('node-html-parser')
const Utils = require('../utils/utils')
const got = require('got')
const tunnel = require('tunnel')
const {CookieJar} = require('tough-cookie')
const {promisify} = require('util')
const tough = require("tough-cookie");
const qs = require('qs')



module.exports = class Methods extends Utils {
    constructor(group, name, link, inputType, pInput, oInput, randomOos, quantity, profile, proxyList, platform, mode) {
        super(group, name, link, inputType, pInput, oInput, randomOos, quantity, profile, proxyList, platform, mode)
        this.states = {Alaska:'1',Alabama:'2','American Samoa':'3',Arizona:'4',Arkansas:'5',California:'6',Colorado:'7',Connecticut:'8',Delaware:'9','District Of Columbia':'10','Federated States Of Micronesia':'11',Florida:'12',Georgia:'13',Guam:'14',Hawaii:'15',Idaho:'16',Illinois:'17',Indiana:'18',Iowa:'19',Kansas:'20',Kentucky:'21',Louisiana:'22',Maine:'23','Marshall Islands':'24',Maryland:'25',Massachusetts:'26',Michigan:'27',Minnesota:'28',Mississippi:'29',Missouri:'30',Montana:'31',Nebraska:'32',Nevada:'33','New Hampshire':'34','New Jersey':'35','New Mexico':'36','New York':'37','North Carolina':'38','North Dakota':'39','Northern Mariana Islands':'40',Ohio:'41',Oklahoma:'42',Oregon:'43',Palau:'44',Pennsylvania:'45','Puerto Rico':'46','Rhode Island':'47','South Carolina':'48','South Dakota':'49',Tennessee:'50',Texas:'51',Utah:'52',Vermont:'53','Virgin Islands':'54',Virginia:'55',Washington:'56','West Virginia':'57',Wisconsin:'58',Wyoming:'59','Armed Forces Africa':'60','Armed Forces Americas (Except Canada)':'61','Armed Forces Canada':'62','Armed Forces Europe':'63','Armed Forces Middle East':'64','Armed Forces Pacific':'65'}
    }

    async atc(VID) { // I feel like i can get the whole checkout in there but atm it does not seem like it
        if (this.enabled) {
            try {
                const body = {
                    "item[variant_id]": `${VID}`,
                    "guest_token": "null",
                    // "checkout[status_event]": "start",
                    // "checkout[email]":	this.profile.shippingAddress.email
                }
                const response = await got.post(`https://www.storenvy.com/checkouts/${this.obj.sub}/current/items.json`, {
                    responseType: 'json',
                    body: qs.stringify(body),
                    headers: {
                        "User-Agent": this.obj.userAgent,
                        "Accept": "application/json, text/javascript, */*; q=0.01",
                        "Accept-Language": "en-US,en;q=0.5",
                        "Content-Type": "application/x-www-form-urlencoded",
                        "X-Requested-With": "XMLHttpRequest",
                        "Origin": "https://www.storenvy.com",
                        "DNT": "1",
                        "Connection": "keep-alive",
                        // "Referer": "https://www.storenvy.com/iframe?pid=31557325&sid=288475&src=https%253A%252F%252Fdanieldanger.storenvy.com%252Fproducts%252F31557325-it-stopped-being-about-the-panic-v2",
                        "Sec-Fetch-Dest": "empty",
                        "Sec-Fetch-Mode": "cors",
                        "Sec-Fetch-Site": "same-origin",
                        "Pragma": "no-cache",
                        "Cache-Control": "no-cache",
                        "TE": "trailers"
                    },
                    cookieJar: this.cookieJar,
                    agent: this.proxyList === 'local' ? false : this.proxy
                })
                await this.log(`${response.statusCode} - Added to cart`)
                return response.body

            } catch (err) {
                this.log(`${err.response ? err.response.statusCode : '000'} - ${err.response ? err.response.statusCode === 422 ? 'ATC OOS' : 'Unknown reason' : 'Error adding to cart'}`, 'error')
                await new Promise(r => setTimeout(r, 1000))
                const retry = await this.atc(VID)
                return retry
            }
        }
    };

    async genCheckout (id, stripe) {
        if(this.enabled) {
            try {
                const data = {
                    "checkout[status_event]": "start",
                    "checkout[email]":	this.profile.shippingAddress.email,
                    "checkout[address_id]":	id,
                    "checkout[stripe_token]": stripe,
                    "checkout[setup_paypal]": "false"
                }
                const response = await got.put(`https://www.storenvy.com/checkouts/${this.obj.sub}/current.json`, {
                    responseType: 'json',
                    body: qs.stringify(data),
                    headers: {
                        "User-Agent": this.obj.userAgent,
                        "Accept": "application/json, text/javascript, */*; q=0.01",
                        "Accept-Language": "en-US,en;q=0.5",
                        "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
                        "X-Requested-With": "XMLHttpRequest",
                        "Origin": "https://www.storenvy.com",
                        "DNT": "1",
                        "Connection": "keep-alive",
                        "Referer": `https://www.storenvy.com/checkout/${this.obj.sub}/cart`,
                        "Sec-Fetch-Dest": "empty",
                        "Sec-Fetch-Mode": "cors",
                        "Sec-Fetch-Site": "same-origin",
                        "Pragma": "no-cache",
                        "Cache-Control": "no-cache",
                        "TE": "trailers"
                    },
                    cookieJar: this.cookieJar,
                    agent: this.proxyList === 'local' ? false : this.proxy

                })
                await this.log(`${response.statusCode} - Checkout generated`)
                return response.body
            } catch (err) {
                await this.log(`${err.response ? err.response.statusCode : '000'} - Error generating checkout`, 'error')
                this.enabled = false
            }
        }
    };

    async postProfile () {
        if(this.enabled) {
            try {
                const data = {
                    "address[country_id]": "226",
                    "address[full_name]": this.profile.shippingAddress.name,
                    "address[address_1]": this.profile.shippingAddress.line1,
                    "address[address_2]": this.profile.shippingAddress.line2,
                    "address[city]": this.profile.shippingAddress.city,
                    "address[state_id]": this.states[this.profile.shippingAddress.state],
                    "address[state_text]": "",
                    "address[postal_code]": this.profile.shippingAddress.postCode,
                    "address[phone]": this.profile.shippingAddress.phone
                }
                const response = await got.post('https://www.storenvy.com/users/addresses.json', {
                    responseType: 'json',
                    body: qs.stringify(data),
                    headers: {
                        "User-Agent": this.obj.userAgent,
                        "Accept": "application/json, text/javascript, */*; q=0.01",
                        "Accept-Language": "en-US,en;q=0.5",
                        "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
                        "X-Requested-With": "XMLHttpRequest",
                        "Origin": "https://www.storenvy.com",
                        "DNT": "1",
                        "Connection": "keep-alive",
                        "Referer": `https://www.storenvy.com/checkout/${this.obj.sub}/shipping`,
                        "Sec-Fetch-Dest": "empty",
                        "Sec-Fetch-Mode": "cors",
                        "Sec-Fetch-Site": "same-origin",
                        "Pragma": "no-cache",
                        "Cache-Control": "no-cache"
                    },
                    cookieJar: this.cookieJar,
                    agent: this.proxyList === 'local' ? false : this.proxy

                })
                await this.log(`${response.statusCode} - Posted profile`)
                return response.body
            } catch (err) {
                await this.log(`${err.response ? err.response.statusCode : '000'} - Error posting profile`, 'error')
                this.enabled = false
            }
        }
    };

    async generatePaymentToken () {
        if (this.enabled) {
            await this.log('000 - Getting payment token')
            let stripeIds = ''
            try {
                const response = await got.post('https://m.stripe.com/6', {
                    responseType: 'json',
                    agent: this.proxyList === 'local' ? false : this.proxy
    
                })
                stripeIds = response
            } catch (err) {
                await this.log(`${err.response ? err.response.statusCode : '000'} - Error getting Stripe IDs`, 'error')
                this.enabled = false
            }
            if (this.enabled) {
                try {
                    let response = await got.post('https://api.stripe.com/v1/payment_methods', {
                        responseType: 'json',
                        body: qs.stringify({
                            'type': 'card',
                            // 'billing_details[name]': this.profile.billingAddress.name,
                            // 'billing_details[address][line1]': this.profile.billingAddress.line1,
                            // 'billing_details[address][line2]': this.profile.billingAddress.line2,
                            // 'billing_details[address][city]': this.profile.billingAddress.city,
                            // 'billing_details[address][state]': this.profile.billingAddress.state,
                            'billing_details[address][postal_code]': this.profile.billingAddress.postCode,
                            'billing_details[address][country]': 'US',
                            'card[number]': this.profile.paymentDetails.cardNumber,
                            'card[cvc]': this.profile.paymentDetails.cardCvv,
                            'card[exp_month]': this.profile.paymentDetails.cardExpMonth,
                            'card[exp_year]': this.profile.paymentDetails.cardExpYear,
                            'guid': stripeIds.body.guid,
                            'muid': stripeIds.body.muid,
                            'sid': stripeIds.body.sid,
                            'payment_user_agent': this.obj.stripePaymentAgent,
                            'time_on_page': '50318', //TODO
                            'key': this.link.includes('danieldanger') ? 'pk_live_Z7ILjBLTzcl8dI5CLgmYwah7' : this.link.includes('sketone') ? 'pk_live_Z7ILjBLTzcl8dI5CLgmYwah7' : false //TODO
                        }),
                        headers: {
                            "User-Agent": this.obj.userAgent,
                            "Accept": "application/json",
                            "Accept-Language": "en-US,en;q=0.5",
                            "Content-Type": "application/x-www-form-urlencoded",
                            "Origin": "https://js.stripe.com",
                            "DNT": "1",
                            "Connection": "keep-alive",
                            "Referer": "https://js.stripe.com/",
                            "Sec-Fetch-Dest": "empty",
                            "Sec-Fetch-Mode": "cors",
                            "Sec-Fetch-Site": "same-site",
                            "Pragma": "no-cache",
                            "Cache-Control": "no-cache"
                        },
                        agent: this.proxyList === 'local' ? false : this.proxy
                    });
                    // await console.log(response.body)
                    await this.log(`${response.statusCode} - Generated payment token`)
                    if (response.statusCode === 200) return response.body["id"];
                    
                } catch (err) {
                    this.log(`${err.response ? err.response.statusCode : '000'} - Error generating payment token`, 'error')
                    // const redo  = this.generatePaymentToken()
                    // return redo
                    // console.log(err)
                    this.enabled = false
                }
            }
        }
    };

    async submitOrder () {
        if(this.enabled) {
            try {
                const response = await got.put(`https://www.storenvy.com/checkouts/${this.obj.sub}/${this.obj.checkout.checkout.id}.json`, {
                    responseType: 'json',
                    body: qs.stringify({
                        "checkout[status_event]": "pay"
                    }),
                    headers: {
                        "User-Agent": this.obj.userAgent,
                        "Accept": "application/json, text/javascript, */*; q=0.01",
                        "Accept-Language": "en-US,en;q=0.5",
                        "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
                        "X-Requested-With": "XMLHttpRequest",
                        "Origin": "https://www.storenvy.com",
                        "DNT": "1",
                        "Connection": "keep-alive",
                        "Referer": `https://www.storenvy.com/checkout/${this.obj.sub}/review`, // TODO
                        "Sec-Fetch-Dest": "empty",
                        "Sec-Fetch-Mode": "cors",
                        "Sec-Fetch-Site": "same-origin",
                        "Pragma": "no-cache",
                        "Cache-Control": "no-cache"
                    },
                    cookieJar: this.cookieJar,
                    agent: this.proxyList === 'local' ? false : this.proxy

                })
                await this.log(`${response.statusCode} - I think you checked out but idk fosho`, 'success')
                return response.body
            } catch (err) {
                this.log(`${err.response ? err.response.statusCode : '000'} - Error submitting order`)
                // console.log(err)
                if (err.response) {
                    if (err.response.statusCode === 422) {
                        this.log(`${err.response.statusCode} - ${err.response.body.checkout.errors.card}`, 'error')
                        // console.log(err.response.body.checkout.errors)
                    } else {
                        console.log(err.response)
                    }
                } else {
                    console.log(err)
                }
                this.enabled = false
                return false
            }
        }
    };

    // MONITOR
    async monitor () {
        if (this.enabled) {
            await this.log(`000 - Monitoring ${this.link.split('/')[2]}`)
        
            // Lowering oInput
            this.oInput = this.oInput.toLowerCase()
    
            let monitored
            // Is the product input (pInput) a direct link, option ID, or Keyword
            this.oInput = this.oInput.toLowerCase()
            // console.log(this.inputType)
            switch (this.inputType) {
                case 'DL':
                    monitored = await this.dlMonitor() // NEEDS TO BE ADDED
                    break;
                case 'PID':
                    monitored = {link: this.link, OID: this.pInput}
                    break;
                case 'KW':
                    this.pInput = this.pInput.map(p => p.toLowerCase())
                    this.positives = this.pInput.filter(keyword => keyword[0] === '+')
                    this.negatives = this.pInput.filter(keyword => keyword[0] === '-')
                    monitored = await this.kwMonitor()
                    break;
            }
            if (monitored) {
                // this.log(`000 - ${JSON.stringify(monitored)}`)
                return (monitored)
            }
        }
    };

    async kwMonitor() {
        if (this.enabled) {
            try {
                const response = await got.get(`${this.link}/products.json?cgid=${this.randomStr()}`, {
                    responseType: 'json',
                    headers: {
                        "User-Agent": this.userAgent(),
                        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                        "Accept-Language": "en-US,en;q=0.5",
                        "DNT": "1",
                        "Connection": "keep-alive",
                        "Upgrade-Insecure-Requests": "1",
                        "Sec-Fetch-Dest": "document",
                        "Sec-Fetch-Mode": "navigate",
                        "Sec-Fetch-Site": "none",
                        "Sec-Fetch-User": "?1",
                        "Pragma": "no-cache",
                        "Cache-Control": "no-cache",
                        "TE": "trailers"
                    },
                    agent: await this.proxyGot('monitor')
                })
                // await console.log(response.body)
                const products = response.body
                for (let i = 0; i < products.length; i++) {
                    const product = products[i]
                    product.name = product.name.toLowerCase().replace(/[\-]/gi, ' ').toLowerCase().replace(/[^\w\s]/gi, '').split(' ')
                    const pMatches = await this.positives.filter(keyword => product.name.includes(keyword.slice(1, keyword.length)))
                    const nMatches = await this.negatives.filter(keyword => product.name.includes(keyword.slice(1, keyword.length)))
                    if (pMatches.length === this.positives.length && nMatches.length === 0) {
                        const variants = product.variants
                        for (let i = 0; i < variants.length; i++) {
                            const v = variants[i]
                            // console.log(i)
                            if (v.variant.name.toLowerCase() === this.oInput.toLowerCase()) {
                                await this.log(`${response.statusCode} - Found: ${product.name.join(' ')} - ${v.variant.name} ($${v.variant.price.toFixed(2)})`)
                                return v.variant.id
                            }
        
                            if (i === variants.length-1 && this.randomOos) {
                                await this.log(`${response.statusCode} - Found: ${product.name.join(' ')} - ${v.variant.name} ($${v.variant.price.toFixed(2)})`)
                                return variants[0].variant.id
                            }
                        }
                        // console.log(`${product.id} - ${product.name} - ${product.status} - ${product.variants[0].variant.id} - ${product.variants[0].variant.in_stock}`)
                        this.log(`${response.statusCode} - Found: ${product.name}`)
                        return product.variants[0].variant.id
                    }
                }

            } catch(err) {
                await this.log(`${err.response ? err.response.statusCode : '000'} - Error keyword monitor | ${err.message}`, 'error')
                // console.log(err)
            }
            await new Promise(r => setTimeout(r, 1000))
            const retry = await this.kwMonitor()
            return retry
        }
    };

    async dlMonitor () {
        if (this.enabled) {
            try {
                const urlObj = new URL(this.pInput)
                const uri = `${urlObj.origin}${urlObj.pathname[urlObj.pathname.length-1] === '/' ? urlObj.pathname.substring(0, urlObj.pathname.length - 1) : urlObj.pathname}`
                const response = await got.get(`${uri}.json?cgid=${this.randomStr()}`, {
                    responseType: 'json',
                    headers: {
                        "User-Agent": this.userAgent(),
                        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                        "Accept-Language": "en-US,en;q=0.5",
                        "DNT": "1",
                        "Connection": "keep-alive",
                        "Upgrade-Insecure-Requests": "1",
                        "Sec-Fetch-Dest": "document",
                        "Sec-Fetch-Mode": "navigate",
                        "Sec-Fetch-Site": "none",
                        "Sec-Fetch-User": "?1",
                        "Pragma": "no-cache",
                        "Cache-Control": "no-cache",
                        "TE": "trailers"
                    },
                    agent: await this.proxyGot('monitor')
                })
                const variants = response.body.variants
                for (let i = 0; i < variants.length; i++) {
                    const v = variants[i]
                    // console.log(i)
                    if (v.variant.name.toLowerCase() === this.oInput.toLowerCase()) {
                        await this.log(`${response.statusCode} - Found: ${response.body.name} - ${v.variant.name}`)
                        return v.variant.id
                    }

                    if (i === variants.length-1 && this.randomOos) {
                        await this.log(`${response.statusCode} - Found: ${response.body.name} - ${v.variant.name}`)
                        return variants[0].variant.id
                    }
                }
            } catch (err) {
                await this.log(`${err.response ? err.response.statusCode : '000'} - Error link monitor | ${err.message}`, 'error')
                // console.log(err)
            }
            // await new Promise(r => setTimeout(r, 1000))
            // const retry = await this.dlMonitor()
            // return retry
        }
    }


}