
const { resolve } = require('path')
const Methods = require('./methods')
const {CookieJar} = require('tough-cookie')
const {promisify} = require('util')

const SENormal = class SENormal extends Methods {
    constructor(group, name, link, inputType, pInput, oInput, randomOos, quantity, profile, proxyList, platform, mode, port) {
        super(group, name, link, inputType, pInput, oInput, randomOos, quantity, profile, proxyList, 'Store Envy', 'Normal')
		this.fullPath = resolve(`${__filename}`)
		this.port = port
    }

    async flow() {
        this.cookieJar = new CookieJar();
        const setCookie = promisify(this.cookieJar.setCookie.bind(this.cookieJar));
        this.obj = {
            // stripe: false,
            // success: false,
            // cart: false,
            // store_id: false,
            // order_id: false,
            sub: this.link.split('/')[2].split('.')[0],
        }
        this.obj.userAgent = await this.userAgent()

        this.enabled = true
        this.error = 0

        // Proxy
        this.proxy = await this.proxyGot()

        this.obj.stripePaymentAgent = await this.getStripePaymentAgent();
        
        // CHECKOUT FLOW
        const stripeToken = await this.generatePaymentToken()
        const monitor = await this.monitor()
        this.obj.cart = await this.atc(monitor)
        const profiled = await this.postProfile()
        if (this.enabled) {
            this.obj.checkout = await this.genCheckout(profiled.id, stripeToken)
            const submit = await this.submitOrder()
            if (this.enabled && submit) {
                await this.staffLog(this.link, this.obj.checkout.checkout.orders[0].items[0].product_name, `${this.obj.checkout.checkout.total.cents/100}`, `https:${this.obj.checkout.checkout.orders[0].items[0].photo_url}`)
            }
        }

    }
}

module.exports.flow = async function flow(obj) {
    const instance = new SENormal(obj.group, obj.name, obj.link, obj.inputType, obj.pInput, obj.oInput, obj.randomOos, obj.quantity, obj.profile, obj.proxyList, obj.platform, obj.mode, obj.port)
    await instance.flow()
}

module.exports.SENormal = SENormal

// TODO
// Error handling
// Retry on decline
// DL monitor