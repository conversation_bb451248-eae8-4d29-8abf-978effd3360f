const _remove = require("lodash/remove");

module.exports.match = function (origText, keywords = []) {
  let text = origText.toLowerCase();
  let tokenizeTexts = text.split(" ");
  let resultsMatch = false;
  let p = [];
  let n = [];
  for (let i = 0; i < keywords.length; i++) {
    let kw = keywords[i];
    let kwSubString = kw;
    let isPositive = false;
    if (kwSubString.indexOf("+") > -1 || kwSubString.indexOf("-" > -1)) {
      kwSubString = kw.substring(1, kw.length);
      if (kw.indexOf("+") > -1) isPositive = true;
    }
    for (let j = 0; j < tokenizeTexts.length; j++) {
      let searchToken = tokenizeTexts[j];
      if (isPositive) {
        if (searchToken === kwSubString) {
          p = p.concat({ [kwSubString]: true });
          _remove(p, (p) => p[kwSubString] == false);
        } else {
          if (!p.some((p) => Object.keys(p).includes(kwSubString)))
            p = p.concat({ [kwSubString]: false });
        }
      } else {
        if (searchToken === kwSubString) {
          n = n.concat({ [kwSubString]: true });
          _remove(n, (n) => n[kwSubString] == false);
        } else {
          if (!n.some((n) => Object.keys(n).includes(kwSubString)))
            n = n.concat({ [kwSubString]: false });
        }
      }
    }
  }

  let allPositiveP =
    p.length > 0 &&
    p.filter((p) => p[Object.keys(p)] === true).length === p.length;
  let allNegativeN =
    n.length > 0 &&
    n.filter((n) => n[Object.keys(n)] === true).length <= n.length &&
    n.filter((n) => !n[Object.keys(n)]).length !== n.length;

  if (allPositiveP && !allNegativeN) resultsMatch = true;

  // console.log("Product Name: ", origText);
  // console.log("Keywords: ", JSON.stringify(keywords));
  // console.log("All Positive P matches: ", allPositiveP);
  // console.log("All Negative N not matches", allNegativeN);
  // console.log(
  //   "Is Product Name matching with Keyword: ",
  //   resultsMatch ? "Yes" : "No"
  // );
  return resultsMatch;
};
