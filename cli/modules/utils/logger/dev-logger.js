const { format, createLogger, transports, addColors } = require('winston')
const { timestamp, colorize, combine, printf, errors } = format
const customLevels = require('./custom-levels.json')

function buildDevLogger() {
  let format = combine(
    colorize({
      all: true,
    }),
    timestamp({
      format: 'YY-MM-DD HH:mm:ss.SSS',
    }),
    errors({ stack: true }),
    printf(({ level, message, timestamp, stack }) => `${timestamp} ${level}: ${stack || message}`)
  )

  addColors(customLevels.colors)

  return createLogger({
		levels: customLevels.levels,
		format: format,
		transports: [
			new transports.Console({ level: 'monitor' }),
			new transports.File({ filename: 'logs/error.log', level: 'error' }),
			new transports.File({ filename: 'logs/combined.log' })
		],
		level: 'monitor'
	});
}

module.exports = buildDevLogger
