const { format, createLogger, transports, addColors } = require('winston')
const { timestamp, colorize, combine, printf, errors } = format
const customLevels = require('./custom-levels.json')

function buildProdLogger() {
  let consoleFormat = combine(
    colorize({
      all: true,
    }),
    timestamp({
      format: 'YY-MM-DD HH:mm:ss.SSS',
    }),
    errors({ stack: true }),
    printf(({ level, message, timestamp, stack }) => `${timestamp} ${level}: ${stack || message}`)
  )

  let fileFormat = combine(
    timestamp({
      format: 'YY-MM-DD HH:mm:ss.SSS',
    }),
    errors({ stack: true }),
    printf(({ level, message, timestamp, stack }) => `${timestamp} ${level}: ${stack || message}`)
  )

  // Not used for now
  // let httpFormat = combine(timestamp(), errors({ stack: true }), json());

  addColors(customLevels.colors)

  return createLogger({
		levels: customLevels.levels,
		format: consoleFormat,
		defaultMeta: { service: 'stackd-cli' },
		transports: [
			new transports.Console({ level: 'monitor' }),
			new transports.File({ filename: 'logs/error.log', level: 'error', format: fileFormat }),
			new transports.File({ filename: 'logs/combined.log', format: fileFormat })
		],
		level: 'monitor'
	});
}

module.exports = buildProdLogger
