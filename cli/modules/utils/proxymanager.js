const randomInt = require('./randomint')

module.exports =  class ProxyManager {
    constructor(proxyList){
        // immutable
        this.proxyList = proxyList;
        // mutable
        this.proxySet = new Set(this.proxyList);
    }

    rand () {
	    return this.proxyList[randomInt(0, this.proxyList.length - 1)]
    }

    seq()  {
        // refresh list so that logic may roundrobbin
        if(this.proxySet.size === 0)
            this.proxySet = new Set(this.proxyList);
        const proxyArr = Array.from(this.proxySet);
        const proxy = proxyArr[0];
        if(this.proxySet.has(proxy))
            this.proxySet.delete(proxy);
        return proxy;
    }
}