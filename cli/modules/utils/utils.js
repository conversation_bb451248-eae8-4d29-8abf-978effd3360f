const axios = require('axios').default;
const got = require('got');
const axiosCookieJarSupport = require('axios-cookiejar-support').default;
const tough = require('tough-cookie');
const HttpsProxyAgent = require('https-proxy-agent');
const kleur = require('kleur');
const moment = require('moment');
const qs = require('qs');
const fs = require('fs');
const { parse } = require('node-html-parser');
const userAgentsData = require('../../assets/user-agents.json');
const tunnel = require('tunnel');
const logger = require('./logger/index');

module.exports = class Utils {
	constructor(group, name, link, inputType, pInput, oInput, randomOos, quantity, profile, proxyList, platform, mode) {
		this.group = group;
		this.name = name;
		this.link = link;
		this.inputType = inputType;
		this.pInput = pInput;
		this.oInput = oInput;
		this.randomOos = randomOos;
		this.quantity = quantity;
		this.profile = profile;
		this.proxyList = proxyList;
		this.platform = platform;
		this.mode = mode;
		if (this.link && this.link.length > 0) this.parsedLink = new URL(this.link);
		this.enabled = false;
	}

	async message(message, color = 'gray') {
		console.log(
			kleur
				.cyan()
				.dim(
					`[${moment().format('MM/DD/YY HH:mm:ss.SSS')}] ${this.platform} [${this.mode}] ${this.name}:` +
						kleur.reset()[color](` ${message}`)
				)
		);
	}

	async proxyChange() {
		if (this.proxyList !== 'local') {
			try {
				const response = await axios.get('http://localhost:3030/proxy-rotator');
				this.log(`${response.status} - New proxy: ${response.data.split(':')[0]}`);
				return new HttpsProxyAgent({
					host: `${response.data.split(':')[0]}`,
					port: `${response.data.split(':')[1]}`,
					auth: `${response.data.split(':')[2]}:${response.data.split(':')[3]}`
				});
			} catch (err) {
				this.log(`${err.response ? err.response.status : '000'} - Error fetching new proxy`, 'error');
			}
		} else {
			return 'local';
		}
	}

	async proxyGot(list = 'checkout') {
		// list can be "monitor" or "checkout"
		// if (this.proxyList !== false) {
		try {
			const response = await got.get(`http://localhost:3030/proxy-rotator/${this.group}?type=${list}`, {
				responseType: 'json'
			});
			if (response.statusCode === 204) {
				this.log(`${response.statusCode} - ${list} is using localhost`);
				return false;
			} else {
				this.log(`${response.statusCode} - New ${list} proxy: ${response.body.split(':')[0]}`);
				return {
					https: tunnel.httpsOverHttp({
						proxy: {
							host: `${response.body.split(':')[0]}`,
							port: parseFloat(response.body.split(':')[1]),
							proxyAuth: `${response.body.split(':')[2]}:${response.body.split(':')[3]}`
							// headers: {
							//     'User-Agent': 'Node',
							//     connection: 'keep-alive'
							// }
						}
					})
				};
			}
		} catch (err) {
			this.log(`${err.response ? err.response.statusCode : '000'} - Error fetching new ${list} proxy`, 'error');
			// console.log(err)
			this.enabled = false;
		}
		// } else {
		//     return false
		// }
	}

	userAgent() {
		return userAgentsData[Math.floor(Math.random() * userAgentsData.length)];
	}

	// Post something to the extensions monitor feed
	async feed(obj) {
		try {
			const response = await axios.post('http://localhost:3030/monitor', {
				platform: `${this.platform} - ${this.name} - ${this.profile.shippingAddress.email}`,
				site: this.link.split('/')[2],
				type: 'Checkout',
				title: obj.title,
				link: obj.link,
				price: obj.price,
				image: obj.image,
				variants: obj.variants,
				cookies: this.cookieId ? this.cookieId : false
			});
			this.log(`${response.status} - Posted to monitor`, 'prompt');
		} catch (err) {
			this.log(`${err.response ? err.response.status : '000'} - Error posting to monitor`, 'error');
		}
	}

	async feed(obj) {
		// const cookieId = this.randomStr()
		// console.log(cookieId)
		try {
			const response = await axios.post('http://localhost:3030/monitor', {
				platform: `${this.platform} - ${this.name} - ${this.profile.shippingAddress.email}`,
				site: this.link.split('/')[2],
				type: 'Checkout',
				title: obj.title,
				link: obj.link,
				price: obj.price,
				image: obj.image,
				variants: obj.variants,
				cookies: this.cookieId ? this.cookieId : false
			});
			//  this.log(`${response.status} - Posted to monitor`, 'prompt')
		} catch (err) {
			this.log(`${err.response ? err.response.status : '000'} - Error posting to monitor`, 'error');
		}

		if (obj.cookies) {
			try {
				const response = await axios.post('http://localhost:3030/cookies', {
					id: cookieId,
					cookies: obj.cookies
				});
				this.log(`${response.status} - Posted to monitor`, 'prompt');
			} catch (err) {
				this.log(`${err.response ? err.response.status : '000'} - Error posting cookies`, 'error');
			}
		}
	}

	log(message, type) {
		type = type !== undefined ? type.toLowerCase() : 'info';
		switch (type) {
			case 'monitor':
				logger.monitor(`${this.platform} [${this.mode}] ${this.name}: ${message}`);
				break;
			case 'success':
				logger.success(`${this.platform} [${this.mode}] ${this.name}: ${message}`);
				break;
			case 'prompt':
				logger.prompt(`${this.platform} [${this.mode}] ${this.name}: ${message}`);
				break;
			case 'debug':
				logger.debug(`${this.platform} [${this.mode}] ${this.name}: ${message}`);
				break;
			case 'trace':
				logger.trace(`${this.platform} [${this.mode}] ${this.name}: ${message}`);
				break;
			case 'info':
				logger.info(`${this.platform} [${this.mode}] ${this.name}: ${message}`);
				break;
			case 'warn':
				logger.warn(`${this.platform} [${this.mode}] ${this.name}: ${message}`);
				break;
			case 'error':
				logger.error(`${this.platform} [${this.mode}] ${this.name}: ${message}`);
				break;
			default:
				logger.info(`${this.platform} [${this.mode}] ${this.name}: ${message}`);
				break;
		}
	}

	randomInt(min, max) {
		// min and max included
		return Math.floor(Math.random() * (max - min + 1) + min);
	}

	randomStr(quantity = 8) {
		const alphaNum = [
			'A',
			'B',
			'C',
			'D',
			'E',
			'F',
			'G',
			'H',
			'I',
			'J',
			'K',
			'L',
			'M',
			'N',
			'O',
			'P',
			'Q',
			'R',
			'S',
			'T',
			'U',
			'V',
			'W',
			'X',
			'Y',
			'Z',
			'0',
			'1',
			'2',
			'3',
			'4',
			'5',
			'6',
			'7',
			'8',
			'9'
		];
		let str = '';
		for (let i = 0; i < quantity; i++) {
			str += alphaNum[Math.floor(Math.random() * alphaNum.length)];
		}
		return str;
	}

	jwtDecode(token) {
		const base64 = token.split('.')[1];
		return JSON.parse(Buffer.from(base64, 'base64').toString());
	}

	async staffLog(
		link = 'https://duckduckgo.com',
		productTitle = 'Unknown product',
		cost = '0.00',
		image = 'https://cdn.discordapp.com/attachments/714346599735754762/843321954688565278/blue.png'
	) {
		const data = {
			content: null,
			embeds: [
				{
					title: `Success - ${link.split('/')[2]}`,
					url: link,
					color: 1930750,
					fields: [
						{
							name: 'Product',
							value: productTitle
						},
						{
							name: 'Cost',
							value: cost,
							inline: true
						},
						{
							name: 'Quantity',
							value: `${this.quantity}`,
							inline: true
						},
						{
							name: 'Timestamp',
							// "value": `<t:${Date.now()}>`,
							value: `${new Date().toISOString()}`,
							inline: true
						},
						{
							name: 'Proxy',
							value: this.proxy ? 'True' : 'False',
							inline: true
						},
						{
							name: 'Product input',
							value: `${this.pInput}`,
							inline: true
						},
						{
							name: 'Option input',
							value: `${this.oInput ? this.oInput : 'false'}`,
							inline: true
						}
					],
					author: {
						name: `${this.platform} (${this.mode})`
					},
					footer: {
						text: `Stackd - Staff checkout log`,
						icon_url: 'https://cdn.discordapp.com/attachments/714346599735754762/843321954688565278/blue.png'
					},
					thumbnail: {
						url: image
					}
				}
			]
		};
		// await console.log(data.embeds[0])
		try {
			const response = await axios.post('https://events.hookdeck.com/e/src_GA9o5bBUVxvlWmISDsiUqVh8', data);
		} catch (err) {
			this.log(`${err.response ? err.response.status : '000'} - Error staff checkout log`); // Comment this out later
		}
	}

	async getSettings() {
		try {
			const response = await got.get('http://localhost:3030/settings', { responseType: 'json' });
			return response.body;
		} catch (error) {
			this.log(
				`${error.response ? error.response.statusCode : '000'} - Error getting settings: ${error.message}`,
				'error'
			);
			return false;
		}
	}
	async userWebhook(
		link = 'https://duckduckgo.com',
		productTitle = 'Unknown product',
		cost = '0.00',
		image = 'https://cdn.discordapp.com/attachments/714346599735754762/843321954688565278/blue.png'
	) {
		if (this.enabled && this.settings && this.settings.webhook.includes('https://')) {
			try {
				const data = {
					content: null,
					embeds: [
						{
							title: `Success - ${link.split('/')[2]}`,
							url: link,
							color: 1930750,
							fields: [
								{
									name: 'Product',
									value: productTitle
								},
								{
									name: 'Cost',
									value: cost,
									inline: true
								},
								{
									name: 'Quantity',
									value: `${this.quantity}`,
									inline: true
								},
								{
									name: 'Timestamp',
									value: `||${new Date().toISOString()}||`,
									inline: true
								},
								{
									name: 'Proxy',
									value: `||${this.proxy ? this.proxy.https.proxyOptions.host : 'False'}||`,
									inline: true
								},
								{
									name: 'Product input',
									value: `${this.pInput}`,
									inline: true
								},
								{
									name: 'Option input',
									value: `${this.oInput ? this.oInput : 'false'}`,
									inline: true
								},
								{
									name: 'Email',
									value: `||${this.profile.shippingAddress.email}||`,
									inline: true
								}
							],
							author: {
								name: `${this.platform} (${this.mode})`
							},
							footer: {
								text: `Stackd`,
								icon_url: 'https://cdn.discordapp.com/attachments/714346599735754762/843321954688565278/blue.png'
							},
							thumbnail: {
								url: image
							}
						}
					]
				};
				const response = await got.post(this.settings.webhook, {
					responseType: 'json',
					json: data
				});
			} catch (error) {
				this.log(
					`${error.response ? error.response.statusCode : '000'} - Error posting success: ${error.message}`,
					'error'
				);
			}
		}
	}

	async successAnalytics(url, type, order_number = false) {
		try {
			const response = await got.post('http://localhost:3030/orders', {
				responseType: 'json',
				json: {
					url: url,
					email: this.profile.shippingAddress.email,
					order_number: order_number,
					type: type,
					zip: this.profile.billingAddress.postCode
				}
			});
		} catch (err) {
			this.log(`${err.response ? err.response.statusCode : '000'} - Error posting order to analytics`, 'error');
		}
	}

	// returns account
	async findAccount() {
		try {
			const response = await got.get('http://localhost:3030/accounts', {
				responseType: 'json'
			});
			const website = response.body[this.link.split('/')[2]];
			if (website) {
				if (website[this.profile.shippingAddress.email]) {
					return website[this.profile.shippingAddress.email];
				} else {
					return false;
				}
			} else {
				return false;
			}
		} catch (error) {
			this.log(
				`${error.response ? error.response.statusCode : '000'} - Error getting accounts: ${error.message}`,
				'error'
			);
			return false;
		}
	}

	async getStoreInfo() {
		try {
			const storeInfo = (
				await got.get(`http://localhost:3030/store-info/${this.parsedLink.host}?query=storeName`, {
					responseType: 'json'
				})
			).body;
			if (
				!storeInfo.hasOwnProperty('storeName') &&
				!(storeInfo.hasOwnProperty('storeId') || storeInfo.hasOwnProperty('stripeLiveKey'))
			)
				throw new Error(`store-info response missing necessary key.`);
			return storeInfo;
		} catch (error) {
			this.log(
				`${error.response ? error.response.statusCode : '000'} - Error getting store-info: ${error.message}`,
				'error'
			);
		}
	}

	async createStoreInfo(
		storeInfoData = {
			storeName,
			storeType,
			storeId,
			stripeLiveKey,
			stripeTestKey,
			paypalMerchantId
		}
	) {
		try {
			const storeInfoResponse = await got.post(`http://localhost:3030/store-info`, {
				responseType: 'json',
				json: { ...storeInfoData }
			});
			if (storeInfoResponse.statusCode !== 200) throw new Error('');
			return storeInfo;
		} catch (error) {}
	}

	async getStripePaymentAgent() {
		try {
			const body = (
				await got.get(`http://localhost:3030/stripepaymentagent`, {
					responseType: 'json'
				})
			).body;
			if (!body.hasOwnProperty('stripePaymentAgent') && !body.stripePaymentAgent)
				throw new Error(`Missing payment agent.`);
			return body.stripePaymentAgent;
		} catch (error) {
			this.log(
				`${error.response ? error.response.statusCode : '000'} - Error getting payment agent: ${error.message}`,
				'error'
			);
			
			// automatically set task to not proceed further if missing stripe payment agent
			this.enabled = false
		}
	}

	async delay (time = 1000) {
        return await new Promise((r) => setTimeout(r, time));

    }
};
