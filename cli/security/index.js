const jwt = require('jsonwebtoken');
const fs = require('fs');
const path = require('path');
const publicKEY = fs.readFileSync(path.join(__dirname, '../../certs/public.key'), 'utf8');

module.exports = {
	verifyJwt: (token, { issuer, subject, audience } = options) => {
		return jwt.verify(
			token.replace('Bearer ', ''),
			publicKEY,
			{
				issuer: issuer,
				subject: subject,
				audience: audience
			},
			(err, decoded) => {
				if (!err) return true;
			}
		);
	}
};
