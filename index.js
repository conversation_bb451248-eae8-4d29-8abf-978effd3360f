const got = require('got');
const prompts = require('prompts');
const kleur = require('kleur');
const { verifyJwt } = require('./cli/security');
const {
	BCNormal,
	BCPreload,
	LRNormal,
	MENormal,
	MEPreload,
	SSNormal,
	SSPreload,
	SENormal,
	SWNormal
} = require('./cli/modules/index');

const runningTasks = {};
const runningTaskGroupQuestionSolver = {};

// feather client
const feathers = require('@feathersjs/feathers');
const socketio = require('@feathersjs/socketio-client');
const io = require('socket.io-client');
// worker threads
const { Piscina } = require('piscina');
const setTitle = require('console-title');
// child process
const { fork } = require('child_process');
const path = require('path');
const serverFork = fork(path.join(__dirname, `./server/server.js`));
// feather client init
const socket = io('http://localhost:3030');
const app = feathers();

const { authenticate } = require('./startup/auth')
const { runUpdater } = require('./startup/updater')

// Set up Socket.io client with the socket
app.configure(socketio(socket));
global.piscina = new Piscina({
	concurrentTasksPerWorker: 10
});

let taskViewPid = -1;

process.on('SIGINT', function (e) {
	if (taskViewPid > -1) {
		taskViewPid = -1;
		menu();
	}
});

const menu = async (message = 'Hello') => {
	console.clear();
	// prompt users to authenticate
	const authUser = await authenticate();
	await runUpdater();
	// set title to console window
	setStackdTitle();
	// greeting
	console.log(kleur.bold().cyan(`Stackd | ${message} ${authUser.username}`));

	const onSubmit = async (prompt, answer) => {
		if (prompt.name === 'value') {
			switch (answer) {
				case 'start':
					start();
					break;
				case 'stop':
					stop();
					break;
				case 'create':
					create();
					break;
				case 'edit':
					edit();
					break;
				case 'delete':
					deleted();
					break;
				case 'off':
					off();
					break;
				default:
					// console.log(response)
					console.log('How tf did you select that option, congrats you broke me x_x');
			}
		}
	};
	const onCancel = async (prompt, answers) => {
		if (Object.keys(runningTasks).length === 0) {
			if (Object.keys(answers).length === 0) {
				serverFork.send({ action: 'CHILD_SHOULD_EXIT' });
				serverFork.kill('SIGINT');
				Object.values(runningTasks).forEach((group) => {
					group.child_process.send({ action: 'CHILD_SHOULD_EXIT' });
					group.child_process.kill('SIGINT');
				});
				process.exit(0);
			}
		}
	};

	await prompts(
		{
			type: 'select',
			name: 'value',
			message: 'Menu',
			warn: 'To exit hit ctrl+c',
			choices: [
				{ title: 'Tasks', description: 'Start a task group', value: 'start' },
				{ title: 'Stop Tasks', description: 'Stop a task group', value: 'stop' },
				{ title: 'Close application', description: 'Close down Stackd', value: 'off' }
			]
		},
		{ onSubmit, onCancel }
	);
};
menu();

const stop = async () => {
	const taskGroups = Object.values(runningTasks).map((group) => {
		const individualTask = group[0];
		return {
			title: individualTask.group,
			description: `[${group.pid}] | ${individualTask.platform} | ${!individualTask.mode ? 'Normal' : individualTask.mode
				} | ${individualTask.link ? individualTask.link : individualTask.platform} | ${individualTask.pInput} | ${individualTask.profile.profileGroup
				} | ${individualTask.proxyList}`,
			value: individualTask.group
		};
	});

	taskGroups.push({
		title: 'Main Menu',
		description: 'Go back to the main menu.',
		value: 'menu'
	});

	onSubmit = (prompt, answer) => {
		const selected = runningTasks[answer];
		selected.child_process.send({ action: 'CHILD_SHOULD_EXIT' });
		selected.child_process.kill('SIGINT');
		delete runningTasks[answer];
		menu();
	};

	onCancel = (prompt, answer) => {
		menu();
	};

	await prompts(
		{
			type: 'select',
			name: 'Task groups',
			message: 'Stop a task group',
			choices: taskGroups
		},
		{ onSubmit, onCancel }
	);
};

const off = async () => {
	console.log(kleur.bold().cyan(`Stackd | Goodbye`));
	serverFork.send({ action: 'CHILD_SHOULD_EXIT' });
	serverFork.kill('SIGINT');
	Object.values(runningTasks).forEach((group) => {
		group.child_process.send({ action: 'CHILD_SHOULD_EXIT' });
		group.child_process.kill('SIGINT');
	});
	process.exit(0);
};

// Cleaner console when tasks are running
const running = async () => {
	const stoppingTasks = async () => {
		let activeTasks = 0;
		Object.keys(runningTasks).forEach((group) => {
			runningTasks[group].forEach((task) => {
				if (task.enabled) {
					activeTasks++;
				}
				task.enabled = false;
			});
		});
		menu(`Stopped all running tasks (${activeTasks})`);
	};

	const onSubmit = async () => {
		await stoppingTasks();
	};

	const onCancel = async (prompt, answer) => {
		await stoppingTasks();
	};

	const questions = {
		type: 'invisible',
		name: 'value',
		message: 'Tasks started. Press esc or enter to stop tasks.\n',
		initial: true
	};

	await prompts(questions, { onSubmit, onCancel });
};

const setStackdTitle = () => {
	if (Object.keys(runningTasks).length > 0) setTitle(`Stackd | Running: [${Object.keys(runningTasks).join(' | ')}]`);
	else setTitle(`Stackd`);
};

// Starts tasks
const start = async () => {
	const proxies = await got.get('http://localhost:3030/proxies', {
		resolveBodyOnly: true,
		responseType: 'json'
	});
	const getProfiles = await got.get('http://localhost:3030/profiles', {
		resolveBodyOnly: true,
		responseType: 'json'
	});
	const tasksJson = await got.get('http://localhost:3030/tasks', {
		resolveBodyOnly: true,
		responseType: 'json'
	});

	const tasks = Object.values(tasksJson).map((task) => {
		return {
			title: task.name,
			description: `${task.platform} | ${!task.mode ? 'Normal' : task.mode} | ${task.link ? task.link : task.platform
				} | ${task.pInput} | ${task.profileGroup} | ${task.proxyList}`,
			value: task.name
		};
	});

	tasks.push({
		title: 'Main Menu',
		description: 'Go back to the main menu.',
		value: 'menu'
	});

	// Start tasks here
	const onSubmit = async (prompt, answer) => {
		if (answer === "menu") return menu()
		await startTasks(answer, {
			proxies: proxies,
			profiles: getProfiles,
			tasks: tasksJson
		});
	};

	const onCancel = async (prompt, answer) => {
		menu();
		return false;
	};

	await prompts(
		{
			type: 'select',
			name: 'tasks',
			message: 'Start a task group',
			choices: tasks
		},
		{ onSubmit, onCancel }
	);
};

// Creates and starts tasks
const startTasks = async (answer, data = false) => {
	if (answer) {
		try {
			// answer is the task group's name
			// Retrieve tasks, profiles and proxies if not already provided
			if (data === false) {
				data = {};
				data.proxies = await got.get('http://localhost:3030/proxies', {
					resolveBodyOnly: true,
					responseType: 'json'
				});
				data.profiles = await got.get('http://localhost:3030/profiles', {
					resolveBodyOnly: true,
					responseType: 'json'
				});
				data.tasks = await got.get('http://localhost:3030/tasks', {
					resolveBodyOnly: true,
					responseType: 'json'
				});
			}

			// Configure proxies and profiles
			const profiles = Object.values(data.profiles).filter(
				(profile) => profile.profileGroup === data.tasks[answer].profileGroup
			);
			const proxyList = data.tasks[answer].proxyList === false ? false : data.proxies[data.tasks[answer].proxyList];
			const monitorProxyList =
				data.tasks[answer].monitorProxyList === false ? false : data.proxies[data.tasks[answer].monitorProxyList];

			let paypalProxyList = [];
			if (data.proxies.hasOwnProperty('paypal') || data.proxies.hasOwnProperty('Paypal')) {
				paypalProxyList = data.proxies['paypal'] || data.paypal['Paypal'];
			} else {
				paypalProxyList = data.proxies[data.tasks[answer].proxyList];
			}

			let taskGroupProxyGroups = {
				group: data.tasks[answer].name,
				checkout: proxyList,
				monitor: monitorProxyList
			};
			// include paypal proxy group if exist to proxy rotator service
			if (paypalProxyList && paypalProxyList.length > 0) {
				taskGroupProxyGroups['paypal'] = paypalProxyList;
			}
			if (proxyList !== 'local') {
				// !TODO: this need to be change to false... i think
				await got.post('http://localhost:3030/proxy-rotator', {
					responseType: 'json',
					json: taskGroupProxyGroups
				});
			}

			let index = -1;
			console.clear();

			// Create tasks
			if (runningTasks[answer] === undefined) {
				runningTasks[answer] = profiles.map((profile) => {
					// console.log(1)
					try {
						index++;
						switch (data.tasks[answer].platform) {
							case 'Big Cartel':
								if (data.tasks[answer].mode === 'Preload') {
									return new BCPreload(
										data.tasks[answer].name,
										`${data.tasks[answer].name}-${index}`,
										data.tasks[answer].link,
										data.tasks[answer].inputType,
										data.tasks[answer].pInput,
										data.tasks[answer].oInput,
										data.tasks[answer].randomOos,
										data.tasks[answer].quantity,
										profile,
										data.tasks[answer].proxyList
									);
								} else if (data.tasks[answer].mode === 'Normal') {
									return new BCNormal(
										data.tasks[answer].name,
										`${data.tasks[answer].name}-${index}`,
										data.tasks[answer].link,
										data.tasks[answer].inputType,
										data.tasks[answer].pInput,
										data.tasks[answer].oInput,
										data.tasks[answer].randomOos,
										data.tasks[answer].quantity,
										profile,
										data.tasks[answer].proxyList
									);
								}
								break;
							case 'Limited Run':
								return new LRNormal(
									data.tasks[answer].name,
									`${data.tasks[answer].name}-${index}`,
									data.tasks[answer].link,
									data.tasks[answer].inputType,
									data.tasks[answer].pInput,
									data.tasks[answer].oInput,
									data.tasks[answer].randomOos,
									data.tasks[answer].quantity,
									profile,
									data.tasks[answer].proxyList
								);
								// return new LRNormal(task.name, task.platform, task.mode, task.link, task.pInput, task.oInput, task.randomOos, task.quantity, task.profileName, task.proxyGroup, task.proxy)
								break;
							case 'Metallica':
								if (data.tasks[answer].mode === 'Preload') {
									return new MEPreload(
										data.tasks[answer].name,
										`${data.tasks[answer].name}-${index}`,
										data.tasks[answer].inputType,
										data.tasks[answer].pInput,
										data.tasks[answer].quantity,
										profile,
										data.tasks[answer].proxyList
									);
								} else if (data.tasks[answer].mode === 'Normal' || data.tasks[answer].mode === 'Safe') {
									return new MENormal(
										data.tasks[answer].name,
										`${data.tasks[answer].name}-${index}`,
										data.tasks[answer].inputType,
										data.tasks[answer].pInput,
										data.tasks[answer].quantity,
										profile,
										data.tasks[answer].proxyList
									);
								}
								// name, pInput, oInput, randomOos, quantity, profileName, proxyList
								break;
							case 'Squarespace':
								if (data.tasks[answer].mode === 'Preload')
									return new SSPreload(
										data.tasks[answer].name,
										`${data.tasks[answer].name}-${index}`,
										data.tasks[answer].link,
										data.tasks[answer].inputType,
										data.tasks[answer].pInput,
										data.tasks[answer].oInput,
										data.tasks[answer].randomOos,
										data.tasks[answer].quantity,
										profile,
										data.tasks[answer].proxyList
									);
								else
									return new SSNormal(
										data.tasks[answer].name,
										`${data.tasks[answer].name}-${index}`,
										data.tasks[answer].link,
										data.tasks[answer].inputType,
										data.tasks[answer].pInput,
										data.tasks[answer].oInput,
										data.tasks[answer].randomOos,
										data.tasks[answer].quantity,
										profile,
										data.tasks[answer].proxyList
									);
								break;
							case 'Store Envy':
								return new SENormal(
									data.tasks[answer].name,
									`${data.tasks[answer].name}-${index}`,
									data.tasks[answer].link,
									data.tasks[answer].inputType,
									data.tasks[answer].pInput,
									data.tasks[answer].oInput,
									data.tasks[answer].randomOos,
									data.tasks[answer].quantity,
									profile,
									data.tasks[answer].proxyList
								);
								break;
							default:
								console.log('eror here');
								menu();
						}
					} catch (error) {
						console.log('Error creating tasks: ', error);
					}
				});
			}

			// Start tasks
			try {
				let child;

				if (!runningTasks[answer].pid) {
					child = fork(path.join(__dirname, 'child.js'), [JSON.stringify(runningTasks[answer])], {
						shell: true,
						silent: true
					});
					// set title to console window
					setStackdTitle();
					// assign pid of the child running the tasks group to the runningTasks object
					runningTasks[answer].pid = child.pid;
					// assign instance to ChildProcess obj to runningTasks[answer]
					runningTasks[answer].child_process = child;
					taskViewPid = child.pid;

					runningTasks[answer].stdOutBuffer = [];

					child.on('error', (err) => {
						console.log(err);
					});

					// toggle flag to redisplay captcha required or not when showing child stdout view
					child.on('message', (message) => {
						if (typeof message === 'object') {
							const { action, data } = message;
							switch (action) {
								case 'SET_RUNNING_TASKGROUP_QUESTION_SOLVER':
									break;
								case 'GET_RUNNING_TASKGROUP_QUESTION_SOLVER':
									child.send({ action: 'QUESTION_REQUIRED_SET', data: { runningTaskGroupQuestionSolver, ...data } });
									break;
								default:
									break;
							}
						} else
							switch (message) {
								case 'CAPTCHA_REQUIRED':
									runningTasks[answer].captchaRequired = true;
									break;
								case 'CAPTCHA_CLEARED':
									runningTasks[answer].captchaRequired = false;
									break;
								case 'QUESTION_REQUIRED':
									runningTasks[answer].questionRequired = true;
									break;
								case 'QUESTION_ANSWERED':
									runningTasks[answer].questionRequired = false;
								default:
									break;
							}
					});

					child.stdout.on('data', (data) => {
						if (runningTasks[answer].stdOutBuffer.length > 25) {
							runningTasks[answer].stdOutBuffer.shift();
						}
						if (taskViewPid === runningTasks[answer].pid) {
							console.log(data.toString().replace(/\n$/, ''));
						}
						runningTasks[answer].stdOutBuffer.push(data);
					});

					child.on('close', (code) => {
						console.log(`child process exited with code ${code}`);
					});
				} else {
					// change stdio of child to log mode
					const currentTaskInView = runningTasks[answer];
					taskViewPid = currentTaskInView.pid;
					child = currentTaskInView.child_process;
					// if (currentTaskInView.captchaRequired) console.log('Solver connected, awaiting solve.');
					// if (currentTaskInView.questionRequired) console.log('Question required.');
					for (let i = 0; i < runningTasks[answer].stdOutBuffer.length; i++) {
						if (i === runningTasks[answer].stdOutBuffer.length - 1) {
							console.log('\n' + runningTasks[answer].stdOutBuffer[i].toString());
						} else console.log(runningTasks[answer].stdOutBuffer[i].toString().replace('\n', ''));
					}
				}
			} catch (err) {
				console.error(err);
			}
		} catch (error) {
			console.log('Error with task creation', error);
		}
	}
};

// receive message to start quicktask from quicktask-service
serverFork.on('message', async (task) => {
	await startTasks(task);
});
