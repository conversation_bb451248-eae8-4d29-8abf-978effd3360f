# Stackd Server

### Install?
- `npm i @feathersjs/express, @feathersjs/feathers, @feathersjs/socketio, axios, axios-cookiejar-support, https-proxy-agent, kleur, moment, node-html-parser, random-useragent, tough-cookie`
### Usage
- node server
- Use the chrome extension and Stackd CLI

### TODO
- !!! monitorsJSON has some really weird shit happen to it where an extra } and removal of a comma fuck it up. Happens randomly like every 2nd-3rd time i start server. DRIVING ME FUCKING NUTS.
- Accounts format issue

- Proxy rotator have multiple running task groups
- Tasks (rn it's CLI)
- Child and worker threads
- Improve monitors & fix monitors json
- Woo commerce monitor
- Switch from Axios to Got
- *Long ways away* switch to Golang

