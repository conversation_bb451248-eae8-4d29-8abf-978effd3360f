const got = require('got')
const tunnel = require('tunnel')
const { <PERSON><PERSON><PERSON>ar } = require('tough-cookie')
const { parse } = require('node-html-parser')
const qs = require('qs')
const {promisify} = require('util')
const Utils = require('./utils')
const randomName = require('random-name')

// const capmonster = require('capmonster')
const zipcodes = require('zipcodes');

module.exports = class Met extends Utils {
    constructor(name, type, proxy, domain = false, user = false,) {
        super(name, 'Met', proxy, user)
        this.domain = domain
    }
    async flow() {
        this.enabled = true
        this.cookieJar = new CookieJar()
        this.ua = this.userAgent()
        this.obj = {
            user: this.user ? this.user : (await this.randomS(2,3)).join(''),
            keys: {},
        }
        this.proxy = this.proxyFormat(this.proxy)
        this.obj.email = `${this.obj.user}@${this.domain}`
        await this.message(`000 - Starting ${this.obj.email}`)
        await this.getRegister()
        await this.message('000 - Solving captcha please wait')
        const capTask = await this.capCreate('https://www.metallica.com/account', this.obj.keys.captcha)
        if (this.enabled) {
            const solved = (await this.capGet(capTask)).gRecaptchaResponse
            const acc = await this.postRegister(solved)
            if (acc) {
                const res = await got.patch('http://localhost:3030/accounts', {
                    responseType: 'json',
                    json: {
                        site: 'www.metallica.com',
                        username: this.obj.email,
                        password: 'IUBsaofi438745&*a'
                    }
                })
            }
        }
    }

    async getRegister () {
        if (this.enabled) {
            try {
                const response = await got.get('https://www.metallica.com/register', {
                    responseType: 'text',
                    headers: {
                        'user-agent': this.ua,
                        'referer': 'https://www.metallica.com/login/?original=%2Faccount%2F'
                    },
                    cookieJar: this.cookieJar,
                    agent: this.proxy
                })
                const root = parse(response.body)
                this.obj.keys.captcha = root.querySelector('.g-recaptcha').attrs['data-sitekey']
                this.obj.keys.csrf = root.querySelector('[name="csrf_token"]').attrs.value
                const dwfrm = root.querySelectorAll('.input-text')
                dwfrm.map(d => {
                    if (d.attrs.id.includes('dwfrm_profile_login_password_')) {
                        this.obj.keys.pass = d.attrs.id
                    } else if (d.attrs.id.includes('dwfrm_profile_login_passwordconfirm_')) {
                        this.obj.keys.passConf = d.attrs.id
                    }
                })
                // await console.log(this.obj.keys)
                await this.message(`${response.statusCode} - Recieved keys`)
            } catch (err) {
                this.message(err.response ? `${err.response.statusCode} - Error getting registeration form` : err, 'red')
                this.enabled = false
            }
        }
    };

    async postRegister (token) {
        if (this.enabled) {
            try {
                const date = {
                    m: `${Math.ceil(Math.random() * 12)}`,
                    d: `${Math.ceil(Math.random() * 28)}`,
                    y: Math.floor(Math.random() * (2002 - 1960 + 1) + 1960)
                }
                if (date.m.length === 1) {
                    date.m = `0${date.m}`
                }
                if (date.d.length === 1) {
                    date.d = `0${date.d}`
                }
                const z = zipcodes.random().zip
                // console.log(date)
                const body = {
                    'dwfrm_profile_customer_firstname': randomName.first(),
                    'dwfrm_profile_customer_lastname': randomName.last(),
                    'dwfrm_profile_customer_username': this.obj.email.replace('@','').split('.').join(''),
                    'dwfrm_profile_customer_email': this.obj.email,
                    'dwfrm_profile_customer_emailconfirm': this.obj.email,
                    [this.obj.keys.pass]: 'IUBsaofi438745&*a',
                    [this.obj.keys.passConf]: 'IUBsaofi438745&*a',
                    // 'dwfrm_profile_customer_birthday': '03/28/1968',
                    'dwfrm_profile_customer_birthday': `${date.m}/${date.d}/${date.y}`,
                    'dwfrm_profile_customer_gender': '1',
                    'dwfrm_profile_customer_country': 'us',
                    'dwfrm_profile_customer_postal': zipcodes.random(),
                    'g-recaptcha-response': token,
                    'dwfrm_profile_confirm': 'Apply',
                    'csrf_token': this.obj.keys.csrf
                }
                const headers = { 
                    'User-Agent': this.ua, 
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8', 
                    'Accept-Language': 'en-US,en;q=0.5', 
                    'Content-Type': 'application/x-www-form-urlencoded', 
                    'Origin': 'https://www.metallica.com', 
                    'DNT': '1', 
                    'Connection': 'keep-alive', 
                    'Referer': 'https://www.metallica.com/on/demandware.store/Sites-Metallica-Site/default/Account-RegistrationForm', 
                    'Upgrade-Insecure-Requests': '1', 
                    'Sec-Fetch-Dest': 'document', 
                    'Sec-Fetch-Mode': 'navigate', 
                    'Sec-Fetch-Site': 'same-origin', 
                    'Sec-Fetch-User': '?1', 
                    'Pragma': 'no-cache', 
                    'Cache-Control': 'no-cache', 
                    'TE': 'trailers'
                  }
                // console.log(body)
                // console.log(headers)
                const response = await got.post('https://www.metallica.com/on/demandware.store/Sites-Metallica-Site/default/Account-RegistrationForm', {
                    responseType: 'text',
                    body: qs.stringify(body),
                    headers: headers,
                    cookieJar: this.cookieJar,
                    agent: this.proxy,
                    followRedirect: false
                })
                this.message(`${response.statusCode} - ${response.statusCode === 302 ? `Successfully created account ${this.obj.email}` : `Something went wrong creating account`}`, response.statusCode === 302 ? 'green' : 'yellow')
                if (response.statusCode === 302) {
                    return true
                } else {
                    return false
                }
            } catch (err) {
                this.message(err.response ? `${err.response.statusCode} - Error posting registeration form` : err, 'red')
                this.enabled = false
            }
        }
    };
}

// const proxyFormat = async (proxy) => {
//     return {
//         https: tunnel.httpsOverHttp({
//             proxy: {
//                 host: `${proxy.split(':')[0]}`,
//                 port: parseFloat(proxy.split(':')[1]),
//                 proxyAuth: `${proxy.split(':')[2]}:${proxy.split(':')[3]}`,
//             }
//         })
//     } 
// }

// const test = new Met('testing', 'Met', proxyFormat('************:8123:BOUJEEB:jp3x4p08mjqq'), 'notverified.me')
// test.flow()