const kleur = require('kleur');
const moment = require('moment');
const randomName = require('random-name');
const randomWords = require('random-words');
const got = require('got');
const tunnel = require('tunnel');
const { log } = require('../utils');
const fs = require('fs');

module.exports = class Utils {
	constructor(name, type, proxy, user, link = type) {
		this.name = name;
		this.type = type;
		this.proxy = proxy;
		this.user = user;
		this.link = link;
		this.settings = JSON.parse(fs.readFileSync('./data/settings.json', 'utf-8'));
	}

	async message(message, color = 'gray') {
		await log(`[${moment().format('MM/DD/YY HH:mm:ss.SSS')}] Account Generator [${this.type}] ${this.name}:${message}`);
	}

	async randomS(min, max = min) {
		return randomWords({ min: min, max: max });
	}

	userAgent() {
		const userAgentsData = [
			'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.71 Safari/537.36',
			'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.61 Safari/537.36',
			'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.81 Safari/537.36',
			'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:93.0) Gecko/******** Firefox/93.0',
			'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:92.0) Gecko/******** Firefox/92.0',
			'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.71 Safari/537.36',
			'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.61 Safari/537.36',
			'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Safari/605.1.15',
			'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.82 Safari/537.36',
			'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.81 Safari/537.36',
			'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.82 Safari/537.36',
			'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:92.0) Gecko/******** Firefox/92.0',
			'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:93.0) Gecko/******** Firefox/93.0',
			'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.71 Safari/537.36 Edg/94.0.992.38',
			'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.61 Safari/537.36 Edg/94.0.992.31',
			'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.159 Safari/537.36',
			'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Safari/605.1.15',
			'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.81 Safari/537.36 Edg/94.0.992.47',
		];
		return userAgentsData[Math.floor(Math.random() * userAgentsData.length)];
	}

	async capCreate(site, key) {
		if (this.enabled) {
			try {
				const response = await got.post('https://api.capmonster.cloud/createTask', {
					responseType: 'json',
					json: {
						clientKey: this.settings.capmonster,
						task: {
							type: 'NoCaptchaTaskProxyless',
							websiteUrl: site,
							websiteKey: key,
						},
					},
				});
				if (response.body.errorId === 0) {
					return response.body.taskId;
				} else {
					await log(response.body);
					this.enabled = false;
				}
			} catch (err) {
				log(err, 'error');
				this.enabled = false;
			}
		}
	}

	async capGet(key) {
		if (this.enabled) {
			try {
				const response = await got.post('https://api.capmonster.cloud/getTaskResult', {
					responseType: 'json',
					json: {
						clientKey: this.settings.capmonster,
						taskId: key,
					},
				});
				if (response.body.status === 'processing') {
					await new Promise((r) => setTimeout(r, 2000));
					const retry = await this.capGet(key);
					return retry;
				} else if (response.body.status === 'ready') {
					await this.message('000 - Solved captcha');
					return response.body.solution;
				}
			} catch (err) {
				log(err, 'error');
				this.enabled = false;
			}
		}
	}

	async proxyFormat(proxy) {
		return {
			https: tunnel.httpsOverHttp({
				proxy: {
					host: `${proxy.split(':')[0]}`,
					port: parseFloat(proxy.split(':')[1]),
					proxyAuth: `${proxy.split(':')[2]}:${proxy.split(':')[3]}`,
				},
			}),
		};
	}
};
