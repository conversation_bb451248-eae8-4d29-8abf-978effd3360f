// global-hook.js

const validateJwtFunc = require('./validate-jwt-func');
const { log } = require('../utils');
const injectHeaders = require('./injectHeaders');

module.exports = {
	before: {
		find: [validateJwtFunc, injectHeaders],
		get: [validateJwtFunc, injectHeaders],
		create: [async (context) => injectHeaders(context)]
	},
	error: {
		all: [
			async (context) => {
				log(context.error, 'error');
				return context;
			}
		]
	}
};
