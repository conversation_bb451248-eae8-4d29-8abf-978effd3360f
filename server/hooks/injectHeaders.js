module.exports = async (context) => {
	// define methods that are allowed to have injected headers
	const whiteList = [
		'before:create:store-info',
		'before:find:store-info',
		'before:get:store-info',
		'before:find:stripepaymentagent'
	];

	if (whiteList.indexOf(`${context.type}:${context.method}:${context.path}`) > -1) {
		// define path white-list to inject in token
		const { app } = context;
		const { auth } = app.services;
		const { token } = auth;
		// clear host
		delete context.params.headers.host;
		// clear connection options
		delete context.params.headers.connection;
		// clear content-length
		delete context.params.headers['content-length'];
		// assign token to the Authorization header
		context.params.headers.Authorization = token;
	}
	return context;
};
