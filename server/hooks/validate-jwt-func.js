const { verifyJwt } = require('../utils');
const got = require('got'),
	qs = require('querystring');

module.exports = async (context) => {
	const { app } = context;
	const { auth } = app.services;
	const { token, username, fingerprint, licenseKey } = auth;
	// validate token locally
	if (!token) throw new Error('INVALID_TOKEN');
	const valid = verifyJwt(token, {
		issuer: 'Flubbed Auth API',
		subject: username,
		audience: 'https://*.flubbed.io'
	});
	if (!valid) throw new Error('INVALID_TOKEN');
	try {
		// validate token remotely
		const url = `https://api.flubbed.io/auth/${licenseKey}?${qs.stringify({ mutation: 'VERIFY' })}`;
		await got.put(url, {
			responseType: 'json',
			headers: {
				Authorization: token,
				'Content-Type': 'application/json',
				Accept: 'application/json',
				Connection: 'keep-alive'
			},
			json: {
				username,
				fingerprint
			}
		});
	} catch (error) {
		throw new Error('INVALID_TOKEN');
	}
	return context;
};
