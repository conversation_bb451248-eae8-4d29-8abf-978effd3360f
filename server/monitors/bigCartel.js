const got = require('got');
const tunnel = require('tunnel');
const { log, userAgent } = require('../utils');

module.exports = class monitorBigCartel {
	constructor(id, platform, link, pInput, proxyList, delay, status) {
		this.id = id;
		this.platform = platform;
		this.link = link;
		this.pInput = pInput;
		this.proxyList = proxyList;
		this.delay = delay;
		this.status = false;
	}

	async flow() {
		try {
			// this.status = true
			const prods = {};
			let counter = 0;
			const positives = this.pInput.filter((keyword) => keyword[0] === '+');
			const negatives = this.pInput.filter((keyword) => keyword[0] === '-');
			let proxies;
			if (!this.proxies) {
				let getProxies = await got.get(`http://localhost:3030/proxies`, { responseType: 'json' });
				proxies = getProxies.body[this.proxyList];
				proxies = proxies.map((proxy) => {
					return {
						https: tunnel.httpsOverHttp({
							proxy: {
								host: `${proxy.split(':')[0]}`,
								port: parseFloat(proxy.split(':')[1]),
								proxyAuth: `${proxy.split(':')[2]}:${proxy.split(':')[3]}`,
							},
						}),
					};
				});
			}
			while (this.status) {
				try {
					const response = await got.get(`${this.link}/products.json?${Math.floor(Math.random() * 999999)}`, {
						headers: {
							'User-Agent': await userAgent(),
						},
						agent: proxies[counter],
						responseType: 'json',
					});
					try {
						// await this.logger(`${response.statusCode} - ${response.headers['x-cache']}`);
						// await log(`[Big Cartel] ${this.id}: ${response.statusCode} - ${response.headers['x-cache']}`);
						const products = response.body;
						products.forEach(async (product) => {
							let change = false;
							const pMatches = positives.filter((keyword) =>
								product.name.toLowerCase().includes(keyword.slice(1, keyword.length))
							);
							const nMatches = negatives.filter((keyword) =>
								product.name.toLowerCase().includes(keyword.slice(1, keyword.length))
							);
							if (pMatches.length === positives.length && nMatches.length === 0) {
								if (!prods[product.id]) {
									prods[product.id] = {
										status: product.status,
										options: {},
									};
								}

								if (prods[product.id].status !== product.status) {
									prods[product.id].status = product.status;
									if (product.status === 'active') {
										change = 'New';
									} else if (product.status === 'coming-soon' || product.status === 'sold-out') {
										change = 'Oos';
									}
								}
								product.options.forEach((option) => {
									if (!option.sold_out && !prods[product.id].options[option.name]) {
										//newly in stock and not added to obj yet
										change = 'New';
										prods[product.id].options[option.name] = `${this.link}/${product.url}?${option.id}`;
									} else if (option.sold_out && prods[product.id].options[option.name]) {
										delete prods[product.id].options[option.name];
										if (prods[product.id].options.length > 0) {
											change = 'Update';
										} else {
											change = 'Oos';
										}
									}
								});
								let image = 'https://cdn.discordapp.com/attachments/714346599735754762/843321954688565278/blue.png';
								if (product.images.length > 0) {
									image = product.images[0].url;
								}
								if (change) {
									got.post('http://localhost:3030/monitor', {
										json: {
											platform: 'Big Cartel',
											site: this.link.split('/')[2],
											type: change,
											title: product.name,
											link: `${this.link}${product.url}`,
											price: product.price.toFixed(2),
											image: image,
											variants: prods[product.id].options,
										},
										responseType: 'json',
									});
								}
							}
						});
					} catch (err) {
						// await this.logger(`000 - Error parsing data ${err.message ? err.message : ''}`, 'red');
						await this.logger(`Error parsing data: ${err.message}`, 'error');

					}
				} catch (err) {
					// console.log('Error with request')
					// this.logger(
					// 	`${err.response ? err.response.statusCode : '000'} - Error with request ${err.stack}`,
					// 	'red'
					// );
					await this.logger(`Error with request: ${err.message}`, 'error');

					// add pw up support
				}
				await new Promise((r) => setTimeout(r, this.delay));
				counter === proxies.length - 1 ? (counter = 0) : counter++;
			}
			// await this.logger('000 - Finished');
			await this.logger(`Finished`);
		} catch (error) {
			// await this.logger(`000 - Error in monitor: ${error.stack}`, 'red')
			await this.logger(`Error in monitor: ${error.message}`, 'error');

		}
	}

	async logger(message, type = 'info') {
		await log(`[Big Cartel] ${this.id}: ${message}`, type)
	}
};
