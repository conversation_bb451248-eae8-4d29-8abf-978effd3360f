const checkShop = require('./modules/shopify')
const checkMet = require('./modules/metallica')
const checkBc = require('./modules//bigCartel')

module.exports = updateOrders = async (orders) => {
    await console.log('Checking orders, this may take a few minutes.')
    const newOrders = []
    for  (let i = 0; i < orders.length; i++) {
        const order = orders[i]
        // await console.dir(order)
        if (typeof order === 'object') {
            if (order.status !== 'Canceled' && order.status !== 'Delivered') {
                await console.log(`Checking ${order.url.split('/')[2]} order: ${order.order_number ? order.order_number : order.email}`)
    
                // console.log(order.url ? order.url : order)
                if (order.url.includes('api.bigcartel.com') || order.type === 'Big Cartel') {
                    newOrders.push(await checkBc(order.url))
                } else if (order.type === 'Metallica' || order.url.includes('metallica.com')) {
                    newOrders.push(await checkMet(order))
                } else if (order.type === 'Shopify') {
                    newOrders.push(await checkShop(order))
                    await new Promise(r => setTimeout(r, 1000))
                }
    
            } else {
                newOrders.push(order)
            }
        
            if (orders.indexOf(order) === orders.length-1) {
                // await console.log(newOrders)
                // await console.log(newOrders.length)
                // await write(newOrders)
                return newOrders
            }
        }
    }
}