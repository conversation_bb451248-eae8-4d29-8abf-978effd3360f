const got = require('got')
module.exports = bcTracker = async (order) => {
    try {
        const response = await got.get(order, {
            responseType: 'json'
        })
        // console.log(response.body)
        let totalCostItems = 0
        const arr = []
        const store = await getStore(order)
        let status = ''
        switch (response.body.events[response.body.events.length -1].summary) {
            case 'Payment completed':
                status = 'Confirmed'
                break;
            case 'Marked as shipped':
            case 'Shipment created (customer notified)':
            case 'Shipment created':
                status = 'Shipped'
                break;
            default:
                status = 'Unknown'
        }
        const obj = {
            url: response.body.url,
            type: 'Big Cartel',
            store: store,
            order_date: response.body.completed_at,
            order_number: response.body.number,
            email: response.body.buyer_email,
            // item_name: `${items.product_name} - ${items.option_name}`,
            // image: items.image_url,
            // cost: parseFloat(items.price),
            items: [],
            payment_method: response.body.payment.type === 'credit' ? response.body.payment.last_4_digits : 'Paypal' ,
            status: status,
            tracking: false
        }
        response.body.items.forEach(items => {
            for (let i = 0; i < items.quantity; i++) {
                totalCostItems += parseFloat(items.price)
                obj.items.push({
                    name: `${items.product_name} - ${items.option_name}`,
                    image: items.image_url,
                    cost: parseFloat(items.price),
                })
            }
        })
        obj.items[0].cost += response.body.total - totalCostItems
        return obj
    } catch (err) {
        console.log(err)
        console.log(order.url)

        return order
    }
};

const getStore = async (link) => {
    try {
        let l = link
        // console.log(l)
        if (l.includes('/carts/')) {
            l = l.replace('/carts/', '/orders/')
        }
        l = ((l.replace('/store/', '/')).replace('api', 'checkout')).split('/')
        l.pop()
        l = l.join('/')
        // console.log(l)
        const response = await got.get(`${l}/a`, {
            responseType: 'text',
            followRedirect: false
        })
        return response.statusCode === 302 ? response.headers.location : false
    } catch (err) {
        console.log(err)
        return 'https://bigcartel.com'
    } 
}