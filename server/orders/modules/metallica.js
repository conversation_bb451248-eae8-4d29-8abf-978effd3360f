const got = require('got')
const  { parse } = require('node-html-parser')
const qs = require('qs')
const fs = require('fs')
const {CookieJar} = require('tough-cookie')
const {promisify} = require('util')
const cookieJar = new CookieJar();
const setCookie = promisify(cookieJar.setCookie.bind(cookieJar));
module.exports = met = async (order) => {
    if (order.url === 'https://www.metallica.com/on/demandware.store/Sites-Metallica-Site/default/Login-LoginForm?scope=') {
        const a = await auth()
        if (a) {
            return await login(order, a)
        } else {
            return order
        }
    } else {
        return await dl(order)
    }
    
}

const auth = async () => {
    try {
        const response = await got.get('https://www.metallica.com/login/?original=%2Faccount%2F', {
            responseType: 'text',
            headers: {
                "User-Agent": "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:93.0) Gecko/******** Firefox/93.0",
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8",
                "Accept-Language": "en-US,en;q=0.5",
                "Referer": "https://www.metallica.com/store/i-disappear-digital-download/9416.html?cgid=store",
                "DNT": "1",
                "Connection": "keep-alive",
                "Upgrade-Insecure-Requests": "1",
                "Sec-Fetch-Dest": "document",
                "Sec-Fetch-Mode": "navigate",
                "Sec-Fetch-Site": "same-origin",
                "Sec-Fetch-User": "?1",
                "Pragma": "no-cache",
                "Cache-Control": "no-cache",
                "TE": "trailers"
            },
            cookieJar: cookieJar,

        })
        const root = parse(response.body)
        // const h = root.querySelector('#dwfrm_ordertrack').innerHTML
        // const ht = parse(h)
        // const c = ht.querySelector('[name="csrf_token"]').attrs.value
        const c = root.querySelectorAll('[name="csrf_token"]')
        // console.log(c.length)
        const d = c[2].attrs.value
        // await console.log(d)
        return d

    } catch (err) {
        console.log(err.message)
        return false
    }
}
const login = async(order, a) => {
    try {
        const response = await got.post(order.url, {
            responseType: 'text',
            headers: {
                'User-Agent': 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:93.0) Gecko/******** Firefox/93.0', 
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8', 
                'Accept-Language': 'en-US,en;q=0.5', 
                'Content-Type': 'application/x-www-form-urlencoded', 
                'Origin': 'https://www.metallica.com', 
                'DNT': '1', 
                'Connection': 'keep-alive', 
                'Referer': 'https://www.metallica.com/login/?original=%2Faccount%2F', 
                // 'Cookie': 'dwac_c26e5001b1c5ffde452d7bcd56=sfZwwjhczr5NJvLtie0hqjQARcfzrhZb-QY%3D|dw-only|||USD|false|US%2FPacific|true; cqcid=abKfybiElmtV72Z7KQsH7HNOMb; cquid=||; sid=sfZwwjhczr5NJvLtie0hqjQARcfzrhZb-QY; dwanonymous_14a9e20de599203e490e731dc5bac197=abKfybiElmtV72Z7KQsH7HNOMb; __cq_dnt=0; dw_dnt=0; dwsid=AQV0G-2mw2_SjQdDdMkjkYTzQR4mVBuKZq274e5fAomisQHCEkU5YudeUk9G9LlEQmezI1yWw5Y4WuQO5ZWnvQ==; dw=1; dw_cookies_accepted=1; __cqact=%5B%7B%22activityType%22%3A%22addToCart%22%2C%22parameters%22%3A%7B%22cookieId%22%3A%22abB8ZxA2H4Has2F1xZ0NTokEQ5%22%2C%22userId%22%3A%22SoEQfPlsIwvM4BURdx6y1NH1xsq9djJo%2FVixbvS9yDs%3D%22%2C%22product%22%3A%7B%22id%22%3A%229416%22%2C%22sku%22%3A%22TC49UXEA%22%2C%22quantity%22%3A1%7D%2C%22realm%22%3A%22BCPJ%22%2C%22siteId%22%3A%22Metallica%22%2C%22instanceType%22%3A%22prd%22%2C%22emailId%22%3A%2209c0034e1b93802bbc2cb18b26a5b2efbf1f9befbb035f8f9beb6e02e95c47b7%22%2C%22loginId%22%3A%2209c0034e1b93802bbc2cb18b26a5b2efbf1f9befbb035f8f9beb6e02e95c47b7%22%2C%22locale%22%3A%22default%22%7D%7D%5D; dwanonymous_14a9e20de599203e490e731dc5bac197=bduadUnTL43G62vP0erpkZbFq0', 
                'Upgrade-Insecure-Requests': '1', 
                'Sec-Fetch-Dest': 'document', 
                'Sec-Fetch-Mode': 'navigate', 
                'Sec-Fetch-Site': 'same-origin', 
                'Sec-Fetch-User': '?1', 
                'Pragma': 'no-cache', 
                'Cache-Control': 'no-cache'
            },
            body: qs.stringify({
                // 'csrf_token': 'sKX6XgSSbKYmZFH1dpuzb3ZYH9f-DSRy_2b21omQb6hKSbmP6UTw5hQOmkgTTq7sG0p_5OUFlvCe-1LZwKT2nHvdXdwpZ2f4cv27XAJlxNGOme5Wwd8szeqkKMTDTyg0Q_a0rW_rjZHk510GIYx9AzgEmrmV4XD4Kzs1nBq-vDlb-vnx0AY=',
                'csrf_token': a,
                'dwfrm_ordertrack_orderNumber': order.order_number,
                'dwfrm_ordertrack_orderEmail': order.email,
                'dwfrm_ordertrack_postalCode': order.zip,
                'dwfrm_ordertrack_findorder': 'Check Status' 
            }),
            cookieJar: cookieJar,
        })
        // await console.log(response.statusCode, order.order_number)
        const root = await parse(response.body)
        // fs.writeFile('./test-site.html', response.body, err => {err && console.log('Error updating profiles')})

        // await console.log(root.querySelector('body').innerText.trim())
        const time1 = root.querySelector('.order-date').innerText.split('Order Placed:')[1].trim().split(' ')
        let time2 = ''
        time1.forEach(time => {
            switch(time1.indexOf(time)) {
                case 0:
                    const months = {'Jan': '01', 'Feb': '02', 'Mar': '03', 'Apr': '04', 'May': '05', 'June': '06', 'Jul': '07', 'Aug': '08', 'Sep': '09', 'Oct': '10', 'Nov': '11', 'Dec': '12'}
                    time2 += `${months[time]}-`
                    break;
                case 1:
                    time2 += parseInt(time.replace(',', '')) > 9 ? `${time.replace(',', '')}T00:00:00+00:00` : `0${time.replace(',', '')}T00:00:00+00:00`
                    break;
                case 2:
                    time2 = `${time}-${time2}`
                    break;
                
            }
        })
        const arr = []
        const items = root.querySelectorAll('.line-item')
        let totalItemsCost = 0
        items.forEach(item => {
            // console.log(item.querySelector('.line-item-price').innerText.split(':')[1].replace('$','').trim())
            const p = parseFloat(item.querySelector('.line-item-price').innerText.split(':')[1].replace('$','').trim())
            totalItemsCost += p
            arr.push({
                name: item.querySelector('.name').innerText.trim(),
                cost: p,
                image: 'https://cdn11.bigcommerce.com/s-fg272t4iw0/images/stencil/1280x1280/products/5432/5685/C-15479__15575.1557817317.jpg',
            })
        })
        const total = parseFloat(root.querySelector('.order-value').innerText.replace('$', '').trim())
        if (total !== totalItemsCost) {
            arr[0].cost += total - totalItemsCost
        }
        //2021-10-03T00:51:17.000+01:00
        let t = false
        if (root.querySelector('.trackingnumber')) {
            t = root.querySelector('.trackingnumber').innerHTML.split('href="')[1].split('"')[0]
        }
        const obj = {
            url: order.url,
            type: 'Metallica',
            store: `https://www.metallica.com`,
            order_date: time2,
            order_number: order.order_number,
            email: order.email,
            items: arr,
            payment_method: root.querySelector('.payment-type').innerText === 'Credit Card' ? 'Credit' : 'Paypal',
            status: root.querySelector('.order-status').innerText.split(':')[1].trim() === 'Shipped' ? 'Shipped' : 'Confirmed',
            tracking: t
        }
        return obj
    } catch (err) {
        console.log(err.message)
        return order
    }
}

const dl = async (order) => {
    try {
        const response = await got.get(order.url, {
            responseType: 'text'
        })
        const root = parse(response.body)
        const time1 = root.querySelector('.order-date').innerText.split('Order Placed:')[1].trim().split(' ')
        let time2 = ''
        time1.forEach(time => {
            switch(time1.indexOf(time)) {
                case 0:
                    const months = {'Jan': '01', 'Feb': '02', 'Mar': '03', 'Apr': '04', 'May': '05', 'June': '06', 'Jul': '07', 'Aug': '08', 'Sep': '09', 'Oct': '10', 'Nov': '11', 'Dec': '12'}
                    time2 += `${months[time]}-`
                    break;
                case 1:
                    time2 += parseInt(time.replace(',', '')) > 9 ? `${time.replace(',', '')}T00:00:00+00:00` : `0${time.replace(',', '')}T00:00:00+00:00`
                    break;
                case 2:
                    time2 = `${time}-${time2}`
                    break;
                
            }
        })
        const arr = []
        const items = root.querySelectorAll('.line-item')
        let totalItemsCost = 0
        items.forEach(item => {
            // console.log(item.querySelector('.line-item-price').innerText.split(':')[1].replace('$','').trim())
            const p = parseFloat(item.querySelector('.line-item-price').innerText.split(':')[1].replace('$','').trim())
            totalItemsCost += p
            arr.push({
                name: item.querySelector('.name').innerText.trim(),
                cost: p,
                image: 'https://cdn11.bigcommerce.com/s-fg272t4iw0/images/stencil/1280x1280/products/5432/5685/C-15479__15575.1557817317.jpg',
            })
        })
        const total = parseFloat(root.querySelector('.order-value').innerText.replace('$', '').trim())
        if (total !== totalItemsCost) {
            arr[0].cost += total - totalItemsCost
        }
        //2021-10-03T00:51:17.000+01:00
        const obj = {
            url: response.url,
            type: 'Metallica',
            store: `https://www.metallica.com`,
            order_date: time2,
            order_number: response.url.split('orderNo=')[1].split('&')[0],
            email: Object.keys(qs.parse(response.url.split('customerEmail=')[1].split('&')[0]))[0],
            items: arr,
            payment_method: root.querySelector('.payment-type').innerText === 'Credit Card' ? 'Credit' : 'Paypal',
            status: root.querySelector('.order-status').innerText.split(':')[1].trim() === 'Shipped' ? 'Shipped' : 'Confirmed',
            tracking: root.querySelector('.trackingnumber').innerHTML.split('href="')[1].split('"')[0]
        }
        return obj
    } catch (err) {
        // console.log(err.response ? err.response.statusCode + ' error' : err)
        console.log(err.message)
        return order
    }
}