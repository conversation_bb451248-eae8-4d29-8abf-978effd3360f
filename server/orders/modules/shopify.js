const got = require('got')
const {CookieJar} = require('tough-cookie')
const {promisify} = require('util')
const {parse} = require('node-html-parser')
const qs = require('qs')
const cookieJar = new CookieJar();
const setCookie = promisify(cookieJar.setCookie.bind(cookieJar));

module.exports = shop = async (order) => {
    // const auth = await getAuth(order)
    // await console.log(auth)
    // await postAuth(order, auth)
    // console.log(order)
    if(order.url.includes('/thank_you')) {
        // console.log(order.url)
        const l = await getOrderLink(order)
        // console.log(l)
        order.url = l
    }
    await postAuth(order)
    return await scrapeOrder(order)
    
    // test(order)
}

const getAuth = async (order) => {
    try {
        const response = await got.get(order.url, {
            responseType: 'text',
            followRedirect: true,
            cookieJar: cookieJar,
        })
        const root= await parse(response.body)
        // return root.querySelector('[name="authenticity_token"]').attrs.value
        return root.querySelector('form').attrs.value
    } catch (err) {
        console.log(err.response ? `${err.response.statusCode} Error getting auth` : err)
    }
};

const getOrderLink = async (order) => {
    try {
        const response = await got.get(order.url, {
            responseType: 'text',
            headers: {
                'User-Agent': 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:93.0) Gecko/20100101 Firefox/93.0', 
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8', 
                'Accept-Language': 'en-US,en;q=0.5', 
                'Content-Type': 'application/x-www-form-urlencoded', 
                'DNT': '1', 
                'Connection': 'keep-alive', 
                'Upgrade-Insecure-Requests': '1', 
                'Sec-Fetch-Dest': 'document', 
                'Sec-Fetch-Mode': 'navigate', 
                'Sec-Fetch-Site': 'same-origin', 
                'Sec-Fetch-User': '?1'
            },
            followRedirect: false
        })
        if (response.statusCode === 302) {
            return response.headers.location
        } else {
            console.log('fail')
            return order
        }
    } catch(err) {
        console.log(err.response ? `${err.response.statusCode} Error getting url` : err)
        return order
    }
}

const postAuth = async (order, auth = undefined) => {
    try {
        const link = `${order.url.split('?')[0]}/authenticate`
        // console.log(link)
        const data = {
            'authenticity_token': auth,
            'email': order.email,
            'order_number': order.order_number,
            'checkout[client_details][browser_width]': '1920',
            'checkout[client_details][browser_height]': '1040',
            'checkout[client_details][javascript_enabled]': '1',
            'checkout[client_details][color_depth]': '24',
            'checkout[client_details][java_enabled]': 'false',
            'checkout[client_details][browser_tz]': '300' 
        }
        // console.log(data)
        const response = await got.post(link, {
            headers: {
                'User-Agent': 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:93.0) Gecko/20100101 Firefox/93.0', 
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8', 
                'Accept-Language': 'en-US,en;q=0.5', 
                // 'Referer': 'https://wickedgatorcomics.com/', 
                'Content-Type': 'application/x-www-form-urlencoded', 
                // 'Origin': 'https://wickedgatorcomics.com', 
                'DNT': '1', 
                // 'Alt-Used': 'wickedgatorcomics.com', 
                'Connection': 'keep-alive', 
                'Upgrade-Insecure-Requests': '1', 
                'Sec-Fetch-Dest': 'document', 
                'Sec-Fetch-Mode': 'navigate', 
                'Sec-Fetch-Site': 'same-origin', 
                'Sec-Fetch-User': '?1'
            },
            body: qs.stringify(data),
            responseType: 'text',
            followRedirect: false,
            cookieJar: cookieJar,
        })
        // await console.log(`${response.statusCode} - ${response.headers.location}`)
    } catch (err) {
        console.log(err.response ? `${err.response.statusCode} Error posting auth` : err)
        return order
    }
};

const scrapeOrder = async (order) => {
    try {
        let newOrder = ''
        const response = await got.get(`${order.url.split('?')[0]}`, {
            headers: {
                'User-Agent': 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:93.0) Gecko/20100101 Firefox/93.0', 
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8', 
                'Accept-Language': 'en-US,en;q=0.5', 
                'Referer': 'https://wickedgatorcomics.com/', 
                'Content-Type': 'application/x-www-form-urlencoded', 
                'Origin': 'https://wickedgatorcomics.com', 
                'DNT': '1', 
                'Alt-Used': 'wickedgatorcomics.com', 
                'Connection': 'keep-alive', 
                'Upgrade-Insecure-Requests': '1', 
                'Sec-Fetch-Dest': 'document', 
                'Sec-Fetch-Mode': 'navigate', 
                'Sec-Fetch-Site': 'same-origin', 
                'Sec-Fetch-User': '?1'
            },
            responseType: 'text',
            followRedirect: true,
            cookieJar: cookieJar,
        })
        const root = parse(response.body)
        const scripts = root.querySelectorAll('script')
        scripts.forEach(script => {
            if (script.innerHTML.includes('Shopify.checkout = {')) {
                const s = script.innerHTML.split('Shopify.checkout = ')[1].split(';')[0]
                const obj = JSON.parse(s)
                // console.log(obj)
                let status = ''
                const statusT = root.querySelector('.os-step__title').innerText
                // console.log(statusT)
                switch (statusT) {
                    case 'Your shipment has been delivered':
                        status = 'Delivered'
                        break;
                    case 'Your order is confirmed':
                        status= 'Confirmed'
                        break;
                    case 'Your order has been canceled':
                        status= 'Canceled'
                        break;
                    default:
                        status = 'Unknown'
                }
                let tracking = root.querySelector(`[data-tracking-number-link="true"]`)
                if (tracking) {
                    tracking = tracking.attrs.href
                    if (status === 'Confirmed') {
                        status = 'Shipped'
                    }
                } else {
                    tracking = false
                }
                let totalProductCost = 0
                const arr = []
                obj.line_items.forEach(items => {
                    for (let i = 0; i < items.quantity; i++) {

                        totalProductCost += parseFloat(items.price)
                        arr.push({
                            name: `${items.title}${items.variant_title !== '' ? ' - ' + items.variant_title : ''}`,
                            image: items.image_url,
                            cost: parseFloat(items.price),
                        })
                    }
                })
                if (parseFloat(obj.payment_due) !== totalProductCost) {
                    arr[0].cost += parseFloat(obj.payment_due) - totalProductCost
                }
                newOrder =  {
                    url: response.url,
                    type: 'Shopify',
                    store: `https://${response.url.split('/')[2]}`,
                    order_date: obj.created_at,
                    order_number: root.querySelector('.os-order-number').innerText.trim().split(' ')[1],
                    email: obj.email,
                    items: arr,
                    payment_method: obj.credit_card.brand ? obj.credit_card.last_digits : 'Paypal', // change to last 4
                    status: status,
                    tracking: tracking
                }

            }
        })
        // console.log(newOrder)
        return newOrder
    } catch (err) {
        console.log(err.response ? `${err.response.statusCode} Error scraping` : err)
        return order
    }
}