const fs = require('fs');
const feathers = require('@feathersjs/feathers');
const express = require('@feathersjs/express');
const socketio = require('@feathersjs/socketio');

// data
let monitorTasksJson = JSON.parse(fs.readFileSync('./data/monitors.json'));
let settingsJson = JSON.parse(fs.readFileSync('./data/settings.json'));
const { authHook, globalHook } = require('./hooks');
const { log } = require('./utils');

const {
	AccountService,
	AccountGenService,
	AuthService,
	CaptchaSolverService,
	CookieService,
	MonitorService,
	MonitorTasksService,
	OrdersService,
	ProfileService,
	ProxyService,
	ProxyRotatorService,
	QuestionSolverService,
	QuicktaskService,
	SettingsService,
	StoreInfoService,
	TaskService,
	StripePaymentAgentService
} = require('./services');

// Startup
const app = express(feathers());

// Parse JSON
app.use(express.json());

//Enable CORS
app.use(function (req, res, next) {
	res.header('Access-Control-Allow-Origin', '*');
	res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
	res.header('Access-Control-Allow-Methods', '*');
	next();
});
// Config Socket.io realtime APIs
app.configure(socketio());

// Enable REST services
app.configure(express.rest());

// Register services
app.use('/auth', new AuthService());
app.use('/proxies', new ProxyService());
app.use('/profiles', new ProfileService());
app.use('/monitor', new MonitorService());
app.use('/monitor-tasks', new MonitorTasksService());
app.use('/settings', new SettingsService());
app.use('/store-info', new StoreInfoService());
app.use('/proxy-rotator', new ProxyRotatorService());
app.use('/tasks', new TaskService());
app.use('/accounts', new AccountService());
app.use('/account-gen', new AccountGenService());
app.use('/questionsolver', new QuestionSolverService());
app.use('/quicktask', new QuicktaskService());
app.use('/captcha', new CaptchaSolverService());
app.use('/cookies', new CookieService());
app.use('/orders', new OrdersService());
app.use('/stripepaymentagent', new StripePaymentAgentService());

// Register hooks
app.hooks(globalHook);
app.service('auth').hooks(authHook);

// New connections connect to stream channel
app.on('connection', (conn) => app.channel('stream').join(conn));

// Publish events to stream
app.publish((data) => app.channel('stream'));
const PORT = process.env.PORT || 3030;
app.listen(PORT).on('listening', () => log(`Stackd server started`));

// On start adds items in setting, profiles, proxies, and tasks to feathers.
const startUp = async () => {
	// Adding settings
	app.service('settings').create({
		webhook: settingsJson.webhook ? settingsJson.webhook : ''
	});

	// // Adding monitor tasks !!! error here that fucks up the json
	Object.values(monitorTasksJson).forEach((task) => {
		app.service('monitor-tasks').create({
			id: task.id,
			platform: task.platform,
			link: task.link,
			pInput: task.pInput,
			proxyList: task.proxyList,
			status: false
		});
	});
	let SHOULD_CHILD_EXIT = false;

	process.on('message', (message) => {
		const { action, data } = message;
		switch (action) {
			case 'CHILD_SHOULD_EXIT':
				SHOULD_CHILD_EXIT = true;
				break;
		}
	});
	process.on('SIGINT', function () {
		if (SHOULD_CHILD_EXIT) process.exit();
	});
};

startUp();
