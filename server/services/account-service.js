// account-service.js

const fs = require('fs'),
	path = require('path'),
	{ log } = require('../utils');

class AccountService {
	constructor() {
		this.accounts = JSON.parse(fs.readFileSync('./data/accounts.json'), 'utf-8');;
	}

	async find() {
		return this.accounts;
	}

	async create(data) {
		// log(data, 'info')
		if (!this.accounts[data.site]) {
			this.accounts[data.site] = {};
		}
		this.accounts[data.site][data.username] = data.password;
		// log(this.accounts, 'info')
		fs.writeFile('./data/accounts.json', JSON.stringify(this.accounts, null, 2), (err) => {
			err && log('Error updating proxies', 'error');
		});
		return this.accounts;
	}

	async patch(id, data) {
		// log(data, 'info')
		if (data.delete === true) {
			// log(data, 'info')
			delete this.accounts[data.site][data.username];
			if (Object.keys(this.accounts[data.site]).length === 0) {
				delete this.accounts[data.site];
			}
		} else {
			if (!this.accounts[data.site]) {
				this.accounts[data.site] = {};
			}
			this.accounts[data.site][data.username] = data.password;
		}
		// log(this.accounts, 'info')
		fs.writeFile('./data/accounts.json', JSON.stringify(this.accounts, null, 2), (err) => {
			err && log('Error updating proxies', 'error');
		});
		return {
			site: data.site,
			accounts: this.accounts,
		};
	}
}

module.exports = AccountService;
