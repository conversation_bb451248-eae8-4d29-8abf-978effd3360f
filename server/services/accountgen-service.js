//accountgen-service.js

const GenMet = require('../gens/met');
const GenGameNerdz = require('../gens/gameNerdz');
const { log } = require('../utils');
const fs = require('fs')

class AccountGenService {
	constructor() {
		this.gen = [];
	}

	async find() {
		return this.gen;
	}

	async create(data) {
		try {
			const proxies = JSON.parse(fs.readFileSync('./data/proxies.json'))[data.proxies];
			const tasks = [];
			if (data.site === 'Met') {
				if (data.usernames) {
					for (let i = 0; i < data.usernames.length; i++) {
						const task = new GenMet(`${data.domain}-${i}`, 'Met', proxies[0], data.domain, data.usernames[i])
						tasks.push(task);
						proxies.push(proxies[0]);
						proxies.shift();
						// const proxy = proxies[0]
						// const test = new Met('testing', 'Met', proxyFormat('45.146.89.60:8123:BOUJEEB:jp3x4p08mjqq'), 'notverified.me')
						// test.flow()
					}
				} else {
					for (let i = 0; i < data.quantity; i++) {
						tasks.push(new GenMet(`${data.domain}-${i}`, 'Met', proxies[0], data.domain));
						proxies.push(proxies[0]);
						proxies.shift();
					}
				}
				// return {
				// 	status: 'started'
				// }
			} else if (data.site === 'GameNerdz') {
				if (data.usernames) {
					for (let i = 0; i < data.usernames.length; i++) {
						tasks.push(new GenGameNerdz(`${data.domain}-${i}`, 'GameNerdz', proxies[0], data.domain, data.usernames[i]));
						proxies.push(proxies[0]);
						proxies.shift();
						// const proxy = proxies[0]
						// const test = new Met('testing', 'Met', proxyFormat('45.146.89.60:8123:BOUJEEB:jp3x4p08mjqq'), 'notverified.me')
						// test.flow()
					}
				} else {
					for (let i = 0; i < data.quantity; i++) {
						tasks.push(new GenGameNerdz(`${data.domain}-${i}`, 'GameNerdz', proxies[0], data.domain));
						proxies.push(proxies[0]);
						proxies.shift();
					}
				}
			} else {
				throw Error(`${data.site} is not a valid platform`)
			}
			await tasks.forEach((task) => {
				task.flow();
			});
			return { status: "started"}
		} catch (error) {
			return { status: 'error', error: error.message }
		}
	}
}

module.exports = AccountGenService;
