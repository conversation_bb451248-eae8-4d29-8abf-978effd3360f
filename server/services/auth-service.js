// auth-service.js

const got = require('got');
const querystring = require('querystring');
const { verifyJwt, log } = require('../utils');

class AuthService {
	constructor() {
		this.token, this.licenseKey, this.username, this.fingerprint;
	}

	async find(params) {
		return {
			licenseKey: this.licenseKey,
			username: this.username,
			token: this.token,
		};
	}

	async create(data) {
		const { username, password, fingerprint, platform, cpucores, name } = data;
		// const fingerprint = await machineId();
		// login
		let r;
		try {
			let url = `https://api.flubbed.io/auth`;
			r = await got.post(url, {
				responseType: 'json',
				headers: {
					'Content-Type': 'application/json',
					Accept: 'application/json',
					Connection: 'keep-alive',
				},
				json: {
					username,
					password,
					fingerprint,
					platform,
					cpucores,
					name,
				},
			});
			return r.body;
		} catch (error) {
			if (error.hasOwnProperty('response')) throw new Error(error.response.body.status);
			else throw new Error(error);
		}
	}

	async update(id, data, params) {
		const { username, authorization, fingerprint } = data;
		// login
		let r;
		try {
			const { mutation } = params.query;
			const qs = querystring.stringify({ ...params.query });
			const url = `https://api.flubbed.io/auth/${id}?${qs}`;
			switch (mutation) {
				case 'VERIFY' || 'RESET_MACHINE':
					r = await got.put(url, {
						responseType: 'json',
						headers: {
							Authorization: authorization,
							'Content-Type': 'application/json',
							Accept: 'application/json',
							Connection: 'keep-alive',
						},
						json: {
							username,
							fingerprint,
						},
					});
					// validate jwt with public certificate
					if (authorization !== undefined && authorization.length > 0) {
						const test = verifyJwt(authorization, {
							issuer: 'Flubbed Auth API',
							subject: username,
							audience: 'https://*.flubbed.io',
						});
						if (!test) throw new Error('Invalid token');
					}
					break;
				default:
					break;
			}
		} catch (error) {
			if (error.hasOwnProperty('response')) throw new Error(error.response.body.status);
			else throw new Error(error);
		}
		this.setAuthentication({ token: authorization, ...this.jwtDecode(authorization) });
		return r.body;
	}

	setAuthentication({ token, username, licenseKey, fingerprint } = options) {
		if (token) this.token = token;
		if (username) this.username = username;
		if (licenseKey) this.licenseKey = licenseKey;
		if (fingerprint) this.fingerprint = fingerprint;
	}

	jwtDecode = (token) => JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
}

module.exports = AuthService;
