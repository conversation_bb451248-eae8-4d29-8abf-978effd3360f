// captchasolver-service.js

class CaptchaSolverService {
	constructor() {
		this.captchas = [];
	}

	async find() {
		return this.captchas
	}

	async create(data) {
		const obj = {
			id: data.id,
			status: 'Started', // Started, Solving, Solved
			data: data.data ? data.data : {}, // data that may be needed
			cookies: data.cookies,
			proxy: data.proxy,
			url: data.url,
			userAgent: data.userAgent,
			type: data.type, // store, Shopify is what's used most
			timestamp: `${new Date().toISOString()}`
		};
		const found = this.captchas.filter(cap => cap.id === data.id)[0]
		if (found) {
			const index = this.captchas.indexOf(found)
			if (index >= 0) {
				this.captchas[index] = obj
			} else {
				this.captchas.push(obj)
			}
		} else {
			this.captchas.push(obj)
		}
		return obj
	}

	async patch(id, data) {
		const obj = {
			id: data.id,
			status: data.status, // Started, Solving, Solved
			data: data.data, // data that may be needed
			cookies: data.cookies,
			proxy: data.proxy,
			url: data.url,
			userAgent: data.userAgent,
			type: data.type, // store, Shopify is what's used most
			timestamp: `${new Date().toISOString()}`
		};
		const found = this.captchas.filter(cap => cap.id === id)[0]
		if (found) {
			const index = this.captchas.indexOf(found)
			if (index >= 0) {
				this.captchas[index] = obj
			} else {
				this.captchas.push(obj)
			}
		} else {
			this.captchas.push(obj)
		}
		return obj;
	};

	async remove(id) {
		if (id) {
			const filtered = this.captchas.filter(c => c.id === id)
			if (filtered.length > 0) {
				filtered.forEach(f => {
					const index = this.captchas.indexOf(f)
					this.captchas.splice(index, 1)
				})
			}
		} else {
			this.captchas = []
		}
		return this.captchas
	}
}

module.exports = CaptchaSolverService