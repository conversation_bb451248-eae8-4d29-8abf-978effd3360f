module.exports.AccountService = require('./account-service');
module.exports.AccountGenService = require('./accountgen-service');
module.exports.AuthService = require('./auth-service');
module.exports.CaptchaSolverService = require('./captchasolver-service');
module.exports.CookieService = require('./cookie-service');
module.exports.MonitorService = require('./monitor-service');
module.exports.MonitorTasksService = require('./monitortasks-service');
module.exports.OrdersService = require('./orders-service');
module.exports.ProfileService = require('./profile-service');
module.exports.ProxyService = require('./proxy-service');
module.exports.ProxyRotatorService = require('./proxyrotator-service');
module.exports.QuestionSolverService = require('./questionsolver-service')
module.exports.QuicktaskService = require('./quicktask-service')
module.exports.SettingsService = require('./settings-service')
module.exports.StoreInfoService = require('./store-info-service')
module.exports.TaskService = require('./task-service')
module.exports.StripePaymentAgentService = require('./stripepaymentagent-service');