// monitor-service.js

const moment = require('moment'),
	{ log } = require('../utils');

class MonitorService {
	constructor() {
		this.monitor = [];
	}

	async find(params) {
		return this.monitor;
	}

	async create(data) {
		const item = {
			id: this.monitor.length,
			platform: data.platform,
			site: data.site,
			type: data.type,
			title: data.title,
			link: data.link,
			price: data.price,
			image: data.image,
			variants: data.variants,
		};
		if (data.cookies) {
			item.cookies = data.cookies;
		}
		item.time = moment().format('kk:mm:ss.SSS');
		this.monitor.push(item);
		return item;
	}

	async remove(id) {
		this.monitor = [];
	}
}

module.exports = MonitorService;
