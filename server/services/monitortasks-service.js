// monitortask-service.js

const fs = require('fs'),
	path = require('path'),
	{ log } = require('../utils');

const monitorShopify = require('../monitors/shopify'),
	monitorChuck = require('../monitors/chuck'),
	monitorBigCartel = require('../monitors/bigCartel');
// const { monitorWoo } = require('../monitors/woo')

// const ACOBigCartel = require('./bigCartel/preload')

class MonitorTasksService {
	constructor() {
		this.tasks = JSON.parse(fs.readFileSync('./data/monitors.json'));
	}

	async find(params) {
		return this.tasks;
	}

	async create(data) {
		// log(this.tasks)
		log('Created a new monitor task');
		let task;
		this.tasks = Object.assign({}, this.tasks);
		if (data.platform === 'Big Cartel') {
			task = new monitorBigCartel(data.id, data.platform, data.link, data.pInput, data.proxyList, 1000, false);
			this.tasks[data.id] = task;
			fs.writeFile('./data/monitors.json', JSON.stringify(this.tasks, null, 2), (err) => {
				err && log('Error updating monitor tasks', 'error');
			});
			return task;
		} else if (data.platform === 'Shopify') {
			task = new monitorShopify(data.id, data.platform, data.link, data.pInput, data.proxyList, 1000, false);
			this.tasks[data.id] = task;
			fs.writeFile('./data/monitors.json', JSON.stringify(this.tasks, null, 2), (err) => {
				if (err) {
					log('Error updating monitor tasks', 'error');
				}
			});
			return task;
		} else if (data.platform === 'Chuck') {
			task = new monitorChuck(data.id, data.platform, data.link, data.pInput, data.proxyList, 1000, false);
			//'testing', 'Chuck', 'https://chucksperry.net/sperry-books-color-x-color-helikon-chthoneon', false, 'pug', 1000, false
			this.tasks[data.id] = task;
			fs.writeFile('./data/monitors.json', JSON.stringify(this.tasks, null, 2), (err) => {
				err && log('Error updating monitor tasks', 'error');
			});
			return task;
		} else {
			log('Sorry that platform is not supported yet', 'info');
		}
	}

	async patch(id, data) {
		this.tasks[data.id].platform = data.platform;
		this.tasks[data.id].link = data.link;
		this.tasks[data.id].pInput = data.pInput;
		this.tasks[data.id].proxyList = data.proxyList;
		this.tasks[data.id].status = data.status;
		this.tasks = Object.assign({}, this.tasks);
		fs.writeFile('./data/monitors.json', JSON.stringify(this.tasks, null, 2), (err) => {
			err && log('Error updating monitor tasks', 'error');
		});
		if (data.status) {
			this.tasks[data.id].flow();
		}
		return this.tasks[data.id];
	}

	async remove(id) {
		this.tasks = Object.assign({}, this.tasks);
		delete this.tasks[id];
		fs.writeFile('./data/monitors.json', JSON.stringify(this.tasks, null, 2), (err) => {
			err && log('Error deleting monitor', 'error');
		});
		return { id: id };
	}
}

module.exports = MonitorTasksService;
