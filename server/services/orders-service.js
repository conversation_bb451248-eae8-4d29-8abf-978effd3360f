// orders-service.js

const fs = require('fs'),
	path = require('path'),
	{ log } = require('../utils');
const orderCheck = require('../orders/index');

class OrdersService {
	constructor() {
		this.orders = JSON.parse(fs.readFileSync('./data/orders.json'));
	}

	async find() {
		return this.orders;
	}

	async create(data) {
		const obj = {
			url: data.url,
			email: data.email,
			order_number: data.order_number,
			type: data.type,
			zip: data.zip,
		};
		// await log(obj)
		this.orders.push(obj);
		fs.writeFile('./data/orders.json', JSON.stringify(this.orders, null, 2), (err) => {
			err && log('Error posting orders', 'error');
		});
		return { data: this.orders };
	}

	async patch(id, data) {
		if (this.orders[id]) {
			this.orders[id].status = data.status;
			// "url": data.url,
			// "type": data.type,
			// "store": data.store,
			// "order_date": data.order_date,
			// "order_number": data.order_number,
			// "email": data.email,
			// "items": data.items,
			// "payment_method": data.payment_method,
			// "status": data.status,
			// "tracking": false
		} else if (id === 'updatedOrders') {
			this.orders = data.data;
		} else if (id === 'updateOrders') {
			const nOrders = await orderCheck(this.orders);
			this.orders = nOrders;
		}
		fs.writeFile('./data/orders.json', JSON.stringify(this.orders, null, 2), (err) => {
			err && log('Error updating orders', 'error');
		});
		return { data: this.orders };
	}
}

module.exports = OrdersService;
