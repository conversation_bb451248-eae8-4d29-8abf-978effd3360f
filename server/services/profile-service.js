// profile-service.js
const fs = require('fs'),
	path = require('path'),
	{ randomStr, log } = require('../utils');

class ProfileService {
	constructor() {
		this.profiles = JSON.parse(fs.readFileSync('./data/profiles.json'));
	}

	async find(params) {
		return this.profiles;
	}

	// Create a group or profile
	async create(data) {
		// if (data.type === 'group') {
		// 	if (!this.data.groups.includes(data.name)) {
		// 		this.data.groups.push(data.name)
		// 	}
		// } else if (data.type === 'profile' && this.data.groups[data.profileGroup]) {
		const pKeys = Object.keys(this.profiles);
		let pid = false;
		while (!pid) {
			const p = `p-${randomStr()}`;
			if (!pKeys.includes(p)) {
				pid = true;
				this.profiles[p] = {
					id: p,
					name: data.name,
					size: '',
					profileGroup: data.profileGroup,
					billingAddress: {
						name: data.billingAddress.name,
						email: data.billingAddress.email,
						phone: data.billingAddress.phone,
						line1: data.billingAddress.line1,
						line2: data.billingAddress.line2,
						line3: '',
						postCode: data.billingAddress.postCode,
						city: data.billingAddress.city,
						country: data.billingAddress.country,
						state: data.billingAddress.state,
					},
					shippingAddress: {
						name: data.shippingAddress.name,
						email: data.shippingAddress.email,
						phone: data.shippingAddress.phone,
						line1: data.shippingAddress.line1,
						line2: data.shippingAddress.line2,
						line3: '',
						postCode: data.shippingAddress.postCode,
						city: data.shippingAddress.city,
						country: data.shippingAddress.country,
						state: data.shippingAddress.state,
					},
					paymentDetails: {
						nameOnCard: data.paymentDetails.nameOnCard,
						cardType: data.paymentDetails.cardType,
						cardNumber: data.paymentDetails.cardNumber,
						cardExpMonth: data.paymentDetails.cardExpMonth,
						cardExpYear: data.paymentDetails.cardExpYear, // change to be `20${data}`
						cardCvv: data.paymentDetails.cardCvv,
					},
					sameBillingAndShippingAddress: data.sameBillingAndShippingAddress,
					onlyCheckoutOnce: false,
					matchNameOnCardAndAddress: false,
				};
			}
		}
		// }
		this.profiles = Object.assign({}, this.profiles);
		fs.writeFile('./data/profiles.json', JSON.stringify(this.profiles, null, 2), (err) => {
			err && log('Error creating profile', 'error');
		});
		return this.profiles;
	}

	async patch(id, data) {
		if (this.profiles[id]) {
			this.profiles[id] = {
				id: id,
				name: data.name,
				size: '',
				profileGroup: data.profileGroup,
				billingAddress: {
					name: data.billingAddress.name,
					email: data.billingAddress.email,
					phone: data.billingAddress.phone,
					line1: data.billingAddress.line1,
					line2: data.billingAddress.line2,
					line3: '',
					postCode: data.billingAddress.postCode,
					city: data.billingAddress.city,
					country: data.billingAddress.country,
					state: data.billingAddress.state,
				},
				shippingAddress: {
					name: data.shippingAddress.name,
					email: data.shippingAddress.email,
					phone: data.shippingAddress.phone,
					line1: data.shippingAddress.line1,
					line2: data.shippingAddress.line2,
					line3: '',
					postCode: data.shippingAddress.postCode,
					city: data.shippingAddress.city,
					country: data.shippingAddress.country,
					state: data.shippingAddress.state,
				},
				paymentDetails: {
					nameOnCard: data.paymentDetails.nameOnCard,
					cardType: data.paymentDetails.cardType,
					cardNumber: data.paymentDetails.cardNumber,
					cardExpMonth: data.paymentDetails.cardExpMonth,
					cardExpYear: data.paymentDetails.cardExpYear, // change to be `20${data}`
					cardCvv: data.paymentDetails.cardCvv,
				},
				sameBillingAndShippingAddress: data.sameBillingAndShippingAddress,
				onlyCheckoutOnce: false,
				matchNameOnCardAndAddress: false,
			};
		}
		this.profiles = Object.assign({}, this.profiles);
		fs.writeFile('./data/profiles.json', JSON.stringify(this.profiles, null, 2), (err) => {
			err && log('Error patching profiles', 'error');
		});
		return this.profiles;
	}

	async remove(id) {
		let group = false;
		if (this.profiles[id]) {
			group = this.profiles[id].profileGroup;
			delete this.profiles[id];
		}
		this.profiles = Object.assign({}, this.profiles);
		fs.writeFile('./data/profiles.json', JSON.stringify(this.profiles, null, 2), (err) => {
			err && log('Error removing profile', 'error');
		});
		return {
			group: group,
			profiles: this.profiles,
		};
	}
}

module.exports = ProfileService;
