// proxy-service.js

const fs = require('fs'),
	path = require('path');

class ProxyService {
	constructor() {
		this.proxies = JSON.parse(fs.readFileSync('./data/proxies.json'));
	}

	async find(params) {
		return this.proxies;
	}

	async create(data) {
		const list = data.proxi;
		this.proxies[data.id] = list;
		return list;
	}

	async patch(id, data) {
		const list = data.proxi;
		this.proxies[data.id] = list;
		fs.writeFile('./data/proxies.json', JSON.stringify(this.proxies, null, 2), (err) => {
			err && log('Error updating proxies', 'error');
		});
		return this.proxies;
	}

	async remove(id) {
		delete this.proxies[id];
		fs.writeFile('./data/proxies.json', JSON.stringify(this.proxies, null, 2), (err) => {
			err && log('Error deleting proxy list', 'error');
		});
		return this.proxies;
	}
}

module.exports = ProxyService;
