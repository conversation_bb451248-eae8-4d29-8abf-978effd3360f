// proxyrotator-service.js

class ProxyRotatorService {
	constructor() {
		this.rotators = {};
	}

	async find() {
		return this.rotators;
	}

	async get(id, params) {
		if (this.rotators[id][params.query.type] !== false) {
			if (params.query.type === 'checkout') {
				const proxy = this.rotators[id].checkout[0];
				this.rotators[id].checkout.push(this.rotators[id].checkout[0]);
				this.rotators[id].checkout.shift();
				return proxy;
			} else if (params.query.type === 'monitor') {
				const proxy = this.rotators[id].monitor[0];
				this.rotators[id].monitor.push(this.rotators[id].monitor[0]);
				this.rotators[id].monitor.shift();
				return proxy;
			} else if (params.query.type === 'paypal') {
				const proxy = this.rotators[id].paypal[0];
				this.rotators[id].paypal.push(this.rotators[id].paypal[0]);
				this.rotators[id].paypal.shift();
				return proxy;
			} else {
				return false;
			}
		} else {
			return false;
		}
	}

	async create(data) {
		// await log(data)
		this.rotators[data.group] = {
			monitor: data.monitor,
			checkout: data.checkout,
			paypal: data.paypal
		};
		// await log(this.proxies.length)
		return this.rotators;
	}

	// async delete() {
	// 	this.proxies = [];
	// 	return this.proxies;
	// }
}

module.exports = ProxyRotatorService;
