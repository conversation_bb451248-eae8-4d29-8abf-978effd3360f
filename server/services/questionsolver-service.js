// questionsolver-service.js

const _remove = require('lodash/remove'),
	{ log } = require('../utils');

class QuestionSolverService {
	constructor() {
		this.questions = [];
	}

	async find() {
		return this.questions;
	}

	async get(id) {
		try {
			if (this.questions.length > 0 && id !== undefined && id.length > 0)
				return this.questions.find((f) => f.taskGroupName === id);
		} catch (err) {
			return {};
		}
	}

	async create(data) {
		if (data.hasOwnProperty('taskGroupName') && data.hasOwnProperty('questions') && data.questions.length > 0) {
			this.questions.push(data);
			// return this.questions.sort((x, y) => x.answered - y.answered);
			return data;
		}
	}

	async patch(id, data, params = undefined) {
		try {
			if (this.questions.length > 0 && id !== undefined && id.length > 0) {
				let result = this.questions.find((f) => f.taskGroupName === id);
				if (result !== undefined) {
					Object.assign(result, data);
					return result;
				}
			}
		} catch (err) {
			return {};
		}
	}

	async remove(ids, params = undefined) {
		try {
			ids = ids.split('~');
			if (ids !== undefined && ids.length > 0) _remove(this.questions, (q) => q.answered);
			return this.questions;
		} catch (err) {
			return [];
		}
	}
}

module.exports = QuestionSolverService;
