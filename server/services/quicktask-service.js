// quicktask-service.js

const fs = require('fs');
const path = require('path');
const got = require('got')

class QuicktaskService {
	constructor() {
		this.quicktasks = {}
	}

    // Required 
    // pInput string: can either be PID or DL
    // Optional
    // link: string: https://mondoshop.com HIGHLY RECOMENDED AND REQUIRED FOR PID
    // platform string: 
    // mode string
	async create(data) {
        try {
            // {
            //     name: 'QT-2022-06-12T14:22:12.424Z',
            //     link: 'https://ncwinters.bigcartel.com',
            //     inputType: 'DL',
            //     pInput: 'https://ncwinters.bigcartel.com/product/lepidoptera-pack-o-stickers',
            //     platform: 'Big Cartel',
            //     mode: 'Preload'
            //   }
            // console.log(data)
            const task = await this.taskBuilder(data)
            // await console.log(task)
            // const task = await this.buildTask(data)
            // return task

            if (task) {

                // Create task
                const response = await got({
                    url: 'http://localhost:3030/tasks',
                    method: 'POST',
                    responseType: 'json',
                    json: task,
                })

                await process.send(task.name);
                return {
                    success: true,
                    task: task
                }
            }
        } catch (error) {
            return { success: false, message: error.message }
        }
	};

    async find (params) {
        try {
            const task = await this.taskBuilder(params.query)
            // await console.log(task)
            if (task) {

                // Create task
                const response = await got({
                    url: 'http://localhost:3030/tasks',
                    method: 'POST',
                    responseType: 'json',
                    json: task,
                })

                await process.send(task.name);
                return {
                    success: true,
                    task: task
                }
            }
        } catch (error) {
            return {
                success: false,
                message: error.message
            }
        }
    }

    
    async taskBuilder (data) {
        try {
            if (data.pInput && typeof data.pInput === 'string') {


                // Retrieve quicktask settings
                const presets = (await got({
                    responseType: 'json',
                    url: 'http://localhost:3030/settings',
                    resolveBodyOnly: true,
                    method: 'GET'

                })).quicktask

                if (presets) {
                    const task = {
                        name: `QT-${new Date().toISOString()}`,
                        randomOos: true,
                        proxyList: presets.proxyList,
                        monitorProxyList: presets.monitorProxyList,
                        profileGroup: presets.profileGroup,
                        delay: presets.delay,
                        quantity: presets.quantity,
                        oInput: presets.oInput
                    }
                    //Validate and format 

                    // pInput
                    if (data.pInput.includes('https://')) {
                        task.pInput = data.pInput.split('?')[0].trim()
                        task.inputType = 'DL'
                    } else {
                        task.pInput = data.pInput.trim()
                        task.inputType = 'PID'
                    }

                    // link
                    if (data.link || task.inputType === "PID") {
                        if (typeof data.link === 'string') {
                            task.link = data.link.split('?')[0]
                        } else {
                            throw new Error('link: invalid type')
                        }
                    } else {
                        task.link = `https://${data.pInput.split('/')[2].split('?')[0]}`
                    }

                    // platform and mode
                    if (typeof data.platform === 'string' && data.mode !== 'false') {
                        task.platform = data.platform
                        if (typeof data.mode === 'string') {
                            task.mode = data.mode
                        } else if (presets.mode) {
                            task.mode = presets.mode
                        }
                    } else {
                        if (task.link.includes('www.bathandbodyworks.com')) {
                            task.platform = 'Bath & Body Works'
                        } else if (task.link.includes('.bandcamp.com')) {
                            task.platform = 'Bandcamp'
                        } else if (task.link.includes('chucksperry.net')) {
                            task.platform = 'Chuck Sperry'
                        } else if (task.link.includes('.bigcartel.com')) {
                            task.platform = 'Big Cartel'
                        } else if (task.link.includes('etsy.com')) {
                            task.platform = 'Etsy'
                        } else if (task.link.includes('gamenerdz.com')) {
                            task.platform = 'GameNerdz'
                        } else if (task.link.includes('.limitedrun.com')) {
                            task.platform = 'Limited Run'
                        } else if (task.link.includes('metallica.com')) {
                            task.platform = 'Metallica'
                        } else if (task.link.includes('.myshopify.com')) {
                            task.platform = 'Shopify'
                        } else if (task.link.includes('.squarespace.com')) {
                            task.platform = 'Squarespace'
                        } else if (task.link.includes('.storeenvy.com')) {
                            task.platform = 'Store Envy'
                        } else {
                            task.platform = presets.platform
                        }

                        if (data.mode === 'string' && data.mode !== 'false') {
                            task.mode = data.mode
                        } else if (presets.mode || data.mode === 'false') {
                            task.mode = presets.mode
                        }
                    }

                    return task



                } else {
                    throw new Error('Quicktask settings empty')
                }

            } else {
                throw new Error('Product input not provided')
            }
        } catch (error) {
            await console.log(`Error building task group: ${error.stack}`)
            return false
        }
    }

    // Builds the task group object before being created
    async buildTask (data) {
        try {
            const valid = []
            await Object.keys(data).forEach(async key => { 
                const result = await this.validator(key, data[key]) 
                valid.push(result)
            })

            if (valid.every(value => value === true)) {
                // Retrieve QT settings
                // const settings = await JSON.parse(await fs.readFileSync('./data/settings.json'))
                const settings = await got({
                    responseType: 'json',
                    url: 'http://localhost:3030/settings',
                    resolveBodyOnly: true,
                    method: 'GET'

                })
                // await console.log(settings)
                const presets = settings.quicktask
                // await console.dir(presets)
                if (
                    presets.hasOwnProperty('platform') && 
                    presets.hasOwnProperty('mode') && 
                    presets.hasOwnProperty('oInput') && 
                    presets.hasOwnProperty('quantity') && 
                    presets.hasOwnProperty('monitorProxyList') && 
                    presets.hasOwnProperty('proxyList') && 
                    presets.hasOwnProperty('profileGroup') && 
                    presets.hasOwnProperty('delay')
                ) {
                    const task = data
                    if (task.mode === false) {
                        task.mode = presets.mode
                    }
                    if (task.platform === false) {
                        task.platform = presets.platform
                    }
                    if (task.pInput.includes('https://')) {
                        task.inputType = 'DL'
                    } else {
                        task.inputType = 'PID'
                    }
                    task.oInput = presets.oInput
                    task.randomOos = true
                    task.quantity = presets.quantity
                    task.monitorProxyList = presets.monitorProxyList
                    task.proxyList = presets.proxyList
                    task.profileGroup = presets.profileGroup
                    task.delay = presets.delay
                    // console.dir(task)

                    // Create task
                    const response = await got({
                        url: 'http://localhost:3030/tasks',
                        method: 'POST',
                        responseType: 'json',
                        json: task,
                    })
                    // await console.dir(response.body)
                    // Start task
                    // await startTasks(task.name)
                    await process.send(task.name);
                    return {
                        success: true
                    }



                } else {
                    return {
                        success: false,
                        message: "Invalid quicktask settings"
                    }
                }

            } else {
                return {
                    success: false,
                    message: "Validation failed"
                }
            }
        } catch (error) {
            await console.log('Error building quicktask:', error.stack)
        }
    }

    async validator (key, value) {
        try {
            let valid = false
            switch (key) {
                case 'name':
                    valid = typeof value === 'string'
                    break;

                case 'platform':
                    if (typeof value === 'string') {
                        if (
                            value === 'Bath & Body Works' ||
                            value === 'Bandcamp' ||
                            value === 'Big Cartel' ||
                            value === 'Chuck Sperry' ||
                            value === 'etsy.com' ||
                            value === 'GameNerdz' ||
                            value === 'Limited Run' ||
                            value === 'Metallica' ||
                            value === 'Shopify' ||
                            value === 'Squarespace' ||
                            value === 'Store Envy'
                        ) {
                            valid = true
                        }
                    } else if (value === false) {
                        valid = true
                    }
                    break;

                case 'mode':
                    valid = typeof value === 'string' || value === false // May change this but I add in dev modes all the time so validating this could be annoying
                    break;

                case 'link':
                    if (typeof value === 'string' && value.includes('https://')) {
                        valid = true
                    }
                    break;

                case 'inputType':
                    if (value === 'DL' || value === 'PID') {
                        valid = true
                    }
                    break;

                case 'pInput':
                    valid = typeof value === 'string' // PID may be added in later
                    break;

                case 'oInput':
                    valid = typeof value === 'string'
                    break;

                case 'randomOos':
                    valid = value === true
                    break;

                case 'quantity':
                    valid = typeof value === 'string'
                    break;

                case 'monitorProxyList':
                    if (typeof value === 'string' || value === false) {
                        valid = true
                    }
                    break;

                case 'proxyList':
                    if (typeof value === 'string' || value === false) {
                        valid = true
                    }
                    break;

                case 'profileGroup':
                    valid = typeof value === 'string'
                    break;

                case 'delay':
                    valid = typeof value === 'number'
                    break;
                default:
                    await console.log(`Unknown key: ${key}`)
            }
            // if (valid === false) {
            //     console.log(key, value)
            // }
            return valid
        } catch (error) {
            await console.log(error.stack)
            return false
        }
    }
}

module.exports = QuicktaskService;
// !TODO
// [x] More server side validation
// [x] GET
// [x] Make a method for all of the other validation so poth CREATE and FIND use the same method
// [ ] process.send should probably have some sort of ID to avoid other listeners from using it