// settings-service.js

const fs = require('fs'),
	path = require('path'),
	{ log } = require('../utils');
const got = require('got');

class SettingsService {
	constructor() {
		this.settings = JSON.parse(fs.readFileSync('./data/settings.json'));
	}

	async find(params) {
		return this.settings;
	}

	async create(data) {
		let key = Object.keys(data)[0];
		switch (key) {
			case 'aycdToken':
				this.settings.aycdToken = data.aycdToken;
				break;
			case 'aycdKey':
				this.settings.aycdKey = data.aycdKey;
				break;
			case 'capmonster':
				this.settings.capmonster = data.capmonster;
				break;
			case 'webhook':
				this.settings.webhook = data.webhook;
				break;
			case 'quicktask':
				this.settings.quicktask = data.quicktask;
				break;
			default:
				break;
		}
		fs.writeFile('./data/settings.json', JSON.stringify(this.settings, null, 2), (err) => {
			err && log('Error updating settings', 'error');
		});
		return this.settings;
	}

	async patch(id, data) {
		switch (id) {
			case 'aycdToken':
				this.settings.aycdToken = data.aycdToken;
				break;
			case 'aycdKey':
				this.settings.aycdKey = data.aycdKey;
				break;
			case 'capmonster':
				this.settings.capmonster = data.capmonster;
				break;
			case 'webhook':
				this.settings.webhook = data.webhook;
				try {
					await got({
						method: 'POST',
						responseType: 'json',
						headers: {
							'Access-Control-Allow-Origin': '*',
							'Content-Type': 'application/json',
							withCredentials: true,
							mode: 'no-cors',
						},
						json: {
							content: null,
							embeds: [
								{
									title: 'Webhook Test Successful',
									color: 1930750,
								},
							],
						},
						url: `${this.settings.webhook}`
					})
				} catch (error) {
					log(JSON.stringify(error.response.body), 'error');
				}
				break;
			case 'quicktask':
				this.settings.quicktask = data.quicktask;
				break;
			default:
				break;
		}
		fs.writeFile('./data/settings.json', JSON.stringify(this.settings, null, 2), (err) => {
			err && log('Error updating settings', 'error');
		});
		return this.settings;
	}
}

module.exports = SettingsService;
