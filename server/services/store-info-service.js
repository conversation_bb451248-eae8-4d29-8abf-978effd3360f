// storeinfo-service.js

const { log } = require('../utils');

const got = require('got');
const BASE_URI_DATA_API = 'https://data-api.flubbed.io/storeinfo';
// const BASE_URI_DATA_API = 'http://localhost:3003/storeinfo';
class StoreInfoService {
	constructor() {
		this.stores, this.store;
	}

	async find(params) {
		this.stores = {};
		try {
			const r = await got.get(`${BASE_URI_DATA_API}`, {
				responseType: 'json',
				headers: params.headers
			});
			this.stores = r.body;
		} catch (error) {
			log(error.message, 'Error');
		}

		return this.stores;
	}

	async get(id, params) {
		this.store;
		try {
			const r = await got.get(`${BASE_URI_DATA_API}/${id}?query=storeName`, {
				responseType: 'json',
				headers: params.headers
			});
			if (r.statusCode === 204) throw new Error('No matching store-info key found');
			this.store = r.body;
		} catch (error) {
			log(error.message, 'error');
			if (error.hasOwnProperty('response') && error.response.hasOwnProperty('body'))
				log(JSON.stringify(error.response.body), 'error');
			this.store = {};
		}
		return this.store;
	}

	async create(data, params) {
		try {
			const { storeName, storeType, storeId, stripeLiveKey, stripeTestKey, paypalMerchantId } = data;
			let storeInfo = {
				storeName,
				storeType,
				storeId,
				stripeLiveKey,
				stripeTestKey,
				paypalMerchantId
			};
			if (!storeName) throw new Error('Invalid store-info body.');
			await got.post(`${BASE_URI_DATA_API}`, {
				responseType: 'json',
				json: storeInfo,
				headers: params.headers
			});
		} catch (error) {
			log(error.message, 'error');
			if (error.hasOwnProperty('response') && error.response.hasOwnProperty('body'))
				log(JSON.stringify(error.response.body), 'error');
		}
	}
}

module.exports = StoreInfoService;
