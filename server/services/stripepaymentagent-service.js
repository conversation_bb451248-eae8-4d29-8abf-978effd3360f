// stripepaymentagent-service.js
const got = require('got');
const { log } = require('../utils');

const BASE_URI_DATA_API = 'https://data-api.flubbed.io/stripepaymentagent';

class StripePaymentAgentService {
	constructor() {}

	async find(params) {
		try {
			const r = await got.get(`${BASE_URI_DATA_API}`, {
				responseType: 'json',
				headers: params.headers
			});
			return r.body;
		} catch (error) {
			log(error.message, 'error');
		}
	}
}

module.exports = StripePaymentAgentService;
