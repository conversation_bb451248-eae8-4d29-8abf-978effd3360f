// task-service.js

const fs = require('fs'),
	path = require('path'),
	{ log } = require('../utils');

class TaskService {
	constructor() {
		this.tasks = JSON.parse(fs.readFileSync('./data/tasks.json'));
	}

	async find() {
		return this.tasks;
	}

	async create(data) {
		const taskGroup = {
			name: data.name,
			platform: data.platform,
			mode: data.mode,
			link: data.link,
			inputType: data.inputType,
			pInput: data.pInput,
			oInput: data.oInput,
			randomOos: data.randomOos,
			quantity: data.quantity,
			monitorProxyList: data.monitorProxyList,
			proxyList: data.proxyList,
			profileGroup: data.profileGroup,
			delay: data.delay,
		};
		this.tasks[data.name] = taskGroup;
		fs.writeFile('./data/tasks.json', JSON.stringify(this.tasks, null, 2), err => {
		    err && log('Error creating tasks', 'error');
		})
		return taskGroup;
	}

	async patch(id, data) {
		const taskGroup = {
			name: data.name,
			platform: data.platform,
			mode: data.mode,
			link: data.link,
			inputType: data.inputType,
			pInput: data.pInput,
			oInput: data.oInput,
			randomOos: data.randomOos,
			quantity: data.quantity,
			monitorProxyList: data.monitorProxyList,
			proxyList: data.proxyList,
			profileGroup: data.profileGroup,
			delay: data.delay,
		};
		this.tasks[data.name] = taskGroup;
		fs.writeFile('./data/tasks.json', JSON.stringify(this.tasks, null, 2), (err) => {
			err && log('Error updating tasks', 'error');
		});
		return this.tasks;
	}

	async remove(id) {
		delete this.tasks[id];
		fs.writeFile('./data/tasks.json', JSON.stringify(this.tasks, null, 2), (err) => {
			err && log('Error deleting task group', 'error');
		});
		return this.tasks;
	}
}

module.exports = TaskService;
