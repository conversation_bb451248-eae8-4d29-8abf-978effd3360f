const { format, createLogger, transports, addColors } = require('winston')
const { timestamp, colorize, combine, printf, errors } = format
const customLevels = require('./custom-levels.json')

function buildProdLogger() {
  let consoleFormat = combine(
    colorize({
      all: true,
    }),
    timestamp({
      format: 'YY-MM-DD HH:mm:ss.SSS',
    }),
    errors({ stack: true }),
    printf(({ level, message, timestamp, stack }) => `${timestamp} ${level}: ${stack || message}`)
  )

  let fileFormat = combine(
		timestamp({
			format: 'YY-MM-DD HH:mm:ss.SSS',
		}),
		errors({ stack: true }),
		printf(({ level, message, timestamp, stack }) => `${timestamp} ${level}: ${stack || message}`)
	);

  // Not used for now
  // let httpFormat = combine(timestamp(), errors({ stack: true }), json());

  addColors(customLevels.colors)

  return createLogger({
    levels: customLevels.levels,
    defaultMeta: { service: 'flub-server' },
    transports: [
      // new transports.Console({ level: 'info', format: consoleFormat }),
      new transports.File({ filename: 'logs/server-error.log', level: 'error', format: fileFormat }),
      new transports.File({ filename: 'logs/server-combined.log', format: fileFormat }),
    ],
  })
}

module.exports = buildProdLogger
