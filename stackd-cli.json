{"pkg": {"targets": ["node16-win-x64"], "assets": ["node_modules/piscina/dist/src/worker.js", "modules/**/*.js", "server/**/*.js", "server/**/*.json"], "outputPath": ".dist"}, "pkg_fetch_version": "5.5.1", "node_version": "16.13.1", "pkg_cache_path": ".dist", "icon": "icon.ico", "name": "Stackd CLI", "description": "Stackd CLI", "company": "Stackd LLC", "version": "0.0.20", "copyright": "Stackd LLC", "file": "index.js"}