const kleur = require('kleur');
// feather client
const feathers = require('@feathersjs/feathers');
const socketio = require('@feathersjs/socketio-client');
const io = require('socket.io-client');
// feather client init
const socket = io('http://localhost:3030');
const app = feathers();
// Set up Socket.io client with the socket
app.configure(socketio(socket));

const { verifyJwt } = require('../cli/security');

module.exports.authenticate = async () => {
    return new Promise(async function (resolve, reject) {
        let auth;
        try {
            auth = await app.service('auth').find();
        } catch (error) {
            console.log(kleur.bold().cyan(`Please login via Stackd extension.`));
        }
        await app.service('auth').on('updated', async (data) => {
            auth = await app.service('auth').find();
            if (auth && auth.hasOwnProperty('token')) {
                const { token, username, fingerprint } = auth;
                const result = verifyJwt(token, {
                    issuer: 'Flubbed Auth API',
                    subject: username,
                    audience: 'https://*.flubbed.io'
                });
                if (!result) reject('CR@CKING');
                resolve(auth);
            }
        });
        if (auth && auth.hasOwnProperty('token')) {
            const { token, username, fingerprint } = auth;
            const result = verifyJwt(token, {
                issuer: 'Flubbed Auth API',
                subject: username,
                audience: 'https://*.flubbed.io'
            });
            if (!result) reject('CR@CKING');
            resolve(auth);
        }
    });
};