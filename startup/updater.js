const { readFileSync, createWriteStream, rename, unlink } = require('fs');
const { basename, resolve } = require('path');
const got = require('got');
const ResEdit = require('resedit');

const DATA_API = 'https://data-api.flubbed.io/version';

module.exports.checkLatestVersion = async () => {
	try {
		const r = await got.get(DATA_API, {
			responseType: 'json',
			headers: {
				Accept: 'application/json',
				Connection: 'keep-alive'
			}
		});
		const { body } = r;
		if (body.hasOwnProperty('StackdVersion') && body.StackdVersion.length > 0) {
			return body.StackdVersion;
		}
	} catch (error) {
		if (error.hasOwnProperty('response')) throw new Error(error.response.body.status);
		else throw new Error(error);
	}
};

module.exports.runUpdater = async () => {
	try {
		const that = this;
		let isDevelopment = process.env.NODE_ENV === 'development';
		if (!isDevelopment) isDevelopment = false;
		const currentPath = process.execPath;
		const currentDir = process.cwd();
		const currentFilename = basename(resolve(currentPath));
		// console.log('currentPath', currentPath)
		const currentExeBlob = readFileSync(currentPath);
		// console.log('currentExeBlob', currentExeBlob)
		const currentExe = ResEdit.NtExecutable.from(currentExeBlob)
		// console.log('currentExe', currentExe)
		const res = ResEdit.NtExecutableResource.from(currentExe)
		// console.log('res', res)
		const viList = ResEdit.Resource.VersionInfo.fromEntries(res.entries)
		const currentVersionStr = viList[0].data.strings[0].values.ProductVersion.split('.')
		const currentVersion = `${currentVersionStr[0]}.${currentVersionStr[1]}.${currentVersionStr[2]}`

		return new Promise(async function (resolve, reject) {
			const latestVersion = await that.checkLatestVersion();
			// if (true) {
			// console.log('isDevelopment', isDevelopment ? 'Development' : 'Not Development');
			// console.log('Newer Version Available', newerVersionAvailable(currentVersion, latestVersion) ? 'Yes' : 'No')
			// console.log(currentVersion, latestVersion)
			if (newerVersionAvailable(currentVersion, latestVersion) && !isDevelopment) {
				console.log(`Newer version ${latestVersion} Available. Downloading Update.`)
				// download new exectutable
				const pathToUpdate = await downloadUpdate(currentDir, currentFilename, latestVersion);
				// delete current running executable

				// rename current running executable
				rename(currentPath, `${currentDir}\\${currentFilename}.old`, () => {
					console.log(`${currentDir}\\${currentFilename}.old`);
				});

				// rename new executable (.update)
				rename(pathToUpdate, `${currentDir}\\${basename(pathToUpdate).replace(/.update/gm, '')}`, async () => {
					console.log(`Renamed update`);
					// end process
					resolve(process.exit(0));
				});
			} else resolve(await deleteOld(currentDir, currentFilename));
		});
	} catch (error) {}
};

const newerVersionAvailable = (currentVersion, latestVersion) => {
	var t = compareVersion(currentVersion, latestVersion);
	return t > 0;
};

const getNextChunk = (version, n, p) => {
	// if pointer is set to end of the string
	// return 0
	if (p > n - 1) return { i: 0, p };

	let pEnd = p;
	while (pEnd < n && version[pEnd] !== '.') {
		pEnd++;
	}
	// retrieve chunk
	i = pEnd !== n - 1 ? parseInt(version.slice(p, pEnd)) : parseInt(version.slice(p, n));
	// find the beginning of next chunk
	p = pEnd + 1;

	return { i, p };
};

const compareVersion = (currentVersion, latestVersion) => {
	let p1 = 0,
		p2 = 0;
	let n1 = currentVersion.length;
	let n2 = latestVersion.length;

	while (p1 < n1 || p2 < n2) {
		let obj1 = getNextChunk(currentVersion, n1, p1);
		p1 = obj1.p;
		let obj2 = getNextChunk(latestVersion, n2, p2);
		p2 = obj2.p;
		if (obj1.i !== obj2.i) return obj2.i > obj1.i ? 1 : -1;
	}
	return 0;
};

const downloadUpdate = async (currentDir, currentFilename, latestVersion) => {
	return new Promise(async function (resolve, reject) {
		// const downloadStream = got.stream(`https://stackd-app-releases.s3.us-east-2.amazonaws.com/stackd-app-${latestVersion}.zip`)
		const downloadStream = got.stream(`https://stackd-app-releases.s3.us-east-2.amazonaws.com/stackd-cli-win.exe`);

		const pathToUpdate = `${currentDir}\\${currentFilename}.update`;
		const fileWriterStream = createWriteStream(pathToUpdate);

		downloadStream
			.on('downloadProgress', ({ transferred, total, percent }) => {
				const percentage = Math.round(percent * 100);
				console.clear()
				console.error(`progress: ${transferred}/${total} (${percentage}%)`);
			})
			.on('error', (error) => {
				console.error(`Download failed: ${error.message}`);
				reject(error);
			});

		fileWriterStream
			.on('error', (error) => {
				console.error(`Could not write file to system: ${error.message}`);
				reject(error);
			})
			.on('finish', () => {
				console.log(`File downloaded to ${`${currentDir}\\${currentFilename}.update`}`);
				resolve(pathToUpdate);
			});

		downloadStream.pipe(fileWriterStream);
	});
};

const deleteOld = async (currentDir, currentFilename) => {
	return new Promise(async function (resolve, reject) {
		unlink(`${currentDir}\\${basename(currentFilename)}.old`, (err) => {
			// if (err) console.log(`ERROR `, err);
			//console.log(`Deleted ${currentDir}\\${basename(currentFilename)}.old`);
			resolve();
		});
	});
};
